from app.config import PYTHON_ENV, PORT, HOST, PYTHON_WORKER
from subprocess import Popen
import sys


def start_server():
    if PYTHON_ENV == "development":
        # Run uvicorn server in development environment
        command = [
            "newrelic-admin",
            "run-program",
            "uvicorn",
            "--host",
            HOST,
            "--port",
            str(PORT),
            "app.main:app",
            "--reload",
        ]
    else:
        # Run gunicorn server in production environment
        command = [
            "newrelic-admin",
            "run-program",
            "gunicorn",
            "-w",
            f"{PYTHON_WORKER}",
            "-k",
            "uvicorn.workers.UvicornWorker",
            "--bind",
            f"0.0.0.0:{PORT}",
            "--access-logfile",
            "-",
            "--error-logfile",
            "-",
            "--access-logformat",
            '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"',
            "app.main:app",
        ]

    # Start the server process
    process = Popen(command)

    try:
        # Wait for the process to complete
        process.wait()
    except KeyboardInterrupt:
        # Handle the interrupt signal
        print("Terminating the server...")
        process.terminate()
        process.wait()
        sys.exit(0)


if __name__ == "__main__":
    start_server()
