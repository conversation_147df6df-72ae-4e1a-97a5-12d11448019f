# Use the official Python 3.10 image as the base image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /var/www/

# Set environment variables to avoid interactive prompts
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    gcc-12 g++-12 \
    wget tar gnupg build-essential \
    libssl-dev zlib1g-dev \
    libyaml-dev

RUN rm -rf /var/lib/apt/lists/*

# Install Node.js 21 from Nodesource
RUN curl -fsSL https://deb.nodesource.com/setup_21.x | bash - && \
    apt-get install -y nodejs


# Download and install OpenJDK 21
RUN wget -O /tmp/openjdk-21.tar.gz https://download.oracle.com/java/21/latest/jdk-21_linux-x64_bin.tar.gz && \
    mkdir -p /usr/lib/jvm/java-21-openjdk && \
    tar -xzf /tmp/openjdk-21.tar.gz -C /usr/lib/jvm/java-21-openjdk --strip-components=1 && \
    rm /tmp/openjdk-21.tar.gz

# Set Java environment variables
ENV JAVA_HOME=/usr/lib/jvm/java-21-openjdk
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# Install Ruby 3.3
RUN curl -fsSL https://cache.ruby-lang.org/pub/ruby/3.3/ruby-3.3.0.tar.gz -o ruby-3.3.0.tar.gz && \
    tar -xzf ruby-3.3.0.tar.gz && \
    cd ruby-3.3.0 && \
    ./configure && make && make install && \
    cd .. && rm -rf ruby-3.3.0 ruby-3.3.0.tar.gz

# --------------------------
# Verify Installations
# --------------------------
RUN java -version && \
    python --version && \
    node -v && \
    gcc --version && \
    ruby --version

    # Copy the requirements file and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Start the FastAPI application
CMD [ "python" , "run.py" ]
