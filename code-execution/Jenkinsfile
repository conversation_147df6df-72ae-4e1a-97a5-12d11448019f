pipeline {
    agent any

    environment {
        DOCKER_CREDENTIALS_ID = 'tlgt-git' // Jenkins Docker credentials ID
        IMAGE_NAME = 'ghcr.io/talentelgia-technologies-pvt-ltd/ats-code-execution'
        K8S_NAMESPACE = 'ats' // Namespace in Kubernetes
    }

    stages {
        stage('Checkout Code') {
            steps {
                // Checkout the code from your Git repository
                git url: 'https://github.com/Talentelgia-Technologies-Pvt-Ltd/ATS.git', credentialsId: 'tlgt-git', branch: 'master'
            }
        }


        stage('Build Image') {
            steps {
                script {
                    // Build Docker image from the backend folder
                    dockerImage = docker.build IMAGE_NAME, 'code-execution'
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                    // Push the Docker image with the build number tag
                    docker.withRegistry('https://ghcr.io', DOCKER_CREDENTIALS_ID) {
                        dockerImage.push("${env.BUILD_NUMBER}")
                    }
                }
            }
        }

        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'ATS-Manifest-Code-Execution', parameters: [string(name: 'DOCKERTAG', value: env.BUILD_NUMBER)]
            }
        }
    }

    // Optionally, you can add post actions here if needed
}
