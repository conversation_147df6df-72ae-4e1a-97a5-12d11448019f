from fastapi import FastAPI, HTTPException, Body
import subprocess
import uuid
import os
import shutil
import resource
import datetime

app = FastAPI(
    title="Secure Code Execution API", docs_url="/api/docs", redoc_url="/api/redocs"
)

SUPPORTED_LANGUAGES = {
    "python": {"ext": "py", "cmd": ["python3", "temp_main.py"]},
    "javascript": {"ext": "js", "cmd": ["node", "temp_main.js"]},
    "ruby": {"ext": "rb", "cmd": ["ruby", "temp_main.rb"]},
    "c": {
        "ext": "c",
        "compile": ["gcc", "temp_main.c", "-o", "temp_main_c"],
        "cmd": ["./temp_main_c"],
    },
    "cpp": {
        "ext": "cpp",
        "compile": ["g++", "temp_main.cpp", "-o", "temp_main_cpp"],
        "cmd": ["./temp_main_cpp"],
    },
    "java": {"ext": "java", "compile": ["javac", "Main.java"], "cmd": ["java", "Main"]},
}


def set_limits():
    """Set resource limits to prevent abuse."""
    resource.setrlimit(resource.RLIMIT_CPU, (2, 2))  # 2 seconds of CPU time
    resource.setrlimit(
        resource.RLIMIT_FSIZE, (1024 * 1024, 1024 * 1024)
    )  # 1 MB file size


@app.post("/execute")
async def execute_code(
    body: dict = Body(
        ...,
        example={
            "language": "python",
            "code": "def add(a, b): return int(a) + int(b)",
            "test_cases": [
                {"input": "add(1, 2)", "expected_output": "3"},
                {"input": "add(5, 7)", "expected_output": "12"},
            ],
        },
    ),
):
    language = body.get("language", "").lower()
    code = body.get("code")
    test_cases = body.get("test_cases", [])

    if language not in SUPPORTED_LANGUAGES:
        raise HTTPException(status_code=400, detail="Unsupported language")
    today = datetime.date.today()
    today_str = today.isoformat()
    unique_id = str(uuid.uuid4())
    code_dir = os.path.join("/tmp", unique_id, language, today_str)
    os.makedirs(code_dir, exist_ok=True)

    results = []
    # Run for each test case
    for test in test_cases:
        input_data = test["input"]
        expected_output = test["expected_output"].strip()

        lang_info = SUPPORTED_LANGUAGES[language]
        file_ext = lang_info["ext"]
        file_name = "Main.java" if language == "java" else f"temp_main.{file_ext}"
        temp_code_file = os.path.join(code_dir, file_name)

        try:

            # Write the code to the temporary file
            with open(temp_code_file, "w") as f:
                if language == "python":
                    f.write(code)
                    f.writelines(f"\nprint({input_data})\n")
                elif language == "javascript":
                    f.write(code)
                    f.writelines(f"\nconsole.log({input_data})\n")
                elif language == "ruby":
                    f.write(code)
                    f.writelines(f"\nputs {input_data}\n")
                elif language == "java":
                    # Correct Java class and method structure for adding numbers
                    f.writelines(f"public class Main {{\n")
                    # Write the code of the method (e.g., add method)
                    f.write(code)
                    f.writelines(f"\n    public static void main(String[] args) {{\n")
                    f.writelines(f"        System.out.println({input_data});\n")
                    f.writelines(f"    }}\n")
                    f.writelines(f"}}\n")
                else:
                    f.write(code)

            # Compile the code if necessary
            if "compile" in lang_info:
                compile_cmd = lang_info["compile"]
                compile_result = subprocess.run(
                    compile_cmd,
                    cwd=code_dir,
                    capture_output=True,
                    text=True,
                    timeout=10,
                )
                if compile_result.returncode != 0:
                    raise RuntimeError(f"Compilation failed: {compile_result.stderr}")

            # Execute the code
            run_cmd = lang_info["cmd"]
            result = subprocess.run(
                run_cmd,
                cwd=code_dir,
                input=(
                    input_data if language in ["python", "javascript", "ruby"] else None
                ),
                capture_output=True,
                text=True,
                timeout=10,
            )

            print(result)

            actual_output = result.stdout.strip()
            error_output = result.stderr.strip()

            results.append(
                {
                    "input": input_data,
                    "expected_output": expected_output,
                    "actual_output": actual_output if actual_output else error_output,
                    "status": (
                        "Passed" if actual_output == expected_output else "Failed"
                    ),
                }
            )
        except subprocess.TimeoutExpired:
            results.append(
                {
                    "input": input_data,
                    "expected_output": expected_output,
                    "actual_output": "Execution timed out",
                    "status": "Failed",
                }
            )
        except Exception as e:
            results.append(
                {
                    "input": input_data,
                    "expected_output": expected_output,
                    "actual_output": str(e),
                    "status": "Error",
                }
            )

    shutil.rmtree(code_dir)
    return {"test_case_results": results}
