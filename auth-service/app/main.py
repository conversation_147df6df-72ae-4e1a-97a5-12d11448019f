from fastapi import FastAPI
from app.providers.google_auth_provider import router as google_router
from starlette.middleware.sessions import SessionMiddleware
from app.config import SECRET_KEY  # You must have a secure secret key

app = FastAPI()
app.add_middleware(SessionMiddleware, secret_key=SECRET_KEY)

# Mount provider routes
app.include_router(google_router)


# Add this line to enable session support
@app.get("/")
def root():
    return {"message": "Central OAuth Server is Running"}
