"""
File conatins database connection URL
"""

from dotenv import load_dotenv
import os

load_dotenv()

PYTHON_ENV = os.environ.get("PYTHON_ENV", "development")
HOST = os.environ.get("HOST", "127.0.0.1")
PORT = int(os.environ.get("PORT", "4000"))
PYTHON_WORKER = os.environ.get("PYTHON_WORKER", "1")


GOOGLE_CLIENT_ID = os.environ.get("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.environ.get("GOOGLE_CLIENT_SECRET")
BASE_AUTH_DOMAIN = os.environ.get("BASE_AUTH_DOMAIN")
SECRET_KEY = os.environ.get("SECRET_KEY", "secret-key")
