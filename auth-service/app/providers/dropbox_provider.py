from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse
from authlib.integrations.starlette_client import OAuth
from app.config import DROPBOX_CLIENT_ID, DROPBOX_CLIENT_SECRET, BASE_AUTH_DOMAIN

router = APIRouter(prefix="/dropbox")

oauth = OAuth()
oauth.register(
    name="dropbox",
    client_id=DROPBOX_CLIENT_ID,
    client_secret=DROPBOX_CLIENT_SECRET,
    access_token_url="https://api.dropbox.com/oauth2/token",
    authorize_url="https://www.dropbox.com/oauth2/authorize",
    client_kwargs={"scope": ""},  # Dropbox scopes optional, "" for basic access
)


@router.get("/login")
async def login(request: Request, callback: str):
    request.session["callback"] = callback
    redirect_uri = f"{BASE_AUTH_DOMAIN}/dropbox/auth/callback"
    return await oauth.dropbox.authorize_redirect(request, redirect_uri)


@router.get("/auth/callback")
async def auth(request: Request):
    token = await oauth.dropbox.authorize_access_token(request)
    print("token", token)
    callback_url = request.session.get("callback")

    if not callback_url:
        return {"error": "Missing callback"}

    return RedirectResponse(
        url=f"{callback_url}?token={token['access_token']}&expires_at={token.get('expires_at', 0)}"
    )
