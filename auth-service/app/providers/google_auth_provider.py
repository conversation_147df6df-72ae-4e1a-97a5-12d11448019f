from fastapi import APIRouter, Request
from fastapi.responses import RedirectResponse
from authlib.integrations.starlette_client import OAuth
from app.config import GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, BASE_AUTH_DOMAIN

router = APIRouter(prefix="/google")

oauth = OAuth()
oauth.register(
    name="google",
    client_id=GOOGLE_CLIENT_ID,
    client_secret=GOOGLE_CLIENT_SECRET,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={
        "scope": "openid email profile https://www.googleapis.com/auth/drive.readonly",
    },
)


@router.get("/login")
async def login(request: Request, callback: str):
    request.session["callback"] = callback
    redirect_uri = f"{BASE_AUTH_DOMAIN}/google/auth/callback"
    return await oauth.google.authorize_redirect(request, redirect_uri)


@router.get("/auth/callback")
async def auth(request: Request):
    token = await oauth.google.authorize_access_token(request)
    print("token", token)
    callback_url = request.session.get("callback")

    if not callback_url:
        return {"error": "Missing callback"}

    return RedirectResponse(
        url=f"{callback_url}?token={token['access_token']}&expires_at={token['expires_at']}"
    )
