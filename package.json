{"name": "ats", "version": "1.0.0", "description": "", "main": "index.js", "engines": {"node": ">=18", "pnpm": "9.0.6"}, "scripts": {"format": "prettier . --write && pnpm run --filter \"@ats/backend\" format", "lint": "pnpm -r --filter !\"@ats/backend\" lint", "pre-commit": "pnpm format", "dev": "docker compose -f docker-compose.dev.yml up -d", "start": "docker compose -f docker-compose.yml up -d", "local": "run-p local:*", "local:backend": "cd backend && PYTHON_ENV=development python run.py", "local:app": "PORT=3081 pnpm run --filter \"@ats/app\" dev", "staging": "run-p staging:*", "staging:backend": "cd backend && PYTHON_ENV=staging python run.py", "staging:app": "pnpm run --filter \"@ats/app\" build && PORT=3081  pnpm run --filter \"@ats/app\" start", "project:setup": "pnpm project:setup:app && pnpm project:setup:backend", "project:setup:backend": "cp-cli backend/.env.example backend/.env", "project:setup:app": "cp-cli app/.env.example app/.env"}, "devDependencies": {"cp-cli": "^2.0.0", "eslint": "8.56.0", "eslint-config-next": "^14.2.3", "eslint-plugin-unused-imports": "^4.1.4", "npm-run-all": "^4.1.5", "prettier": "^3.1.1"}, "keywords": [], "author": "", "license": "ISC"}