from app.config import (
    PYTHON_ENV,
    PORT,
    HOST,
    PYTHON_WORKER,
    CELERY_FLOWER_USER,
    CELERY_FLOWER_PASSWORD,
)
from subprocess import Popen
import sys
import argparse


def start_server():
    if PYTHON_ENV == "development":
        # Run uvicorn server in development environment
        command = [
            "newrelic-admin",
            "run-program",
            "uvicorn",
            "--host",
            HOST,
            "--port",
            str(PORT),
            "app.main:app",
            "--reload",
        ]
    else:
        # Run gunicorn server in production environment
        command = [
            "newrelic-admin",
            "run-program",
            "gunicorn",
            "-w",
            f"{PYTHON_WORKER}",
            "-k",
            "uvicorn.workers.UvicornWorker",
            "--bind",
            f"0.0.0.0:{PORT}",
            "--access-logfile",
            "-",
            "--error-logfile",
            "-",
            "--access-logformat",
            '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"',
            "app.main:app",
        ]

    # Start the server process
    process = Popen(command)

    try:
        # Wait for the process to complete
        process.wait()
    except KeyboardInterrupt:
        # Handle the interrupt signal
        print("Terminating the server...")
        process.terminate()
        process.wait()
        sys.exit(0)


def start_celery():
    # Command to start the Celery worker
    worker_cmd = [
        "celery",
        "-A",
        "app.celery_app",
        "worker",
        "--loglevel=INFO",
        f"--concurrency={PYTHON_WORKER}",
    ]
    flower_cmd = [
        "celery",
        "-A",
        "app.celery_app",
        "flower",
        "--port=5555",
        "--host=0.0.0.0",
        f"--basic_auth={CELERY_FLOWER_USER}:{CELERY_FLOWER_PASSWORD}",
    ]

    # Start the Celery worker process
    worker_process = Popen(worker_cmd)
    flower_process = Popen(flower_cmd)
    try:
        print("Celery worker and Flower dashboard started.")
        # Wait for the process to complete
        flower_process.wait()
        worker_process.wait()
    except KeyboardInterrupt:
        # Handle the interrupt signal
        print("Terminating the elery worker and Flower dashboard...")
        worker_process.terminate()
        flower_process.terminate()
        worker_process.wait()
        flower_process.wait()
        sys.exit(0)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Manage server and Celery worker.")
    parser.add_argument(
        "command",
        choices=["server", "celery"],
        help="Command to run: 'server' to start the FastAPI server, 'celery' to start the Celery worker.",
    )
    args = parser.parse_args()

    if args.command == "server":
        start_server()
    elif args.command == "celery":
        start_celery()
    else:
        print(
            "Invalid command. Use 'server' to start the FastAPI server or 'celery' to start the Celery worker."
        )
        sys.exit(1)
