# Use the official Python 3.10 slim image
FROM python:3.10-slim

# Arguments to allow user ID and group ID customization (to match host user)
ARG USER_ID=1000
ARG GROUP_ID=1000

# Install necessary packages
RUN apt-get update && apt-get install -y \
    ffmpeg \
    wkhtmltopdf \
    curl \
    gcc \
    g++ \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create user with matching UID/GID to avoid permission issues
RUN addgroup --gid $GROUP_ID appgroup && \
    adduser --disabled-password --gecos "" --uid $USER_ID --gid $GROUP_ID ubuntu

# Set the working directory
WORKDIR /var/www/

# Set user ownership for mounted volumes (uploads/logs)
RUN mkdir -p /var/www/uploads /var/www/logs && \
    chown -R ubuntu:appgroup /var/www

# Copy requirements file first to leverage Docker caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN python -m spacy download en_core_web_lg

# Copy the rest of the application
COPY . .

ENV PYTHON_ENV=production
ENV HOST=0.0.0.0

# Switch to non-root user
USER ubuntu

# Default command
CMD ["python", "run.py", "server"]
