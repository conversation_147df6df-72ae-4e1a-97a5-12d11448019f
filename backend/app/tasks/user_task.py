from app.celery_app import celery_app
from app.tasks.base_task import BaseTask  # Import the BaseTask class
from app.models import User
from app.mailer import UserMailer


class UserTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def reset_password_email(cls, user_id: int):
        user = User.get_by_id(user_id)
        UserMailer.reset_password_email(user=user)

    @celery_app.task(bind=True, queue="high_priority")
    def user_verify_email(cls, user_id: int):
        user = User.get_by_id(user_id)
        UserMailer.user_verify_email(user=user)

    @celery_app.task(bind=True, queue="high_priority")
    def resend_otp_email(cls, user_id: int, resend_text: str):
        user = User.get_by_id(user_id)
        UserMailer.resend_otp_email(user=user, resend_text=resend_text)
