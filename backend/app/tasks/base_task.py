from celery import Task
from celery.signals import task_received, task_success, task_failure


# Define a BaseTask class that extends celery.Task
class BaseTask(Task):
    abstract = True  # Prevents instantiating this base class directly

    def on_success(self, retval, task_id, args, kwargs):
        """Called when the task is successfully completed"""
        print(f"Task {task_id} succeeded with result: {retval}")

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called when the task fails"""
        print(f"Task {task_id} failed with error: {exc}")
        print(f"Error Info: {einfo}")

    def after_return(self, status, retval, task_id, args, kwargs, einfo):
        """Called after a task has returned or failed"""
        print(f"Task {task_id} returned status {status}")
