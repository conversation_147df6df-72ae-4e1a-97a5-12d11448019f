from app.celery_app import celery_app
from app.tasks.base_task import BaseTask  # Import the BaseTask class
from app.mailer import EmployeeMailer


class EmployeeTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def reset_password_email(cls, employee_id: int):
        from app.models.employee import Employee

        employee = Employee.get_by_id(employee_id)
        EmployeeMailer.reset_password_email(employee=employee)

    @celery_app.task(bind=True, queue="high_priority")
    def resend_otp_email(cls, employee_id: int, resend_text: str):
        from app.models.employee import Employee

        employee = Employee.get_by_id(employee_id)
        EmployeeMailer.resend_otp_email(employee=employee, resend_text=resend_text)

    @celery_app.task(bind=True, queue="high_priority")
    def new_employee_email(cls, employee_id: int, password: str):
        from app.models.employee import Employee

        employee = Employee.get_by_id(employee_id)
        EmployeeMailer.new_employee_email(employee=employee, password=password)

    @celery_app.task(bind=True, queue="emails")
    def interview_scheduled_mail(cls, employee_id: int, schedule_interview_id: int):
        """Send email about the scheduled interview."""
        from app.models.employee import Employee
        from app.models.schedule_interview import ScheduleInterview

        employee = Employee.get_by_id(employee_id)
        schedule_interview = ScheduleInterview.get_by_id(schedule_interview_id)

        print("start EmployeeMailer.interview_scheduled_mail: schedule_interview - ")
        print(f"employee_id: {employee_id} - {employee}")
        print(f"schedule_interview: {schedule_interview_id} - {schedule_interview}")

        EmployeeMailer.interview_scheduled_mail(
            employee=employee, schedule_interview=schedule_interview
        )

    @celery_app.task(bind=True, queue="emails")
    def interview_reminder_mail(cls, employee_id: int, schedule_interview_id: int):
        """Send email about the scheduled interview."""
        from app.models.employee import Employee
        from app.models.schedule_interview import ScheduleInterview

        employee = Employee.get_by_id(employee_id)
        schedule_interview = ScheduleInterview.get_or_none(
            ScheduleInterview.id == schedule_interview_id,
            ScheduleInterview.interviewer_id == employee_id,
        )

        print("start EmployeeMailer.interview_reminder_mail: schedule_interview - ")
        print(f"employee_id: {employee_id} - {employee}")
        print(f"schedule_interview: {schedule_interview_id} - {schedule_interview}")

        EmployeeMailer.interview_reminder_mail(
            employee=employee, schedule_interview=schedule_interview
        )
