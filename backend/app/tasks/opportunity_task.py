from app.celery_app import celery_app
from app.tasks.base_task import BaseTask
from app.helper.job_helper import JobHelper
import json
import asyncio


class OpportunityTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def extract_opportunity_details(
        cls, opportunity_id: int, business_id: int, employee_id: int, request_data: str
    ):
        """
        Extracts opportunity details and sends an email notification.
        :param opportunity: Opportunity ID
        :param business_id: Business ID
        :param employee_id: Employee ID
        :param request_data: JSON string containing request data
        """
        request_data = json.loads(request_data)
        asyncio.run(
            JobHelper.extract_questions(
                opportunity_id=opportunity_id,
                business_id=business_id,
                employee_id=employee_id,
                request_data=request_data,
            )
        )
        return f"Opportunity details extracted for opportunity ID: {opportunity_id}, business ID: {business_id}, employee ID: {employee_id}"

    @celery_app.task(bind=True, queue="high_priority")
    def extract_opportunity_score_for_candidates(
        cls, opportunity_id: int, business_id: int
    ):
        """
        Extracts opportunity score with candidates.
        :param opportunity_id: Opportunity ID
        :param business_id: Business ID
        """
        JobHelper.extract_opportunity_score_for_candidates(
            opportunity_id=opportunity_id, business_id=business_id
        )
        return f"Opportunity score extracted for opportunity ID: {opportunity_id}, business ID: {business_id}"
