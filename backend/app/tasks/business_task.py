from app.celery_app import celery_app
from app.tasks.base_task import BaseTask
from app.models import Business
from app.mailer import BusinessMailer


class BusinessTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def business_verify_email(cls, business_id: int):
        """Send business verification email."""
        business = Business.get_by_id(business_id)
        BusinessMailer.business_verify_email(business=business)

    @celery_app.task(bind=True, queue="high_priority")
    def resend_business_otp_email(cls, business_id: int, resend_text: str):
        """Resend OTP to business."""
        business = Business.get_by_id(business_id)
        BusinessMailer.resend_business_otp_email(
            business=business, resend_text=resend_text
        )
