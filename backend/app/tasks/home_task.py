from app.celery_app import celery_app
from app.tasks.base_task import BaseTask
from app.models import ContactEnquery
from app.mailer import HomeMailer


class HomeTask(BaseTask):
    @celery_app.task(bind=True, queue="high_priority")
    def contact_us_email(cls, contact_enquery_id: int):
        """Send the 'Contact Us' email asynchronously."""
        contact_enquery = ContactEnquery.get_by_id(contact_enquery_id)
        HomeMailer.contact_us_email(contact_enquery=contact_enquery)
