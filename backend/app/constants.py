class ConstantMessages:
    SOMETHING_WENT_WRONG = "Something went wrong. Please try again later."
    SERVER_ERROR = "Server Error: An unexpected issue occurred."
    INTERNAL_SERVER_ERROR = (
        "Internal Server Error: We encountered a problem on our end."
    )
    PERMISSION_DENIED = "You don't have permission to access this resource."
    FETCHED_SUCCESSFULLY = "Data fetched successfully"
    NODE_REQUIRED = "Node must exist!"
    EMPLOYEE_ROLE_REQUIRED = "Employee role is required!"
    INVALID_EMPLOYEE_ROLE = "Employee role is invalid!"
    PERMISSION_UPDATED = "Permissions updated successfully"
    AUTH_FAILED = "Authorization Failed."
    SESSION_EXPIRED = "ession Expired."
    USER_DOES_NOT_EXIST = "User does not exist"
    CANDIDATE_DOES_NOT_EXIST = "Candidate does not exist"
    RECORD_PERMISSION_DENIED = "You don't have permission to access this record."


class Constant:
    SUPER_ADMIN = "Super Admin"


class NodeName:
    ADMIN_BUSINESS = "admin_business"
    ADMIN_DASHBOARD = "admin_dashboard"

    EMPLOYEE_CANDIDATES = "employee_candidates"
    EMPLOYEE_DASHBOARD = "employee_dashboard"
    EMPLOYEE_DEPARTMENTS = "employee_departments"
    EMPLOYEE_EMPLOYEES = "employee_employees"
    EMPLOYEE_INTERVIEWS = "employee_interviews"
    EMPLOYEE_JOB_REQUESTS = "employee_job_requests"
    EMPLOYEE_LOCATIONS = "employee_locations"
    EMPLOYEE_OPPORTUNITIES = "employee_opportunities"
    EMPLOYEE_SETTINGS = "employee_settings"
    EMPLOYEE_ANALYTICS_MANAGEMENT = "employee_analytics_management"


class PermissionName:
    READ = "read"
    WRITE = "write"
    EDIT = "edit"
    DELETE = "delete"
    READ_ONLY = ["read"]
    READ_WRITE = ["read", "write"]
    READ_EDIT = ["read", "edit"]
    READ_DELETE = ["read", "delete"]
