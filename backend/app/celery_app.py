# celery_app.py
from celery import Celery, signals
from kombu import Queue
from app.config.environment_variables import REDIS_URL
import logging
from database.connection import db

# Initialize the Celery app
celery_app = Celery("backend", broker=REDIS_URL)

# Configure Celery to use multiple queues
celery_app.conf.task_queues = (
    Queue("default"),
    Queue("high_priority"),
    Queue("low_priority"),
    Queue("emails"),
    Queue("gpt_extraction"),
)

# Set default queue
celery_app.conf.task_default_queue = "default"
celery_app.conf.imports = ["app.tasks"]
celery_app.conf.worker_hijack_root_logger = False


# Task START
@signals.task_prerun.connect
def on_task_start(task_id, task, *args, **kwargs):
    if db.is_closed():
        logging.info(f"🔄 DB Connected")
        db.connect(reuse_if_open=True)
    logging.info(f"🔄 Task STARTED: {task.name} [ID={task_id}]")


# Task SUCCESS
@signals.task_success.connect
def on_task_success(result, task_id, task, *args, **kwargs):
    logging.info(f"✅ Task SUCCEEDED: {task.name} [ID={task_id}] Result: {result}")


# Task FAILURE
@signals.task_failure.connect
def on_task_failure(task_id, exception, args, kwargs, traceback, einfo, task, **_):
    logging.error(f"❌ Task FAILED: {task.name} [ID={task_id}] Error: {exception}")


# Task RETRY
@signals.task_retry.connect
def on_task_retry(request, reason, einfo, *args, **kwargs):
    logging.warning(
        f"🔁 Task RETRYING: {request.task} [ID={request.id}] Reason: {reason}"
    )


# Task REVOKED
@signals.task_revoked.connect
def on_task_revoked(request, terminated, signum, expired, *args, **kwargs):
    logging.warning(
        f"⛔ Task REVOKED: {request.task} [ID={request.id}] Terminated: {terminated}, Signal: {signum}, Expired: {expired}"
    )


# Task COMPLETE (after success/failure)
@signals.task_postrun.connect
def on_task_complete(task_id, task, retval, state, *args, **kwargs):
    if not db.is_closed():
        logging.info(f"🔄 DB Connection Closed")
        db.close()
    logging.info(f"🏁 Task COMPLETED: {task.name} [ID={task_id}] State: {state}")
