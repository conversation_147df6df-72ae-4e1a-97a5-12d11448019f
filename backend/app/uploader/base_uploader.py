import os
import io
from werkzeug.utils import secure_filename
from botocore.exceptions import NoCredentialsError
from app.config import S3<PERSON><PERSON>, UPLOAD_TO_S3, PYTHON_ENV, S3_BUCKET_NAME, API_URL
from typing import Optional
from fastapi import UploadFile
from app.utils import generate_unique_key
import mimetypes


class BaseUploader:
    _upload_folder_base_path = f"uploads/{PYTHON_ENV}"
    UPLOAD_FOLDER = ""
    ALLOWED_EXTENSIONS = set()
    RANDOM_NAME = False

    def __init__(
        self,
        file: UploadFile,
        filename: Optional[str] = None,
        upload_path: Optional[str] = None,
    ):
        #
        def build_secure_filename(filename):
            new_name = ""
            if self.RANDOM_NAME:
                new_name = generate_unique_key(5) + "_"
            new_name = new_name + os.path.splitext(filename)[0] + self.extension
            return secure_filename(new_name)

        self.file = file
        self.content_type = file.content_type
        self.upload_path = str(upload_path)
        if file.content_type == "audio/wav":
            self.extension = ".wav"
        else:
            self.extension = mimetypes.guess_extension(file.content_type)
        self.filename = build_secure_filename(filename or file.filename)
        self.response = None

    @classmethod
    def _allowed_file(cls, filename):
        return (
            "." in filename
            and filename.rsplit(".", 1)[-1].lower() in cls.ALLOWED_EXTENSIONS
        )

    def valid(self, exception=False):
        if not self.file:
            if exception:
                raise ValueError("File is required")
            return False
        if not self._allowed_file(self.extension):
            if exception:
                raise ValueError("Invalid file format")
            return False
        return True

    async def upload(self):
        self.valid()
        file_path = os.path.join(
            self._upload_folder_base_path,
            self.UPLOAD_FOLDER,
            self.upload_path,
            self.filename,
        )
        directory_path = os.path.dirname(file_path)
        binary_file = await self.file.read()
        if UPLOAD_TO_S3:
            try:
                await self.file.seek(0)  # Make sure the file pointer is at the start
                # Read the file content asynchronously
                s3_client = S3Client()
                s3_client.client.upload_fileobj(
                    io.BytesIO(binary_file), S3_BUCKET_NAME, file_path
                )
                url = s3_client.build_s3_presigned_url(file_path)
                self.response = {
                    "url": url,
                    "file_path": file_path,
                    "extension": self.extension,
                    "on_cloud": True,
                }
                return self.response
            except NoCredentialsError:
                raise RuntimeError("Credentials not available")
            except Exception as e:
                raise RuntimeError(f"Error uploading to S3: {str(e)}")
        else:
            if not os.path.exists(directory_path):
                os.makedirs(directory_path, exist_ok=True)
            try:
                with open(file_path, "wb") as f:
                    f.write(binary_file)
                self.response = {
                    "url": f"{API_URL}/{file_path}",
                    "file_path": file_path,
                    "extension": self.extension,
                    "on_cloud": False,
                }
                return self.response
            except Exception as e:
                raise RuntimeError(f"Error uploading locally: {str(e)}")
