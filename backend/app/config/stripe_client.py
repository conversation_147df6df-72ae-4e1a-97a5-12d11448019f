from app.config.environment_variables import STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET
import stripe
from stripe import PaymentIntent
from typing import Optional
from fastapi import HTTPException
from typing_extensions import Unpack


class StripeClient:
    # Initialize Stripe with the secret key
    @classmethod
    def initialize(cls):
        stripe.api_key = STRIPE_SECRET_KEY

    @classmethod
    def active_plans(cls):
        try:
            stripe.api_key = STRIPE_SECRET_KEY
            prices = stripe.Price.list(active=True)
            plans = []
            for price in prices.data:
                product = stripe.Product.retrieve(price.product)
                plans.append(
                    {
                        "id": price.id,
                        "amount": price.unit_amount / 100,
                        "currency": price.currency,
                        "interval": price.recurring["interval"],
                        "product_name": product.name,
                        "description": product.description,
                    }
                )
            return plans
        except Exception as e:
            raise Exception(f"Stripe error: {str(e)}")

    # Create a PaymentIntent
    @classmethod
    def create_payment_intent(
        cls, **params: Unpack["PaymentIntent.CreateParams"]
    ) -> PaymentIntent:
        try:
            stripe.api_key = STRIPE_SECRET_KEY
            payment_intent = stripe.PaymentIntent.create(**params)
            return payment_intent
        except Exception as e:
            raise Exception(f"Stripe error: {str(e)}")

    # Create a PaymentIntent
    @classmethod
    def create_checkout_session(
        cls, amount: int, domain: str, metadata: Optional[dict] = None
    ):
        try:
            stripe.api_key = STRIPE_SECRET_KEY
            # Convert amount to cents
            amount_in_cents = int(amount * 100)

            # Create a new Stripe Checkout session
            session = stripe.checkout.Session.create(
                payment_method_types=["card"],
                line_items=[
                    {
                        "price_data": {
                            "currency": "usd",
                            "product_data": {
                                "name": "Test Product",
                            },
                            "unit_amount": amount_in_cents,
                        },
                        "quantity": 1,
                    },
                ],
                mode="payment",
                metadata=metadata,
                success_url=f"{domain}/success",
                cancel_url=f"{domain}/cancelled",
            )

            # Return the session URL to the frontend
            return {"url": session.url}
        except Exception as e:
            raise Exception(f"Stripe error: {str(e)}")

    @classmethod
    def create_subscription(
        cls,
        customer_email: str,
        customer_name: str,
        plan_id: str,
        payment_method_id: str,
        metadata: Optional[dict] = None,
    ):
        try:
            stripe.api_key = STRIPE_SECRET_KEY

            customers = stripe.Customer.list(email=customer_email)
            customer = None

            # If a customer is found, use it; otherwise, create a new customer
            if customers["data"]:
                customer = customers["data"][
                    0
                ]  # Get the first customer if multiple found
            else:
                # Create a new Stripe customer
                customer = stripe.Customer.create(
                    email=customer_email,
                    name=customer_name,
                    metadata=metadata,
                )

            payment_methods = stripe.PaymentMethod.list(
                customer=customer.id,
                type="card",
            )

            if not any(pm.id == payment_method_id for pm in payment_methods.data):
                stripe.PaymentMethod.attach(
                    payment_method_id,
                    customer=customer.id,
                )

            stripe.Customer.modify(
                customer.id,
                invoice_settings={
                    "default_payment_method": payment_method_id,
                },
            )

            # Create a subscription for the customer
            subscription = stripe.Subscription.create(
                customer=customer.id,
                items=[{"price": plan_id}],
                expand=["latest_invoice.payment_intent"],
                metadata=metadata,
            )

            # Retrieve the PaymentIntent from the subscription
            payment_intent = subscription.latest_invoice.payment_intent

            # Return the session URL to the frontend
            return {
                "subscriptionId": subscription.id,
                "clientSecret": payment_intent.client_secret,
            }
        except Exception as e:
            raise Exception(f"Stripe error: {str(e)}")

    @classmethod
    def handle_webhook(cls, payload, sig_header):
        try:
            stripe.api_key = STRIPE_SECRET_KEY
            event = stripe.Webhook.construct_event(
                payload, sig_header, STRIPE_WEBHOOK_SECRET
            )
        except ValueError as e:
            # Invalid payload
            raise HTTPException(status_code=400, detail=f"Invalid payload: {str(e)}")
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature
            raise HTTPException(status_code=400, detail=f"Invalid signature: {str(e)}")

        # Handle the event
        if event["type"] == "payment_intent.succeeded":
            payment_intent = event["data"]["object"]
            # Handle the payment confirmation (e.g., update your database, notify the user)
            print(f"PaymentIntent was successful! {payment_intent}")
        elif event["type"] == "checkout.session.completed":
            session = event["data"]["object"]  # Contains a stripe.Checkout.Session
            print(f"session was successful! {session}")

        elif event["type"] == "payment_intent.payment_failed":
            payment_intent = event["data"]["object"]
            print(f"PaymentIntent was Failed! {payment_intent}")

        return (
            event  # You might want to return the event for further processing if needed
        )
