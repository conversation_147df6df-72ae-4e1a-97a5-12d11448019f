from app.config.environment_variables import (
    S3_ENDPOINT,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
    S3_REGION,
    S3_BUCKET_NAME,
)
from typing import Union
import boto3
from botocore.exceptions import NoCredentialsError


class S3Client:
    def __init__(self):
        self.client = boto3.client(
            "s3",
            endpoint_url=S3_ENDPOINT,
            aws_access_key_id=S3_ACCESS_KEY,
            aws_secret_access_key=S3_SECRET_KEY,
            region_name=S3_REGION,
        )

    def build_s3_presigned_url(
        self, object_key: str, expiration: Union[int, None] = None
    ) -> str:
        """
        Build a URL for an object stored in an Amazon S3 bucket using presigned URL.

        Args:
        - object_key (str): Key (path) of the object within the bucket.
        - expiration (int, optional): Expiration time in seconds for the presigned URL. Default is 3600 seconds (1 hour).

        Returns:
        - str: Fully constructed URL for accessing the S3 object.
        """
        if self.client is None:
            raise NoCredentialsError(
                "S3 client is not initialized. Check your S3 configuration."
            )

        # Generate the presigned URL
        url = self.client.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": S3_BUCKET_NAME, "Key": object_key},
            ExpiresIn=expiration,
            HttpMethod="GET",
        )

        return url
