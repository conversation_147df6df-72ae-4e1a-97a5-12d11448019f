import threading

_thread_locals = threading.local()


def get_whodoneit():
    """
    Retrieves the `user` value stored in the thread-local storage.
    If no `user` value is set, it returns `None`.
    """
    return getattr(_thread_locals, "user", None)


def set_whodoneit(user):
    """
    Sets the `user` value in the thread-local storage to the provided `user`.
    This allows for storing user-specific information that is thread-local and can be accessed throughout the thread's lifetime.

    :param user: The user information to be stored in thread-local storage.
    """
    _thread_locals.user = user


def get_request_headers():
    """
    Retrieves the `request_headers` value stored in the thread-local storage.
    If no `request_headers` value is set, it returns `None`.
    """
    return getattr(_thread_locals, "request_headers", dict({}))


def set_request_headers(data):
    """
    Sets the `request_headers` value in the thread-local storage to the provided `data`.
    This allows for storing request headers that are specific to the current thread of execution.

    :param data: The request headers to be stored in thread-local storage.
    """
    _thread_locals.request_headers = data
