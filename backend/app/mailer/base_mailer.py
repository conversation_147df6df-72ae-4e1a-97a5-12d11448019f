import smtplib
from email.mime.multipart import MIME<PERSON><PERSON>ip<PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from app.config import (
    SMTP_PORT,
    SMTP_API_KEY,
    SMTP_PASSWORD,
    SMTP_SERVER,
    SMTP_FROM_EMAIL,
    PRODUCTION_ENV,
    CC_EMAILS,
)


class BaseMailer:
    @classmethod
    def send_email(
        cls,
        subject: str,
        to_email: str,
        body: str,
        send_to_primary: bool = False,
        attachments: list[tuple[str, bytes, str]] = [],
    ):
        """
        Sends an email with the given subject, recipient email, and body.
        """
        # Create a multipart message
        msg = MIMEMultipart()
        msg["From"] = SMTP_FROM_EMAIL
        msg["Subject"] = subject

        recipients = CC_EMAILS.split(",")
        if send_to_primary or PRODUCTION_ENV is True:
            msg["To"] = to_email
            recipients.append(to_email)
        else:
            msg["To"] = CC_EMAILS

        # Attach HTML body
        msg.attach(MIMEText(body, "html"))

        for filename, file_bytes, mime_type in attachments:
            part = MIMEApplication(file_bytes, _subtype=mime_type.split("/")[-1])
            part.add_header("Content-Disposition", f"attachment; filename={filename}")
            msg.attach(part)

        # Connect to SMTP server and send email
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(SMTP_API_KEY, SMTP_PASSWORD)
            server.sendmail(SMTP_FROM_EMAIL, recipients, msg.as_string())
