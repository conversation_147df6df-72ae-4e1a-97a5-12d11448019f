from app.models import User
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer


class UserMailer(BaseMailer):
    @classmethod
    def reset_password_email(cls, user: User):
        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        body = AppTemplates.get_template(
            "mailer/user_mailer/forgot-password.html"
        ).render(context)
        cls.send_email(subject, user.email, body, send_to_primary=True)

    @classmethod
    def user_verify_email(cls, user: User):
        subject = "Recruitease Pro - Verify Email"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        body = AppTemplates.get_template("mailer/user_mailer/verify-email.html").render(
            context
        )
        cls.send_email(subject, user.email, body, send_to_primary=True)

    @classmethod
    def resend_otp_email(cls, user: User, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "user": user,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        body = AppTemplates.get_template("mailer/user_mailer/resend-otp.html").render(
            context
        )
        cls.send_email(subject, user.email, body, send_to_primary=True)
