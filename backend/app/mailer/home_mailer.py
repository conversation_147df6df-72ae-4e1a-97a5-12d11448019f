from app.models import ContactEnquery
from app.utils import AppTemplates
from app.config import API_URL, APP_URL, ENQUERY_EMAILS
from app.mailer.base_mailer import BaseMailer


class HomeMailer(BaseMailer):

    @classmethod
    def contact_us_email(cls, contact_enquery: ContactEnquery):
        subject = "Recruitease Pro - New Contact Us Enquery"
        context = {
            "request": None,
            "contact_enquery": contact_enquery,
            "api_url": API_URL,
            "app_url": APP_URL,
        }

        body = AppTemplates.get_template("mailer/home_mailer/enquery.html").render(
            context
        )

        cls.send_email(subject, ENQUERY_EMAILS, body, send_to_primary=True)
