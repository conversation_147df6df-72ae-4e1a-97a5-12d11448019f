from app.models import Business
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer


class BusinessMailer(BaseMailer):
    @classmethod
    def business_verify_email(cls, business: Business):
        subject = "Recruitease Pro - Verify Business Email"
        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "business": business,
            "user": business.user,
        }
        body = AppTemplates.get_template(
            "mailer/business_mailer/verify-business-email.html"
        ).render(context)
        cls.send_email(subject, business.email, body, send_to_primary=True)

    @classmethod
    def resend_business_otp_email(cls, business: Business, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "business": business,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        body = AppTemplates.get_template(
            "mailer/business_mailer/resend-business-otp.html"
        ).render(context)
        cls.send_email(subject, business.email, body, send_to_primary=True)
