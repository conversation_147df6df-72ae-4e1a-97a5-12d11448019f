from app.models import (
    Business,
    Candidate,
    Employee,
    DocumentUploadLinkRequest,
    ScheduleInterview,
    EmailTrack,
    CandidateOfferLetter,
)
from app.utils import AppTemplates
from app.config import API_URL, APP_URL
from app.mailer.base_mailer import BaseMailer
from app.utils import build_subdomain_url
from typing import Optional
import requests


class CandidateMailer(BaseMailer):
    @classmethod
    def send_document_request_link(
        cls, candidate: Candidate, employee: Employee, columns: dict
    ):
        subject = "Recruitease Pro - Request for Document Upload"
        business: Business = candidate.business
        document_request = DocumentUploadLinkRequest.create_unique_record(
            candidate_id=candidate.id, employee_id=employee.id, columns=columns
        )
        subdomain_url = build_subdomain_url(APP_URL, employee.business.subdomain)
        upload_link = f"{subdomain_url}/candidates/upload_documents?token={document_request.unique_key}"
        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "upload_link": upload_link,
            "candidate": candidate,
            "business": business,
            "document_request": document_request,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/upload-candidate-email.html"
        ).render(context)
        cls.send_email(subject, candidate.email, body)

    @classmethod
    def interview_scheduled_mail(
        cls, candidate: Candidate, schedule_interview: ScheduleInterview
    ):
        subject = "Recruitease Pro - Interview Scheduled"
        business: Business = candidate.business
        title = schedule_interview.status_message()
        show_comment_only: bool = schedule_interview.status.value not in (1, 2)
        password = None
        auth_exist = candidate.candidate_auth
        try:
            if not auth_exist:
                password = candidate.create_login_auth()
        except:
            password = None
        login_url = (
            f"{build_subdomain_url(APP_URL, business.subdomain)}/candidates/auth/login"
        )

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "login_url": login_url,
            "candidate": candidate,
            "business": business,
            "title": title,
            "schedule_interview": schedule_interview,
            "show_comment_only": show_comment_only,
            "password": password,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/schedule_interview_mailer.html"
        ).render(context)
        cls.send_email(subject, candidate.email, body)

    @classmethod
    def send_custom_email(cls, candidate: Candidate, subject: str, content: str):
        subject = f"Recruitease Pro - {subject}"
        context = {
            "request": None,
            "content": content,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/custom_only.html"
        ).render(context)
        EmailTrack.create(
            body=str(body),
            subject=subject,
            to_email=candidate.email,
            model_name=candidate.__class__.__name__,
            model_id=candidate.id,
        )
        cls.send_email(subject, candidate.email, body)

    @classmethod
    def reset_password_email(cls, candidate: Candidate):
        subject = "Recruitease Pro - Password Reset OTP"
        context = {
            "request": None,
            "candidate": candidate,
            "api_url": API_URL,
            "app_url": APP_URL,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/forgot-password.html"
        ).render(context)
        cls.send_email(subject, candidate.email, body)

    @classmethod
    def resend_otp_email(cls, candidate: Candidate, resend_text: str):
        subject = "Recruitease Pro - New OTP"
        context = {
            "request": None,
            "candidate": candidate,
            "api_url": API_URL,
            "app_url": APP_URL,
            "resend_text": resend_text,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/resend-otp.html"
        ).render(context)
        cls.send_email(subject, candidate.email, body)

    @classmethod
    def send_offer_letter_email(
        cls,
        candidate: Candidate,
        offer_letter: CandidateOfferLetter,
        subject: Optional[str],
    ):
        business = candidate.business

        subject = subject or f"Recruitease Pro -  {business.name} - Offer Letter"

        # Prepare context for the email body
        context = {
            "request": None,
            "candidate": candidate,
            "business": business,
        }

        body = AppTemplates.get_template(
            "mailer/offer_letter_mailer/offer_letter.html"
        ).render(context)

        # offer_letter
        response = requests.get(offer_letter.letter_url)
        pdf_bytes = response.content
        # Prepare attachments as (filename, bytes, MIME type)
        attachments = [("Offer_Letter.pdf", pdf_bytes, "application/pdf")]

        # Send email with attachment
        cls.send_email(subject, candidate.email, body, attachments=attachments)

    @classmethod
    def interview_reminder_mail(cls, schedule_interview: ScheduleInterview):
        subject = "Recruitease Pro - Interview Reminder"
        candidate: Candidate = schedule_interview.candidate
        business: Business = candidate.business
        title = schedule_interview.status_message()
        show_comment_only: bool = schedule_interview.status.value not in (1, 2)

        context = {
            "request": None,
            "api_url": API_URL,
            "app_url": APP_URL,
            "candidate": candidate,
            "business": business,
            "title": title,
            "schedule_interview": schedule_interview,
            "show_comment_only": show_comment_only,
        }
        body = AppTemplates.get_template(
            "mailer/candidate_mailer/interview-reminder.html"
        ).render(context)
        cls.send_email(subject, candidate.email, body)
