from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status, Request, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from fastapi_pagination import add_pagination
from fastapi.middleware.gzip import GZipMiddleware
from app.api import api_router
from app.exceptions import (
    CustomValidationError,
    RecordNotFoundException,
    MethodNotAllowedException,
    UnauthorizedException,
)
from fastapi.middleware.cors import CORSMiddleware
from peewee import IntegrityError
from app.config import ALLOWED_ORIGINS, logger, SWAGGER_USERNAME, SWAGGER_PASSWORD
from app.utils import create_allow_origin_regex, generate_random_string
from database.connection import db
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from starlette.responses import RedirectResponse

app = FastAPI(title="Recruitease Pro", docs_url="/api/docs", redoc_url="/api/redocs")

app.add_middleware(
    CORSMiddleware,
    allow_origin_regex=create_allow_origin_regex(ALLOWED_ORIGINS),
    allow_credentials=True,
    allow_methods=[
        "GET",
        "POST",
        "PUT",
        "PATCH",
        "DELETE",
        "OPTIONS",
    ],  # Allow specific methods
    allow_headers=[
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "X-Forwarded-For",
    ],
)


@app.exception_handler(HTTPException)
async def validation_exception_handler(request: Request, e: HTTPException):
    logger.error(f"HTTPException Exception: {e}")
    return JSONResponse(
        status_code=e.status_code,
        content=jsonable_encoder(
            {"success": False, "status_code": e.status_code, "error": f"{e.detail}"}
        ),
    )


@app.exception_handler(RecordNotFoundException)
async def record_not_found_exception(request: Request, e: RecordNotFoundException):
    logger.error(f"RecordNotFoundException Exception: {e}")
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": status.HTTP_404_NOT_FOUND,
                "error": str(e),
            }
        ),
    )


@app.exception_handler(CustomValidationError)
async def validation_exception_handler(request: Request, e: CustomValidationError):
    logger.error(f"CustomValidationError Exception: {e} - 422 Unprocessable Entity")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
                "error": str(e),
            }
        ),
    )


@app.exception_handler(IntegrityError)
async def validation_exception_handler(request: Request, e: IntegrityError):
    logger.error(f"IntegrityError Exception: {e} - 422 Unprocessable Entity")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
                "error": str(e),
            }
        ),
    )


@app.exception_handler(UnauthorizedException)
async def validation_exception_handler(request: Request, e: UnauthorizedException):
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": status.HTTP_401_UNAUTHORIZED,
                "error": str(e),
            }
        ),
    )


@app.exception_handler(MethodNotAllowedException)
async def validation_exception_handler(request: Request, e: MethodNotAllowedException):
    logger.error(f"MethodNotAllowedException Exception: {e} 405 method not allowed")
    return JSONResponse(
        status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
        content=jsonable_encoder(
            {
                "success": False,
                "status_code": status.HTTP_405_METHOD_NOT_ALLOWED,
                "error": str(e),
            }
        ),
    )


@app.middleware("http")
async def set_request_subdomain(request: Request, call_next):
    # Extract the subdomain from the Host header
    with db.connection_context():
        subdomain = None
        origin = request.headers.get("Origin")
        request.request_id = generate_random_string(20)
        if origin:
            subdomain = origin.split("//")[-1].split(".")[0] if "." in origin else None
        request.state.subdomain = subdomain
        request.client_ip = request.headers.get("X-Forwarded-For", None)
        # Continue processing the request
        logger.info(
            f"{request.client_ip} - {request.method} {request.url.path}",
            extra={"request_id": request.request_id},
        )
        # Log parameters
        response = await call_next(request)
        status_code_message = (
            "successfully" if response.status_code < 400 else "with failure"
        )
        logger.info(
            f"Completed {status_code_message} (status code: {response.status_code})",
            extra={"request_id": request.request_id},
        )
        return response


app.add_middleware(GZipMiddleware, minimum_size=1000)

# HTTPBasic security scheme to require username and password
security = HTTPBasic()


# Dependency to check username and password
def authenticate(credentials: HTTPBasicCredentials = Depends(security)):
    if (
        credentials.username != SWAGGER_USERNAME
        or credentials.password != SWAGGER_PASSWORD
    ):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials


# Protect the /docs route with username/password
@app.get("/api/docs", include_in_schema=False)
async def get_documentation(credentials: HTTPBasicCredentials = Depends(authenticate)):
    return get_swagger_ui_html(openapi_url="/openapi.json", title="Swagger UI")


# Protect the /redocs route
@app.get("/api/redocs", include_in_schema=False)
async def get_redoc_documentation(
    credentials: HTTPBasicCredentials = Depends(authenticate),
):
    return RedirectResponse("/api/docs")


# api's
app.include_router(api_router, prefix="/api/v1")

# static or public files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
app.mount("/", StaticFiles(directory="public"), name="public")


add_pagination(app)
