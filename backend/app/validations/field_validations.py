from app.utils.email_utils import EmailUtils
from app.utils.password_utils import PasswordUtils
from typing import Optional
import re


def EmailValidate(
    email: str,
    field: str = "email",
    max_length: Optional[int] = None,
    min_length: Optional[int] = None,
) -> Optional[str]:
    """
    Validates email format and raises an error for invalid emails.

    Args:
        email: The email address to validate.
        field: The name of the field for error messages (default: "email").

    Raises:
        ValueError: If the email format is invalid.

    Returns:
        The validated email address.
    """

    if not EmailUtils.is_valid_email(email):
        raise ValueError(f"Invalid {field}")
    if max_length is not None and len(email) > max_length:
        raise ValueError(f"{field} must be at most {max_length} characters long")
    if min_length is not None and len(email) < min_length:
        raise ValueError(f"{field} must be at least {min_length} characters long")
    is_disposable = EmailUtils.validate_and_check_disposable(email)
    if is_disposable:
        raise ValueError(f"{field} is not valid.")
    # save email always in lower case
    email = email.lower()

    return email


def StringValidate(
    value: Optional[str],
    required: bool = True,
    field: str = "string",
    max_length: Optional[int] = None,
    min_length: Optional[int] = None,
    strip: bool = False,
    pattern: Optional[str] = None,
    pattern_error: Optional[str] = None,
) -> Optional[str]:
    """
    Validates a string with optional checks for emptiness, min/max length.

    Args:
        value: The string to validate.
        required: Whether the field is required (default: True).
        field: The name of the field for error messages (default: "string").
        max_length: Maximum allowed length (optional).
        min_length: Minimum required length (optional).

    Raises:
        ValueError: If the string validation fails.

    Returns:
        The validated string or None if empty and not required.
    """

    if strip and value:
        value = value.strip()
    if required and (value is None or value.strip() == ""):
        raise ValueError(f"{field} cannot be empty")

    if value is None:
        return None  # Early exit for empty optional value

    if max_length is not None and len(value) > max_length:
        raise ValueError(f"{field} must be at most {max_length} characters long")

    if min_length is not None and len(value) < min_length:
        raise ValueError(f"{field} must be at least {min_length} characters long")

    # Validate against the custom pattern if provided
    if pattern and not re.fullmatch(pattern, value):
        if pattern_error:
            raise ValueError(f"{field} {pattern_error}")
        else:
            raise ValueError(f"{field} must match the pattern: {pattern}")

    return value


def NumberValidate(
    value, field: str = "number", allow_zero=True, min_value=None, max_value=None
):
    """
    Validates a number with optional min/max, zero allowance, and field name.

    Args:
        value: The number to validate.
        field: The name of the field for error messages (default: "number").
        allow_zero: Whether to allow zero (default: True).
        min_value: Minimum allowed value (optional).
        max_value: Maximum allowed value (optional).

    Raises:
        ValueError: If validation fails.

    Returns:
        The validated number.
    """

    if not isinstance(value, (int, float)):
        raise ValueError(f"{field} must be a number")

    if not allow_zero and value == 0:
        raise ValueError(f"{field} cannot be zero")

    if min_value is not None and value < min_value:
        raise ValueError(f"{field} must be at least {min_value}")

    if max_value is not None and value > max_value:
        raise ValueError(f"{field} must be at most {max_value}")

    return value


def PasswordValidate(
    value: Optional[str],
    field: str = "password",
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
) -> Optional[str]:
    """
    Validates a password with checks for emptiness, minimum length, and complexity.

    Args:
        value: The password to validate.
        required: Whether the field is required (default: True).
        field: The name of the field for error messages (default: "password").
        min_length: Minimum required length (default: None).

    Raises:
        ValueError: If the password validation fails.

    Returns:
        The validated password or None if empty and not required.
    """

    # Perform basic string validation using StringValidate
    value = StringValidate(
        value, True, field=field, min_length=min_length, max_length=max_length
    )

    if not value:
        return None

    valid, error_message = PasswordUtils.is_password_secure(value)
    if not valid:
        raise ValueError(error_message)

    # convert to encryted password
    hashed_password = PasswordUtils.hash_password(value)
    return hashed_password


def WebsiteURLValidate(
    value: Optional[str],
    required: bool = True,
    allowEmpty: bool = False,
    field: str = "URL",
    max_length: Optional[int] = None,
    min_length: Optional[int] = None,
    strip: bool = False,
) -> Optional[str]:
    """
    Validates a website URL with optional checks for emptiness, min/max length.

    Args:
        value: The URL to validate.
        required: Whether the field is required (default: True).
        field: The name of the field for error messages (default: "URL").
        max_length: Maximum allowed length (optional).
        min_length: Minimum required length (optional).
        strip: Whether to strip whitespace from the value (default: False).

    Raises:
        ValueError: If the URL validation fails.

    Returns:
        The validated URL or None if empty and not required.
    """

    value = StringValidate(
        value,
        required=required,
        field=field,
        min_length=min_length,
        max_length=max_length,
        strip=strip,
    )

    # Regex pattern to validate URLs
    url_regex = re.compile(
        r"^(https?:\/\/)?"  # optional http/https
        r"([a-zA-Z0-9.-]+)"  # domain
        r"(\.[a-zA-Z]{2,6})"  # TLD
        r"(\/[^\s]*)?$"  # optional path
    )

    if allowEmpty and value == "":
        return value

    if not url_regex.match(value):
        raise ValueError(f"{field} is not a valid URL")

    return value


def WebsiteDomainValidate(
    value: Optional[str],
    required: bool = True,
    allowEmpty: bool = False,
    field: str = "DOMAIN",
    max_length: Optional[int] = None,
    min_length: Optional[int] = None,
    strip: bool = False,
) -> Optional[str]:
    """
    Validates a website Domain with optional checks for emptiness, min/max length.

    Args:
        value: The Domain to validate.
        required: Whether the field is required (default: True).
        field: The name of the field for error messages (default: "Domain").
        max_length: Maximum allowed length (optional).
        min_length: Minimum required length (optional).
        strip: Whether to strip whitespace from the value (default: False).

    Raises:
        ValueError: If the URL validation fails.

    Returns:
        The validated Domain or None if empty and not required.
    """

    value = StringValidate(
        value,
        required=required,
        field=field,
        min_length=min_length,
        max_length=max_length,
        strip=strip,
    )

    # Regex pattern to validate DOMAINs
    domain_regex = re.compile(
        r"^(https?:\/\/)?"  # optional http/https
        r"([a-zA-Z0-9.-]+)"  # domain
        r"(\.[a-zA-Z]{2,6})$"  # TLD
    )

    if allowEmpty and value == "":
        return value

    if not domain_regex.match(value):
        raise ValueError(f"{field} is not a valid domain")

    return value


def SubdomainValidate(
    value: Optional[str],
    required: bool = True,
    field: str = "Subdomain",
    max_length: Optional[int] = None,
    min_length: Optional[int] = None,
    strip: bool = False,
) -> Optional[str]:
    """
    Validates a subdomain with optional checks for emptiness, min/max length.

    Args:
        value: The subdomain to validate.
        required: Whether the field is required (default: True).
        field: The name of the field for error messages (default: "Subdomain").
        max_length: Maximum allowed length (optional).
        min_length: Minimum required length (optional).
        strip: Whether to strip whitespace from the value (default: False).

    Raises:
        ValueError: If the subdomain validation fails.

    Returns:
        The validated subdomain or None if empty and not required.
    """

    value = StringValidate(
        value,
        required=required,
        field=field,
        min_length=min_length,
        max_length=max_length,
        strip=strip,
    )

    # Regex pattern to validate subdomains
    subdomain_regex = re.compile(r"^(?!-)[A-Za-z0-9-]{1,63}(?<!-)$")

    if not subdomain_regex.match(value):
        raise ValueError(f"{field} is not a valid subdomain")

    return value
