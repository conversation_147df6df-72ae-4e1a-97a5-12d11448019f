from fpdf import FPDF


# Create a class that extends FPDF
class TextToPDFUtils(FPDF):

    def footer(self):
        self.set_y(-15)  # Go to 1.5 cm from bottom
        self.set_font("Arial", "I", 8)  # Select Arial italic 8
        self.cell(0, 10, f"Page {self.page_no()}", 0, 0, "C")  # Page number

    def addscell(self, data: str, align="L"):
        self.cell(0, 10, data, 0, 1, align)

    def addmcell(self, data: str, align="L"):
        self.multi_cell(0, 10, data, 0, 1, align)

    def addsection(self):
        self.ln(5)  # Line break
        self.line(20, self.get_y(), 180, self.get_y())
        self.ln(5)  # Line break
