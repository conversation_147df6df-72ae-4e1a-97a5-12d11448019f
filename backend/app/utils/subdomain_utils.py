import re
import spacy
import secrets

# Load the spaCy NLP model
nlp = spacy.load("en_core_web_lg")


class SubdomainUtils:
    RESERVED_SUBDOMAINS = {
        "www",
        "mail",
        "ftp",
        "api",
        "admin",
        "secure",
        "candidates",
        "candidate",
        "app" "portal",
        "client",
        "ats",
        "home",
    }

    @staticmethod
    def extract_keywords(business_name: str):
        """Extract keywords using NLP model"""
        doc = nlp(business_name)
        keywords = [
            token.lemma_ for token in doc if token.is_alpha and not token.is_stop
        ]
        return keywords

    @staticmethod
    def generate_random_string(length=6):
        return secrets.token_hex(
            length // 2
        )  # Generates a hex string of the desired length

    @classmethod
    def generate_subdomain(cls, business_name: str) -> str:
        # Step 1: Extract keywords using NLP
        keywords = cls.extract_keywords(business_name)

        # Step 2: Combine keywords into a potential subdomain
        if keywords:
            subdomain = "".join(keywords).lower()
        else:
            # Fallback if no keywords are extracted
            subdomain = re.sub(r"[^a-zA-Z0-9]", "", business_name.lower())

        # Step 3: Check if it starts with a numeric character, prepend 'biz' if it does
        if subdomain[0].isdigit():
            subdomain = "biz" + subdomain

        # Step 4: Check for reserved subdomains
        if subdomain in cls.RESERVED_SUBDOMAINS:
            subdomain += "hub"

        # Step 5: Limit subdomain length to a maximum of 30 characters
        if len(subdomain) > 40:
            excess_length = len(subdomain) - 40
            subdomain = subdomain[:-excess_length]

        return subdomain

    # Function to ensure subdomain uniqueness
    @classmethod
    def ensure_unique_subdomain(cls, business_name: str, length: int = 6) -> str:
        """
        Ensures the subdomain is unique by appending a random string if the subdomain is already in use.

        Parameters:
        subdomain (str): The original subdomain to check.
        used_subdomains (set): A set of already used subdomains to avoid collisions.
        length (int): Length of the random string to append if necessary.

        Returns:
        str: A unique subdomain.
        """
        original_subdomain = cls.generate_subdomain(business_name)
        random_string = cls.generate_random_string(length)
        subdomain = f"{original_subdomain}-{random_string}"

        if len(subdomain) > 40:
            excess_length = len(subdomain) - 40
            subdomain = subdomain[:-excess_length]

        return subdomain
