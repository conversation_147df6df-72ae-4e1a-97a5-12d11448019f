from app.utils.text_to_pdf_utils import TextToPDFUtils
import logging


class ResumeBuilderUtils:
    @staticmethod
    def create_resume(pdf: TextToPDFUtils, resume_upload_path: str, data=None):
        # Add a page
        pdf.add_page()

        # Set name as heading
        pdf.set_font("Arial", "B", 20)
        pdf.addscell(data["name"].upper(), "C")

        # Set role as sub-heading
        pdf.set_font("Arial", "B", 16)
        pdf.addscell(data["role"], "C")

        # Add basic details
        pdf.set_font("Arial", "", 12)
        pdf.addscell(f"{data['email']} | {data['phone']}", "C")
        pdf.addscell(f"{data['linkedin']} | {data['github']} | {data['website']}", "C")
        pdf.ln(10)  # Line break

        # Add summary
        pdf.set_font("Arial", "B", 14)
        pdf.addscell("Summary:")
        pdf.set_font("Arial", "", 12)
        pdf.addmcell(data["summary"])
        pdf.addsection()

        # Add professional experience
        pdf.set_font("Arial", "B", 14)
        pdf.addscell("Professional Experience:")
        for exp in data["experience"]:
            pdf.set_font("Arial", "B", 12)
            pdf.addscell(
                f"{exp['role']} - {exp['company']} | {exp['start_date']} - {exp['end_date']}"
            )
            pdf.set_font("Arial", "", 12)
            for achievement in exp["achievements"]:
                pdf.addscell(f"- {achievement}")
        pdf.addsection()

        # Add education
        pdf.set_font("Arial", "B", 14)
        pdf.addscell("Education Details:")
        pdf.set_font("Arial", "", 12)
        for edu in data["education"]:
            pdf.addscell(
                f"{edu['degree']} - {edu['university']} | {edu['start_date']} - {edu['end_date']}"
            )
        pdf.addsection()

        # Add skills
        pdf.set_font("Arial", "B", 14)
        pdf.addscell("Skills:")
        pdf.set_font("Arial", "B", 12)
        pdf.addscell("Technical Skills")
        pdf.set_font("Arial", "", 12)
        for tech in data["skills"]["Technical"]:
            pdf.addscell(f"- {tech}")
        pdf.set_font("Arial", "B", 12)
        pdf.addscell("Soft Skills")
        pdf.set_font("Arial", "", 12)
        for soft in data["skills"]["Soft"]:
            pdf.addscell(f"- {soft}")

        resume_upload_path = (
            "uploads/development/resumes/" + resume_upload_path + ".pdf"
        )
        logging.info(f"Resume saved at: {resume_upload_path}")

        # Save the PDF to a file
        pdf.output(resume_upload_path)
        return resume_upload_path

    @staticmethod
    def extract_required_data_to_create_resume(
        basic_details,
        education_details,
        experience_details,
        skills_details,
        resume_upload_subdir: str,
    ):
        try:
            experience = []
            for exp in experience_details:
                if not isinstance(exp, dict):
                    raise ValueError("Invalid experience details")
                end_date = ""
                if exp.get("is_current_experience", False):
                    end_date = ""
                else:
                    end_date = exp.get("end_date", "")
                achievements = (
                    ((str(exp.get("responsibilities", "")))[2:-2]).split("\\n")
                    if exp.get("responsibilities")
                    else []
                )
                experience.append(
                    {
                        "role": exp.get("position", ""),
                        "company": exp.get("employer", ""),
                        "start_date": exp.get("start_date", ""),
                        "end_date": end_date,
                        "achievements": achievements,
                    }
                )

            education = []
            for edu in education_details:
                if not isinstance(edu, dict):
                    raise ValueError("Invalid education details")
                education.append(
                    {
                        "degree": edu.get("degree", ""),
                        "university": edu.get("university", ""),
                        "start_date": edu.get("start_date", ""),
                        "end_date": edu.get("end_date", ""),
                    }
                )

            skills = {"Technical": [], "Soft": []}
            for skill in skills_details:
                if not isinstance(skill, dict):
                    raise ValueError("Invalid skills details")
                if skill.get("skill_type") == "Technical":
                    skills["Technical"].append(skill.get("name", ""))
                else:
                    skills["Soft"].append(skill.get("name", ""))

            required_fields = [
                "name",
                "designation",
                "email",
                "contact",
                "linkedin",
                "github",
                "website",
            ]
            if not all(field in basic_details for field in required_fields):
                raise ValueError("Invalid basic details")
            summary = f"Myself {basic_details['name']}, I'm currently working as a {basic_details['designation']} with over {basic_details['total_experience']} years of Experience. I'm skilled in {', '.join(skills['Technical'])} and {', '.join(skills['Soft'])}."

            data = {
                "name": basic_details["name"],
                "role": basic_details["designation"],
                "email": basic_details["email"],
                "phone": basic_details["contact"],
                "linkedin": basic_details["linkedin"],
                "github": basic_details["github"],
                "website": basic_details["website"],
                "summary": summary,
                "experience": experience,
                "education": education,
                "summary": summary,
                "skills": skills,
            }

            resume_path = ResumeBuilderUtils.create_resume(
                pdf=TextToPDFUtils(), resume_upload_path=resume_upload_subdir, data=data
            )
            return resume_path
        except Exception as e:
            logging.error(f"An error occurred: {str(e)}")
