from urllib.parse import urlparse, urlunparse


def build_subdomain_url(url, subdomain):
    # Parse the URL
    parsed_url = urlparse(url)

    # Split the netloc to get the domain
    domain_parts = parsed_url.netloc.split(".")

    # Add the subdomain
    if len(domain_parts) > 1:
        domain_parts.insert(-len(domain_parts), subdomain)
    else:
        domain_parts.insert(0, subdomain)

    # Reconstruct the netloc with the subdomain
    new_netloc = ".".join(domain_parts)

    # Rebuild the URL with the new netloc
    new_url = urlunparse(parsed_url._replace(netloc=new_netloc))

    return new_url
