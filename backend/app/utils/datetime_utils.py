from datetime import datetime
import pytz


class DatetimeUtils:

    @staticmethod
    def convert_to_timezone(dt: datetime, timezone_str: str = "UTC") -> datetime:
        """
        Convert a datetime (naive or aware) to the specified timezone.

        Args:
            dt: The datetime object (naive assumed UTC).
            timezone_str: The timezone name string, e.g. "Asia/Kolkata".

        Returns:
            Timezone-aware datetime converted to given timezone.
        """
        # If naive, assume UTC
        if dt.tzinfo is None:
            dt = pytz.utc.localize(dt)

        target_tz = pytz.timezone(timezone_str)
        return dt.astimezone(target_tz)
