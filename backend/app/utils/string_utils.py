import uuid
import re
import random
import string


def generate_random_string(length=10):
    return str(uuid.uuid4())[:length].replace("-", "")


def generate_unique_key(length=10) -> str:
    """Generate a random unique key."""
    characters = string.ascii_letters + string.digits
    return "".join(random.choices(characters, k=length))


def create_allow_origin_regex(allowed_origins):
    # If "*" is present in allowed_origins, return a regex pattern to match all origins
    if "*" in allowed_origins:
        return r"^.*$"

    # Escape special characters in each allowed origin
    escaped_origins = [re.escape(origin) for origin in allowed_origins]

    # Replace '*' with regex pattern '.*' for wildcard subdomains
    replaced_wildcard = [origin.replace(r"\*", r".*") for origin in escaped_origins]

    # Join the escaped origins with '|' as separator for regex OR operation
    regex_pattern = "|".join(replaced_wildcard)

    # Create the final regex pattern
    allow_origin_regex = rf"^(?:{regex_pattern})$"

    return allow_origin_regex
