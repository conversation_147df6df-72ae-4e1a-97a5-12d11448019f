from fastapi.templating import Jinja2Templates
from typing import Optional
from datetime import datetime
import pytz

AppTemplates = Jinja2Templates(directory="templates")


def to_timezone(dt: Optional[datetime], timezone_str: str) -> str:
    if dt is None:
        return ""
    if dt.tzinfo is None:
        dt = pytz.utc.localize(dt)
    target_tz = pytz.timezone(timezone_str)
    dt_in_tz = dt.astimezone(target_tz)

    time = dt_in_tz.strftime("%Y-%m-%d %H:%M %Z")
    return time


# Register filter in Jinja environment
AppTemplates.env.filters["to_timezone"] = to_timezone
