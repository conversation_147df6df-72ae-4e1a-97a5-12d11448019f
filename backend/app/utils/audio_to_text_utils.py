import requests
import speech_recognition as sr
from io import BytesIO
from pathlib import Path
from pydub import AudioSegment
import tempfile
import os
from typing import Optional


class AudioToTextUtils:
    @staticmethod
    def audio_to_text(file_path: str, delete_file: Optional[bool] = None):
        path = Path(file_path)

        if not path.is_file():
            # "Audio file does not exist."
            return ""

        recognizer = sr.Recognizer()
        temp_mp3 = tempfile.NamedTemporaryFile(suffix=".mp3", delete=False)
        temp_wav = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)

        temp_mp3_file = temp_mp3.name
        temp_wav_file = temp_wav.name

        # Step 1: Convert MP3 to WAV
        AudioToTextUtils.convert_to_mp3(file_path, temp_mp3_file)
        AudioToTextUtils.convert_mp3_to_wav(temp_mp3_file, temp_wav_file)
        text = ""

        try:
            with sr.AudioFile(temp_wav_file) as source:
                print("Loading audio file...")
                audio_data = recognizer.record(source)
                print("Audio file loaded successfully.")
            print("Converting speech to text...")
            text = recognizer.recognize_google(audio_data)
            print("Conversion complete!")
            os.remove(temp_wav_file)
            os.remove(temp_mp3_file)
            if delete_file:
                os.remove(file_path)
            print(text)

        except sr.UnknownValueError:
            text = ""
        except sr.RequestError as e:
            text = ""
        except Exception as e:
            print(f"An error occurred: {e}")

        return text

    @staticmethod
    def transcribe_audio_from_url(url):
        recognizer = sr.Recognizer()

        # Fetch audio file from URL
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            print("Audio file fetched successfully.")

            # Load the audio data into memory
            audio_data = BytesIO(response.content)
            try:
                with sr.AudioFile(audio_data) as source:
                    print("Processing audio...")
                    audio_content = recognizer.record(source)
                    # Recognize speech using Google API
                    text = recognizer.recognize_google(audio_content)
                    print("Transcription:")
                    print(text)
                    return text
            except sr.UnknownValueError:
                print("Speech was not understood.")
                return ""
            except sr.RequestError as e:
                print(f"Could not request results; {e}")
                return ""
        else:
            return ""

    @staticmethod
    def convert_mp3_to_wav(mp3_file_path: str, wav_file_path: str):
        """Converts an MP3 file to WAV format using pydub."""
        audio = AudioSegment.from_mp3(mp3_file_path)
        audio.export(wav_file_path, format="wav")
        print(f"File converted to {wav_file_path}")

    @staticmethod
    def convert_to_mp3(input_file_path: str, output_file_path: str):
        """Convert an audio file to MP3 format using pydub."""
        try:
            # Load the input audio file (supports multiple formats like WAV, MP3, etc.)
            audio = AudioSegment.from_file(input_file_path)

            # Export the audio to MP3 format
            audio.export(output_file_path, format="mp3")
            print(f"File converted to MP3 and saved to {output_file_path}")

        except Exception as e:
            print(f"An error occurred while converting the file: {e}")
