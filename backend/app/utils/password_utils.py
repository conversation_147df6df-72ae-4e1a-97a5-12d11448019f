import string
import secrets
import re
from passlib.context import CryptContext


class PasswordUtils:
    # Initialize a password context
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    @staticmethod
    def is_password_secure(password: str) -> tuple[bool, str]:
        """
        Check if the provided password meets the security requirements.

        Returns:
        - Tuple[bool, str]: A tuple containing a boolean indicating whether the password meets the requirements and an error message if it doesn't.
        """
        # Define password strength requirements
        require_lowercase = True
        require_uppercase = True
        require_digit = True
        require_special = True  # Special characters such as !@#$%^&*()_+{}

        error_message = ""

        # Check for lowercase letters
        if require_lowercase and not any(char.islower() for char in password):
            error_message = "Password must contain at least one lowercase letter."

        # Check for uppercase letters
        elif require_uppercase and not any(char.isupper() for char in password):
            error_message = "Password must contain at least one uppercase letter."

        # Check for digits
        elif require_digit and not any(char.isdigit() for char in password):
            error_message = "Password must contain at least one digit."

        # Check for special characters
        elif require_special and not re.search(
            r"[!@#$%^&*()_+{}[\]:;<>,.?/~`]", password
        ):
            error_message = "Password must contain at least one special character."

        # Determine if password meets requirements
        is_secure = not error_message

        return is_secure, error_message

    @staticmethod
    def hash_password(password: str):
        """
        Hash the provided password.
        """
        return PasswordUtils.pwd_context.hash(password)

    @staticmethod
    def verify_password(plain_password, hashed_password):
        """
        Verify the provided plain password against the hashed password.
        """
        return PasswordUtils.pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def generate_password(length=12):
        """
        Generate a unique alphanumeric password with at least one special character,
        one uppercase letter, one lowercase letter, and one digit.

        Args:
            length (int): The desired length of the password. Must be at least 8 characters.

        Returns:
            str: The generated password.

        Raises:
            ValueError: If the length is less than 8 characters.
        """
        if length < 8:  # Ensure minimum length to accommodate all character types
            raise ValueError("Password length must be at least 8 characters")

        # Define the character sets
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special_characters = string.punctuation

        # Ensure at least one character from each set is present in the password
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special_characters),
        ]

        # Fill the rest of the password length with a mix of all characters
        all_characters = lowercase + uppercase + digits + special_characters
        password += [secrets.choice(all_characters) for _ in range(length - 4)]

        # Shuffle the resulting password list to ensure randomness
        secrets.SystemRandom().shuffle(password)

        # Convert list to string and return
        return "".join(password)
