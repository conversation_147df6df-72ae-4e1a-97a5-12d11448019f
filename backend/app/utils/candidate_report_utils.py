import io
import pandas as pd
from openpyxl import Workbook
from typing import List, Dict


class CandidateReportUtils:
    @staticmethod
    def create_csv_report(data: List[Dict[str, str]]) -> io.BytesIO:
        """Create a csv report from a list of dictionary objects

        Args:
            data (List[Dict[str, str]]): user data to be written to CSV.

        Returns:
            io.BytesIO: csv file as BytesIO object.
        """
        # Create a DataFrame from the list of dictionaries
        df = pd.DataFrame(data)
        output = io.BytesIO()
        df.to_csv(output, index=False, encoding="utf-8")
        # Move the cursor to the beginning of the BytesIO object
        output.seek(0)
        return output

    @staticmethod
    def create_excel_report(data: List[Dict[str, str]]) -> io.BytesIO:
        """Create an excel report from a list of dictionary objects

        Args:
            data (List[Dict[str, str]]): user data to be written to Excel.

        Returns:
            io.BytesIO: excel file as BytesIO object.
        """
        # Create a new Workbook and select the active worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Candidate Report"

        # Define the column headers
        headers = [
            "candidate_name",
            "candidate_email",
            "candidate_created_at",
            "interview_updated_at",
            "interview_status",
        ]

        # Write the headers to the first row
        ws.append(headers)

        # Write the data rows
        for row in data:
            ws.append([row.get(header, "") for header in headers])

        # Create a BytesIO object to hold the Excel data
        output = io.BytesIO()

        # Save the workbook to the BytesIO object
        wb.save(output)

        # Move the cursor to the beginning of the BytesIO object
        output.seek(0)

        return output
