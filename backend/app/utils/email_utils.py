import re
from disposable_mail_checker import is_disposable_mail


class EmailUtils:
    @staticmethod
    def is_valid_email(email: str) -> bool:
        # Use a simple regex pattern for email validation
        email_regex = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        return bool(re.match(email_regex, email))

    @staticmethod
    def validate_and_check_disposable(email: str):
        """
        Combines email validation and disposable domain check using the validate_email package.

        Args:
            email (str): The email address to validate.

        """
        try:
            is_disposable: bool = is_disposable_mail(
                email
            )  # Enable disposable domain check
            return is_disposable
        except Exception as e:
            print(f"Error during email validation: {e}")
            return False
