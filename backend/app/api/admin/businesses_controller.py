from fastapi import (
    Request,
    APIRouter,
    Depends,
    Query,
    HTTPException,
    status,
    Body,
    Response,
    BackgroundTasks,
)
from app.schema import SuccessResponse, PaginationResponse
from app.models import Business, User, Employee, AuthToken
from app.validations import Email<PERSON>alidate, StringValidate, WebsiteDomainValidate
from app.exceptions import RecordNotFoundException, CustomValidationError
from app.tasks import BusinessTask
from app.helper import AdminAuthHelper, WildcardAuthHelper
import logging

router = APIRouter(
    tags=["Admin Business API"],
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


def get_business(
    id: int, current_user: User = Depends(AdminAuthHelper.get_admin_user)
) -> Business:
    business = None
    if current_user.is_super_admin:
        business = Business.get_or_none(id=id)
    else:
        business = Business.get_or_none(id=id, user_id=current_user.id)
    if not business:
        raise RecordNotFoundException(message="Business not found or is inactive")
    return business


@router.get(
    "/businesses",
    summary="List of Businesses",
    description="Get a paginated list of businesses.",
    response_model=PaginationResponse,
)
def get_businesses(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
):
    """
    Retrieve a paginated list of businesses.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of businesses per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of businesses.
    """
    try:
        # Fetch total count
        if current_user.is_super_admin:
            business_query = Business.select()
        else:
            business_query = Business.select().where(
                Business.user_id == current_user.id
            )

        total_business = business_query.count()

        # Paginate results
        if total_business > 0:
            offset = (page - 1) * limit
            businesses = (
                business_query.offset(offset).limit(limit).order_by(Business.id.desc())
            )
        else:
            businesses = []

        # Prepare business list
        business_list = [
            business.info(show_user=current_user.is_super_admin)
            for business in businesses
        ]

        return PaginationResponse(
            data={
                "page": page,
                "limit": limit,
                "count": total_business,
                "rows": business_list,
            },
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in business list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post(
    "/businesses",
    summary="Register New Business",
    description="Register a new business with the provided information.",
    response_model=SuccessResponse,
)
async def create_business(
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "name": "John",
            "subdomain": "sub-test",
            "website": "example.com",
            "contact_number": "9876543210",
            "location": "mohali",
            "": "",
        },
    ),
):
    """
    Endpoint to register a new business.

    Args:
        current_user (User): The admin user creating the business, provided by dependency injection.
        body (dict): The request body containing business information.

    Returns:
        SuccessResponse: Success message along with business details upon successful registration.
    """
    try:
        email = EmailValidate(body.get("email"), field="Email")
        website = WebsiteDomainValidate(
            body.get("website"), field="Website URL", max_length=100, strip=True
        )
        name = StringValidate(
            body.get("name"), field="Name", required=True, max_length=100, strip=True
        )
        contact_number = StringValidate(
            body.get("contact_number"),
            field="Contact Number",
            required=True,
            max_length=15,
            strip=True,
        )
        location = StringValidate(
            body.get("location"),
            field="Location",
            required=True,
            max_length=255,
            strip=True,
        )
        timezone = StringValidate(
            body.get("timezone"),
            field="Timezone",
            required=True,
            max_length=255,
            strip=True,
        )

        # Check if business already exists with the provided email or website
        if Business.get_or_none(email=email):
            raise ValueError("Business already exists with this email")

        if Business.is_website_exists(website=website):
            raise ValueError("Business already exists with this website domain")

        # Generate unique subdomain based on business name
        subdomain = Business.generate_unique_subdomain(name)

        # Create the new business
        business = Business.create(
            email=email,
            website=website,
            name=name,
            subdomain=subdomain,
            contact_number=contact_number,
            location=location,
            user_id=current_user.id,
            timezone=timezone,
        )

        # Generate OTP for email verification
        business.generate_otp()

        BusinessTask.business_verify_email.delay(business.id)

        # Return success response with business details
        return SuccessResponse(
            message="An email containing the OTP has been sent to your business email address. Please use this OTP to verify your business email address.",
            data=business.info(show_user=current_user.is_super_admin),
        )
    except ValueError as e:
        raise CustomValidationError(error=str(e))


@router.put(
    "/businesses/{id}",
    summary="Update Business Detail",
    description="Update detail of business with the provided information.",
    response_model=SuccessResponse,
)
async def update_business(
    business: Business = Depends(get_business),
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "name": "John",
            "subdomain": "sub-test",
            "website": "example.com",
            "contact_number": "9876543210",
        },
    ),
):
    """
    Endpoint to register a new business.

    Args:
        body (dict): The request body containing business information.

    Returns:
        SuccessResponse: Success message along with business details upon successful registration.
    """
    try:

        email = EmailValidate(body.get("email"), field="Email")
        website = WebsiteDomainValidate(
            body.get("website"), field="Website URL", max_length=100, strip=True
        )
        name = StringValidate(
            body.get("name"), field="Name", required=True, max_length=100, strip=True
        )
        contact_number = StringValidate(
            body.get("contact_number"),
            field="Contact Number",
            required=True,
            max_length=15,
            strip=True,
        )

        timezone = StringValidate(
            body.get("timezone"),
            field="Timezone",
            required=True,
            max_length=255,
            strip=True,
        )

        business_email_same = business.email == email
        business_website_same = business.website == website

        # Check if business already exists with the provided email or website
        if Business.get_or_none(email=email) and not business_email_same:
            raise ValueError("Business already exists with this email")

        if Business.is_website_exists(website=website) and not business_website_same:
            raise ValueError("Business already exists with this website domain")

        if business.email_verified and (
            not business_email_same or not business_website_same
        ):
            raise CustomValidationError(error="Business Account Already Verified")

        if not business.email_verified:
            business.email = email
            business.website = website

        business.name = name
        business.contact_number = contact_number
        business.timezone = timezone
        business.save()

        # Generate OTP for email verification
        if not business_email_same:
            business.generate_otp()
            BusinessTask.business_verify_email.delay(business.id)
            return SuccessResponse(
                message="An email containing the OTP has been sent to your business email address. Please use this OTP to verify your business email address.",
                data=business.info(show_user=current_user.is_super_admin),
            )

        # Return success response with business details
        return SuccessResponse(
            message="Details Updated Successfully.",
            data=business.info(show_user=current_user.is_super_admin),
        )
    except ValueError as e:
        raise CustomValidationError(error=str(e))
    except Exception as e:
        raise CustomValidationError(error=str(e))


@router.post(
    "/businesses/{id}/verify-email",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=SuccessResponse,
)
async def verify_email(
    background_tasks: BackgroundTasks,
    business: Business = Depends(get_business),
    body: dict = Body(..., example={"otp": "123456"}),
):
    """
    Endpoint to verify the email using OTP.

    Args:
        business (Business): The business instance, provided by dependency injection.
        body (dict): The request body containing the OTP.

    Returns:
        SuccessResponse: The response indicating the result of the verification.
    """
    try:
        otp = body.get("otp")

        if business.email_verified:
            return SuccessResponse(message="Email already verified")

        if business.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != business.otp:
            raise CustomValidationError(error="OTP is invalid")

        business.email_verified = True
        business.otp = ""
        business.save()
        background_tasks.add_task(business.create_business_accounts)
        background_tasks.add_task(business.create_default_node_permissions)
        return SuccessResponse(message="Business Email verified successfully")
    except CustomValidationError as e:
        raise e
    except Exception as e:
        logging.error(f"Exception in business verify email: {str(e)}")
        raise HTTPException(status_code=500, detail="Something went wrong")


@router.put(
    "/businesses/{id}/resend-verify-email",
    summary="Resend Verify Email",
    description="Resend Verify Email.",
    response_model=SuccessResponse,
)
def resend_verify_email(business: Business = Depends(get_business)):
    """
    Endpoint to resend the verification email.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message.
    """
    try:
        business.generate_otp()
        BusinessTask.resend_business_otp_email.delay(
            business_id=business.id,
            resend_text="Please use this OTP to verify your business email.",
        )
        return SuccessResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your business email address."
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/businesses/{id}/update-status",
    response_model=SuccessResponse,
    summary="Update Business Status",
    description="Update the status of a business.",
)
def update_business_status(
    business: Business = Depends(get_business),
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of a business.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated business information.
    """
    try:
        status = body.get("status")
        business.status = status
        business.save()
        return SuccessResponse(
            message="Status update successfully.",
            data=business.info(show_user=current_user.is_super_admin),
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/businesses/{id}/payment",
    response_model=SuccessResponse,
    summary="Update Business Payment",
    description="Update the status of a business.",
)
def update_business_status(
    business: Business = Depends(get_business),
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
):
    """
    Update the status of a business.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and updated business information.
    """
    try:
        # business.payment_status = 1
        # business.save()
        return SuccessResponse(
            message="Payment received successfully.",
            data=business.info(show_user=current_user.is_super_admin),
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/businesses/{id}/portal_login",
    response_model=SuccessResponse,
    summary="Business Portal Login",
    description="Wildcard business portal login.",
)
async def update_business_status(
    request: Request,
    response: Response,
    business: Business = Depends(get_business),
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
):
    """
    Business Portal Login.

    Args:
        id (int): The ID of the business.
        business (Business): The business instance, provided by dependency injection.

    Returns:
        SuccessResponse: The response containing the result message and updated business information.
    """
    try:
        employee = Employee.get_or_none(
            Employee.business_id == business.id,
            Employee.created_by_id == None,
            Employee.email == current_user.email,
        )
        token = await WildcardAuthHelper.get_cookie_value(
            request=request, custom_subdomain=business.subdomain
        )
        auth_token = None
        if token:
            auth_token = AuthToken.get_or_none(
                AuthToken.id == token,
                AuthToken.object_type == employee.__class__.__name__,
                AuthToken.object_id == employee.id,
            )

        if not auth_token:
            auth_token = AuthToken.create(
                object_type=employee.__class__.__name__, object_id=employee.id
            )

        auth_token.update_expiration()
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token

        # Set the auth token cookie with the newly created access_token
        await WildcardAuthHelper.set_auth_token_cookie(
            request=request,
            response=response,
            access_token=access_token,
            custom_subdomain=business.subdomain,
        )
        return SuccessResponse(message="Logged in successfully.")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
