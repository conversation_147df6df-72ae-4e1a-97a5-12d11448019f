from fastapi import APIRouter, Body, Request, Response, Depends
from app.models import User, UserType, UserLoginResponse, AuthToken
from app.schema import SuccessR<PERSON>ponse
from app.exceptions import CustomValidationError
from app.validations import EmailValidate, PasswordValidate, StringValidate
from app.helper import Admin<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.tasks import UserTask
from typing import Union
from app.constants import ConstantMessages
import logging


async def get_login_response(
    request: Request,
    response: Response,
    user: User,
    message: str,
    auth_token: Union[AuthToken, None] = None,
) -> UserLoginResponse:
    from app.models.auth_token import AuthToken
    from app.models.user import UserLoginResponse
    from app.models.node import Node

    # If auth_token is not provided, create a new one
    if auth_token is None:
        auth_token = AuthToken.create(
            object_type=user.__class__.__name__, object_id=user.id
        )
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token
        # Set the auth token cookie with the newly created access_token
        await AdminAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=access_token
        )
    else:
        access_token = (
            auth_token.id
        )  # If auth_token is provided, use its id as the access_token

    # Set response message based on whether the user's email is verified
    response_message = (
        "Please verify your account first" if not user.email_verified else message
    )

    # Query the Node table for records of type "AdminNode" with no parent
    base_node_query = Node.where({"type": "AdminNode", "parent_id": None})
    # Order the query results by the order_num field in ascending order
    base_node_query = base_node_query.order_by(Node.order_num.asc())
    # Execute the query and call the info method on each result
    nodes = [record.info() for record in base_node_query]

    # Return a UserLoginResponse object with the user's details and the queried nodes
    return UserLoginResponse(
        message=response_message,
        data={
            "id": user.id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "user_type_id": user.user_type.id,
            "user_type": user.user_type.name,
            "email_verified": user.email_verified,
            "access_token": access_token,
            "nodes": nodes,
        },
    )


router = APIRouter(tags=["Admin Registration"])


@router.post(
    "/register",
    summary="Register new user",
    description="Register a new user with the provided information.",
    response_model=SuccessResponse,
)
async def register_user(
    request: Request,
    response: Response,
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "your_strong_password",
            "first_name": "John",
            "last_name": "Doe",
        },
    ),
):
    """
    Register a new user with the provided information.

    Args:
    - user_data: UserCreate - The data required to register a new user.
      - email: str - The email address of the user (required).
      - password: str - The password of the user (required).
      - first_name: str - The first name of the user (required).
      - last_name: str - The last name of the user (required).

    Returns:
    - User Registed Successfully
    """
    try:
        user_type = UserType.get_or_none(name="Free User")
        if user_type is None:
            raise ValueError("User Type must exist")

        email = EmailValidate(body.get("email"), field="Email")
        user_exist = User.get_or_none(email=email)
        if user_exist:
            if user_exist.status == 0:
                raise ValueError(
                    "Your account has been deleted. Please contact administrator"
                )
            raise ValueError("User already exist with this email")

        first_name = StringValidate(
            body.get("first_name"),
            field="First Name",
            required=True,
            max_length=100,
            strip=True,
        )
        last_name = StringValidate(
            body.get("last_name"),
            field="Last Name",
            required=False,
            max_length=100,
            strip=True,
        )
        password = PasswordValidate(
            body.get("password"), field="Password", min_length=8, max_length=20
        )

        user = User.create(
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name,
            user_type=user_type,
        )
        user.generate_otp()
        UserTask.user_verify_email.delay(user.id)
        await AdminAuthHelper.set_otp_cookie(
            request=request, response=response, email=user.email
        )
        return SuccessResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your email address"
        )
    except ValueError as e:
        logging.error(f"Exception in verify account: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Exception in verify account: {str(e)}")
        raise e
    except Exception as e:
        logging.error(f"Exception in register email: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.put(
    "/verify-account",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=UserLoginResponse,
)
async def resend_verify_email(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_email_otp_admin),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "email": "<EMAIL>",
        },
    ),
):
    # Example data to pass to the template
    try:
        otp = body.get("otp")
        # Extract data from the form
        if user.is_otp_expired():
            raise CustomValidationError(error="Otp has been expired")

        if otp == "" or otp != user.otp:
            raise CustomValidationError(error="Otp is Invalid")

        user.email_verified = True
        user.otp = ""
        user.save()
        await AdminAuthHelper.clear_otp_cookie(request=request, response=response)
        login_res = await get_login_response(
            request=request,
            response=response,
            user=user,
            message="Account Verified Successfully",
        )
        return login_res
    except ValueError as e:
        logging.error(f"Exception in verify account: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Exception in verify account: {str(e)}")
        raise e
    except Exception as e:
        logging.error(f"Exception in verify account: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/resend-verify-email",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=SuccessResponse,
)
async def resend_verify_email(
    request: Request,
    response: Response,
    user: User = Depends(AdminAuthHelper.get_email_otp_admin),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
        },
    ),
):
    # Example data to pass to the template
    try:
        # Extract data from the form
        user.generate_otp()
        UserTask.resend_otp_email.delay(
            user_id=user.id,
            resend_text="Please use this OTP to verify your email address.",
        )
        await AdminAuthHelper.set_otp_cookie(
            request=request, response=response, email=user.email
        )
        return SuccessResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your email address"
        )
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Exception in resend verify email: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)
