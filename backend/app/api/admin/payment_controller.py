from fastapi import APIRouter, Depends, HTTPException, status, Request, Body
from app.models import (
    Business,
    User,
    Payment,
    PaymentPlan,
)
from app.schema import SuccessResponse
from app.exceptions import CustomValidationError, RecordNotFoundException
from app.helper import AdminAuthHelper
from app.config import Stripe<PERSON><PERSON>
from app.config import APP_URL
from app.utils import build_subdomain_url

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/payments",
    tags=["Admin Payment API"],
)

# ------------------------------ functions ------------------------------


@router.get(
    "/plans",
    summary="Get Plan List",
    description="Payment Webhook",
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
    response_model=SuccessResponse,
)
async def get_plans():
    try:
        # plans = StripeClient.active_plans()
        records = PaymentPlan.select().where(PaymentPlan.status == 1)
        rows = [record.info() for record in records]

        return SuccessResponse(
            message="Plans retrieved successfully",
            data=rows,
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/create-payment-intent",
    summary="Payment Webhook",
    description="Payment Webhook",
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
    response_model=SuccessResponse,
)
async def create_payment_intent(
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "business_id": 1,
            "amount": 50,
        },
    ),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        amount = body.get("amount")
        business_id = body.get("business_id")

        business = Business.get_or_none(id=business_id, user_id=current_user.id)
        if not business:
            raise RecordNotFoundException(message="Business not found or is inactive")

        amount_in_cents = amount * 100
        metadata = {"business_id": business_id}

        payment_intent = StripeClient.create_payment_intent(
            amount=amount_in_cents, currency="usd", metadata=metadata
        )
        # Return the data using the defined response model
        return SuccessResponse(
            message="Statistics retrieved successfully",
            data={"client_secret": payment_intent.client_secret},
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/create-payment-session",
    summary="Payment Session",
    description="Payment Session",
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
    response_model=SuccessResponse,
)
async def create_checkout_session(
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "business_id": 1,
            "amount": 50,
        },
    ),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        amount = body.get("amount")
        business_id = body.get("business_id")
        business = Business.get_or_none(id=business_id, user_id=current_user.id)
        if not business:
            raise RecordNotFoundException(message="Business not found or is inactive")

        amount_in_cents = int(amount * 100)
        metadata = {"business_id": business_id}
        domain = f"{build_subdomain_url(APP_URL, 'admin')}/payments/{business_id}"

        session_url = StripeClient.create_checkout_session(
            amount=amount_in_cents, domain=domain, metadata=metadata
        )
        # Return the data using the defined response model
        return SuccessResponse(
            message="Session URL created successfully",
            data={"session_url": session_url},
        )
    except Exception as e:
        print(e, "e as errrr")
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/create-subscription",
    summary="create payment subscription",
    description="create payment subscription",
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
    response_model=SuccessResponse,
)
def create_subscription(
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "business_id": 1,
            "plan_id": "str",
        },
    ),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Create a new stripe payment subscription
        plan_id = body.get("plan_id")
        business_id = body.get("business_id")
        payment_method_id = body.get("payment_method_id")
        business = Business.get_or_none(id=business_id, user_id=current_user.id)
        if not business:
            raise RecordNotFoundException(message="Business not found or is inactive")

        metadata = {"business_id": business_id}

        data = StripeClient.create_subscription(
            customer_email=business.email,
            customer_name=business.name,
            plan_id=plan_id,
            payment_method_id=payment_method_id,
            metadata=metadata,
        )

        return SuccessResponse(
            message="Subscription created successfully",
            data=data,
        )
    except Exception as e:
        print(e, "e as errrr")
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/buy-plan",
    summary="Buy plan",
    description="Buy plan",
    dependencies=[Depends(AdminAuthHelper.get_current_auth_token)],
    response_model=SuccessResponse,
)
def create_subscription(
    current_user: User = Depends(AdminAuthHelper.get_admin_user),
    body: dict = Body(
        ...,
        example={
            "business_id": 1,
            "payment_plan_id": 1,
        },
    ),
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        # Create a new stripe payment subscription
        payment_plan_id = body.get("payment_plan_id")
        business_id = body.get("business_id")
        payment_method_id = body.get("payment_method_id")
        business = Business.get_or_none(id=business_id, user_id=current_user.id)
        if not business:
            raise RecordNotFoundException(message="Business not found or is inactive")

        # Fetch the payment plan
        payment_plan = PaymentPlan.get_or_none(id=payment_plan_id)
        if not payment_plan:
            raise RecordNotFoundException(message="Payment plan not found")

        metadata = {"business_id": business_id, "payment_plan_id": payment_plan_id}

        amount_in_cents = int(payment_plan.amount * 100)  # Amount in cents

        # domain = f"{build_subdomain_url(APP_URL, 'admin')}/payments/{business_id}"

        # Create a PaymentIntent with the plan amount and the payment method from the frontend
        payment_intent = StripeClient.create_payment_intent(
            amount=amount_in_cents,
            currency=payment_plan.currency,
            payment_method=payment_method_id,
            metadata=metadata,
            confirmation_method="automatic",
        )

        return SuccessResponse(
            message="Subscription created successfully",
            data={
                "client_secret": payment_intent.client_secret,
                "payment_intent_id": payment_intent.id,
                "status": payment_intent.status,
            },
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.post(
    "/webhook",
    summary="Payment Webhook",
    description="Payment Webhook",
    response_model=SuccessResponse,
)
async def stripe_payment_webhook(
    request: Request,
):
    """
    Retrieve dashboard statistics.

    Args:
    - type: Type of statistics to fetch (monthly, weekly, yearly).
    - date: The date used for fetching statistics, in YYYY-MM-DD format.
    - user: The authenticated admin user, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of stats.
    """
    try:
        payload = await request.body()
        sig_header = request.headers.get("Stripe-Signature")
        event = StripeClient.handle_webhook(payload=payload, sig_header=sig_header)

        if event["type"] == "payment_intent.succeeded":
            payment_intent = event["data"]["object"]
            Payment.save_payment(payment_intent)
        elif event["type"] == "payment_intent.payment_failed":
            payment_intent = event["data"]["object"]
        else:
            CustomValidationError(error="Unauthorized Type")

        # Return the data using the defined response model
        return SuccessResponse(message="Data saved successfully")
    except Exception as e:
        print(str(e), "e as errrr")
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
