from fastapi import APIRouter, Request, HTTPException, status, Body
from app.schema import SuccessResponse
from app.exceptions import CustomValidationError
from app.models import Permission, Node
import logging

# Create the router
router = APIRouter(prefix="/settings", tags=["Admin Settings"])


@router.get(
    "/permissions",
    summary="Admin Permission list",
    description="Retrieve a comprehensive list of permissions for Admin subdomains.",
    response_model=SuccessResponse,
)
async def permissions():
    """
    Retrieve a list of all permissions available in the system.

    This endpoint fetches all records from the Permission model, processes each
    record to obtain its detailed information, and returns the data in a structured
    response format. The purpose of this endpoint is to provide a comprehensive list
    of permissions that can be used to validate the routes accessible within Admin
    subdomains.

    Returns:
        SuccessResponse: A response object containing the list of permissions and a success message.
    """
    records = Permission.select()
    rows = [record.info() for record in records]

    return SuccessResponse(data=rows, message="Data fetched successfully")


@router.post(
    "/nodes/detail",
    summary="Admin Nodes list",
    description="Retrieve a comprehensive list of nodes for Admin subdomains.",
    response_model=SuccessResponse,
)
async def nodes(
    body: dict = Body(..., example={"unique_key": "admin_business"}),
):
    """
    Retrieve a list of all nodes available in the system.
    """
    try:
        unique_key = body.get("unique_key")

        node = Node.get_or_none(Node.unique_key == unique_key, Node.type == "AdminNode")
        if not node:
            raise CustomValidationError(error="Node must exist!")

        return SuccessResponse(
            message="Data updated successfully",
            data={
                "roles": [],
                "roles_permission": {},
                "title": node.name,
                "singular_name": node.singular_name,
                "description": node.description,
            },
        )
    except Exception as e:
        logging.error(f"Failed to get data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Failed to get data: {str(e)}",
        )
