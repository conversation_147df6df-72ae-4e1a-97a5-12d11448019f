from fastapi import APIRouter, Depends, Query, Request, HTTPException, status
from app.schema import PaginationResponse, SuccessResponse
from app.models import Opportunity, Business, BusinessJobRequest
from app.exceptions import CustomValidationError, RecordNotFoundException
from app.helper import Wildcard<PERSON><PERSON>Helper
from starlette.datastructures import UploadFile
from app.uploader import ResumeUploader
from app.utils import generate_random_string
from datetime import datetime
from app.validations.field_validations import EmailValidate, StringValidate
import logging


# @before_actions
async def get_opportunity(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Opportunity:
    """
    Retrieve an Opportunity by its ID.

    Args:
        id (int): The ID of the Opportunity to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Opportunity: The Opportunity object if found.

    Raises:
        RecordNotFoundException: If the Opportunity does not exist.
    """
    opportunity = Opportunity.get_or_none(id=id, business_id=business.id)
    if not opportunity:
        raise RecordNotFoundException(message="Job does not exist")
    return opportunity


# ------------------------------ router functions ------------------------------

router = APIRouter(
    prefix="/jobs",
    tags=["Jobs API"],
)


@router.get("", summary="List Jobs", response_model=PaginationResponse)
def get_opportunities(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of Jobs.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of Jobs per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of Jobs.
    """
    try:
        base_query = Opportunity.select().where(
            Opportunity.business_id == business.id, Opportunity.status == 1
        )

        total_records = base_query.count()
        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit).order_by(Opportunity.id.desc())
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in job list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post("/{id}/apply", summary="Apply Job", response_model=SuccessResponse)
async def apply_job(
    request: Request,
    opportunity: Opportunity = Depends(get_opportunity),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of Jobs.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of Jobs per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of Jobs.
    """
    try:
        form = await request.form()
        files = [value for _, value in form.items() if isinstance(value, UploadFile)]
        # Extract name and email from the form
        email = EmailValidate(form.get("email"), field="Email")
        name = StringValidate(
            form.get("name"), field="Name", required=True, max_length=100, strip=True
        )
        request_exist = BusinessJobRequest.get_record_exist_with_lower(
            BusinessJobRequest.email,
            email,
            business_id=business.id,
            opportunity_id=opportunity.id,
        )

        if request_exist:
            raise ValueError("your request has been already submitted")

        if not files or len(files) == 0:
            raise CustomValidationError(status_code=400, error="No file uploaded.")
        file = files[-1]
        if not file:
            raise CustomValidationError(status_code=400, error="No file uploaded.")
        print(file, "file")
        unique_subdir = f"business/{business.id}/{opportunity.id}/{int(datetime.now().timestamp())}-{generate_random_string()}"
        resume_uploader = ResumeUploader(file, file.filename, unique_subdir)
        resume_path = ""
        if resume_uploader.valid():
            res = await resume_uploader.upload()
            resume_path = res["file_path"]
        else:
            raise ValueError("Invalid resume format")

        # Insert into BusinessJobRequest
        BusinessJobRequest.create(
            name=name,
            email=email,
            resume=resume_path,
            business_id=business.id,
            opportunity_id=opportunity.id,
        )

        return SuccessResponse(
            message="Your job request has been successfully received and is under review.",
        )
    except Exception as e:
        logging.error(f"Exception in apply job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
