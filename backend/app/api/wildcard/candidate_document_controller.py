from fastapi import APIRouter, Depends, Request, HTTPException
from app.schema import SuccessResponse
from app.exceptions import RecordNotFoundException
from app.helper import Wildcard<PERSON>uthHelper
from app.models import Business, CandidateDocument, DocumentUploadLinkRequest
from datetime import datetime
import logging
from starlette.datastructures import UploadFile
from app.uploader import CandidateDocumentUploader
from app.constants import ConstantMessages


async def get_document(
    request: Request,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieves a document upload link request based on a token from the request.

    Validates the business subdomain and checks if the token is valid and not expired.
    """
    if request.headers.get("content-type").startswith("multipart/form-data"):
        form = await request.form()
    else:
        form = await request.json()
    token = form.get("token")
    document = DocumentUploadLinkRequest.get_or_none(unique_key=token)
    if (
        not document
        or document.is_expired
        or document.candidate.business_id != business.id
    ):
        raise RecordNotFoundException(message="Link has been expired or Invalid.")
    return document


# Create the router
router = APIRouter(prefix="/candidate/documents", tags=["Candidate Document API's"])


@router.post(
    "/uploads",
    summary="Upload Multiple Files",
    description="Upload multiple files and save them.",
    response_model=SuccessResponse,
)
async def upload_file(
    request: Request,
    request_document: DocumentUploadLinkRequest = Depends(get_document),
):
    """
    This endpoint allows for the upload of multiple files.

    Each file is saved to in database.
    """
    try:
        candidate = request_document.candidate
        form = await request.form()
        files = [value for _, value in form.items() if isinstance(value, UploadFile)]
        # Get the current date
        current_date = datetime.now()
        formatted_date = current_date.strftime("%d-%m-%Y")
        unique_subdir = f"{1}/documents/{formatted_date}"

        documents = []
        for file in files:
            document = CandidateDocumentUploader(file=file, upload_path=unique_subdir)
            if document.valid(exception=True):
                await document.upload()
                documents.append(document)

        for document in documents:
            CandidateDocument.create(
                candidate_id=candidate.id,
                document=document.response["file_path"],
                name=document.file.filename,
                content_type=document.content_type,
            )

        request_document.expired = True
        request_document.submitted = True
        request_document.save()
        return SuccessResponse(message="Document Saved successfully.")
    except ValueError as e:
        logging.error(f"Exception in upload file: {str(e)} 2")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logging.error(f"Exception in upload file: {str(e)} 3")
        raise HTTPException(
            status_code=500, detail=ConstantMessages.SOMETHING_WENT_WRONG
        )


@router.post(
    "/validate_link",
    summary="Validate Candidate Link",
    description="Validate the candidate document upload link.",
    response_model=SuccessResponse,
)
async def validate_link(
    request_document: DocumentUploadLinkRequest = Depends(get_document),
):
    """
    This endpoint validates the candidate document upload link.

    It checks if the link is valid and returns the associated document information.
    """

    try:
        return SuccessResponse(
            message="Document Saved successfully.", data=request_document.info()
        )
    except ValueError as e:
        logging.error(f"Exception in upload file: {str(e)} 2")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logging.error(f"Exception in upload file: {str(e)} 3")
        raise HTTPException(
            status_code=500, detail=ConstantMessages.SOMETHING_WENT_WRONG
        )
