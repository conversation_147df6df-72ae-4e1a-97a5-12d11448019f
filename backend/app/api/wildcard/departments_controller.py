from fastapi import APIRouter, Depends, Query, HTTPException, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import Department, Employee, Business
from app.validations import StringValidate
from app.exceptions import RecordNotFoundException
from app.helper import WildcardA<PERSON>Helper
from peewee import fn
from typing import Optional
import logging
from app.constants import NodeName, PermissionName

router = APIRouter(
    prefix="/departments",
    tags=["Wildcard Department API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)


# Helper function to get department by ID
async def get_department(
    id: int, business: Business = Depends(WildcardAuthHelper.validate_subdomain)
) -> Department:
    """
    Retrieve a department by its ID.

    Args:
        id (int): The ID of the department to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Department: The department object if found.

    Raises:
        RecordNotFoundException: If the department does not exist.
    """
    department = Department.get_or_none(id=id, business_id=business.id)
    if not department:
        raise RecordNotFoundException(message="Department does not exist")
    return department


# ------------------------------ router functions ------------------------------


@router.get(
    "",
    summary="List of Categories",
    description="Retrieve a paginated list of departments.",
    response_model=PaginationResponse,
)
def get_departments(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter departments"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of departments.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of departments per page. Defaults to 10.
        search (Optional[str]): Search term to filter departments.

    Returns:
        PaginationResponse: Paginated list of departments.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_DEPARTMENTS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        base_query = Department.select().where(Department.business_id == business.id)

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.lower(Department.name).contains(search))

        total_records = base_query.count()

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit).order_by(Department.id.desc())
        # Prepare department list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in department list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post(
    "",
    summary="Create New Department",
    description="Create a new department with the provided information.",
    response_model=SuccessResponse,
)
async def create_department(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "name": "Department Name",
            "description": "description",
        },
    ),
):
    """
    Endpoint to register a new department.

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing department information.

    Returns:
        SuccessResponse: Success message along with department details upon successful registration.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_DEPARTMENTS,
        perm_names=PermissionName.READ_WRITE,
    )
    try:
        # Validate department data
        department_name = StringValidate(
            body.get("name"), field="Name", required=True, max_length=100, strip=True
        )
        department_description = StringValidate(
            body.get("description"),
            field="Description",
            required=False,
            max_length=255,
            strip=True,
        )

        # Check if department already exists with the provided name and raise a Value Error
        Department.check_record_exist_with_lower(
            Department.name, department_name, business_id=current_employee.business_id
        )

        department = Department.create(
            name=department_name,
            description=department_description,
            created_by_id=current_employee.id,
            business_id=current_employee.business_id,
        )

        # Return success response with department details
        return SuccessResponse(
            message="Department Created Successfully.", data=department.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}",
    summary="Get Department",
    description="Retrieve department details.",
    response_model=SuccessResponse,
)
async def get_department_detail(
    request: Request, department: Department = Depends(get_department)
):
    """
    Endpoint to retrieve department details.

    Args:
        department (Department): The department instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with department details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_DEPARTMENTS,
        perm_names=PermissionName.READ_ONLY,
    )
    try:
        # Return success response
        return SuccessResponse(
            message="Department details fetched successfully.", data=department.info()
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@router.put(
    "/{id}",
    summary="Update Department",
    description="Update an existing department with the provided information.",
    response_model=SuccessResponse,
)
async def update_department(
    request: Request,
    department: Department = Depends(get_department),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(
        ...,
        example={
            "name": "Department Name",
            "description": "description",
        },
    ),
):
    """
    Endpoint to update an existing department.

    Args:
        id (int): The ID of the department to be updated.
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated department information.

    Returns:
        SuccessResponse: Success message upon successful update.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_DEPARTMENTS,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        # Validate department data
        department_name = StringValidate(
            body.get("name"), field="Name", required=True, max_length=100, strip=True
        )
        department_description = StringValidate(
            body.get("description"),
            field="Description",
            required=False,
            max_length=255,
            strip=True,
        )

        if department_name.lower() != department.name.lower():
            # Check if department already exists with the provided name and raise a Value Error
            Department.check_record_exist_with_lower(
                Department.name,
                department_name,
                business_id=current_employee.business_id,
            )

        department.name = department_name
        department.description = department_description

        # Save the updated department
        department.save()

        # Return success response
        return SuccessResponse(
            message="Department Updated Successfully.", data=department.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/status",
    summary="Update Department Status",
    description="Update the status of a department.",
    response_model=SuccessResponse,
)
async def update_department_status(
    request: Request,
    department: Department = Depends(get_department),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of a department.

    Args:
        id (int): The ID of the department.
        department (Department): The department instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated department information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_DEPARTMENTS,
        perm_names=PermissionName.READ_EDIT,
    )
    try:
        department_status = body.get("status")
        department.status = department_status
        department.save()

        return SuccessResponse(
            message="Status updated successfully.", data=department.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/options/all",
    summary="Get Categories as Options",
    description="Retrieve a options list of departments.",
    response_model=SuccessResponse,
)
def get_departments_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter departments"
    ),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a options list of departments..

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of departments per page. Defaults to 10.
        search (Optional[str]): Search term to filter departments.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        base_query = Department.select().where(
            (Department.business_id == business.id) & (Department.status == 1)
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.LOWER(Department.name).contains(search))

        if showId is not None:
            base_query = base_query.orwhere(Department.id == showId)
            base_query = base_query.order_by(
                fn.IF(Department.id == showId, 0, 1), Department.id.desc()
            )
        else:
            base_query = base_query.order_by(Department.id.desc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [
            {"label": record.name, "value": record.id, "disabled": record.status == 0}
            for record in records
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in department list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
