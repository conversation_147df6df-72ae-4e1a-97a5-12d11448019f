from fastapi import APIRouter, Depends, Body
from app.schema import OfferLetterContent, SuccessResponse
from app.exceptions import RecordNotFoundException, HTTPException
from app.helper import Wildcard<PERSON>uthHelper, PdfHelper
from app.models import (
    Candidate,
    Employee,
    Business,
    EmailTemplate,
    CandidateOfferLetter,
    CandidateInterview,
)
from app.uploader import OfferLetterUploader
from app.tasks import CandidateTask
from datetime import datetime
from jinja2 import Template
import logging

router = APIRouter(
    prefix="/candidates/{id}/offer_letter",
    tags=["Wildcard Candidates Offer Letter API"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_auth_token),
        Depends(WildcardAuthHelper.validate_subdomain),
    ],
)


# Helper function to get candidate by ID
async def get_candidate(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Candidate:
    """
    Retrieve a candidate by its ID.

    Args:
        id (int): The ID of the candidate to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Candidate: The candidate object if found.

    Raises:
        RecordNotFoundException: If the candidate does not exist.
    """
    candidate = Candidate.get_or_none(id=id, business_id=business.id, is_deleted=0)
    if not candidate:
        raise RecordNotFoundException(message="Candidate does not exist")

    return candidate


@router.get("/templates", response_model=SuccessResponse)
async def get_offer_letter_preview(
    candidate: Candidate = Depends(get_candidate),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Generate and return the offer letter HTML content for the given candidate.
    """
    try:
        # Fetch the email template by ID (e.g. ID = 4)
        email_template = EmailTemplate.get_or_none(id=3)
        if not email_template:
            raise RecordNotFoundException(message="EmailTemplate does not exist")
        if email_template.is_default == 0 and email_template.business_id != business.id:
            raise RecordNotFoundException(message="EmailTemplate does not exist")

        # Render the email template using Jinja2
        template = Template(email_template.email_body)
        rendered_html = template.render(
            candidate={
                "name": candidate.name,
                "email": candidate.email,
                "opportunity": {"title": candidate.Opportunity.title},
            },
            business={"name": business.name},
            current_date=datetime.utcnow().strftime("%d %B, %Y"),
        )

        # Remove unnecessary newlines for cleaner output
        rendered_html = rendered_html.replace("\n", "")

        return SuccessResponse(
            message="Offer letter template rendered successfully",
            data={"html": rendered_html},
        )

    except RecordNotFoundException as e:
        logging.warning(f"Template not found: {e}")
        raise HTTPException(status_code=404, detail="Template not found")

    except Exception as e:
        logging.exception("Unexpected error while rendering offer letter template")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/send",
    summary="Send Email Template",
    description="Send Email Template.",
    response_model=SuccessResponse,
)
async def send_offer_letter(
    candidate: Candidate = Depends(get_candidate),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    data: OfferLetterContent = Body(...),
):
    try:
        file = PdfHelper.generate_pdf_uploadfile(
            context=data.content, filename="offer_letter.pdf"
        )
        uploader = OfferLetterUploader(
            file, file.filename, f"{candidate.id}/{int(datetime.now().timestamp())}"
        )
        uploaded_path = await uploader.upload()
        can_offer_letter = CandidateOfferLetter.create(
            candidate_id=candidate.id,
            created_by_id=current_employee.id,
            opportunity_id=data.opportunity_id,
            letter=uploaded_path["file_path"],
        )

        interview = CandidateInterview.get_or_none(
            candidate_id=candidate.id,
            opportunity_id=data.opportunity_id,
        )
        interview.offer_sent = True
        interview.save()

        CandidateTask.send_offer_letter_email.delay(
            candidate.id, can_offer_letter.id, data.subject
        )
        return SuccessResponse(message="Offer letter send successfully")
    except RecordNotFoundException as e:
        logging.warning(f"Template not found: {e}")
        raise HTTPException(status_code=404, detail="Template not found")

    except Exception as e:
        logging.exception("Unexpected error while rendering offer letter template")
        raise HTTPException(status_code=500, detail="Internal server error")
