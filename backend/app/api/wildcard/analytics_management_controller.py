from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.responses import StreamingResponse
from app.schema import SuccessResponse
from app.models import (
    Business,
    Opportunity,
    Candidate,
    CandidateInterview,
    ScheduleInterview,
    CandidateOfferLetter,
)
from app.helper import WildcardAuthHelper
from app.constants import NodeName, PermissionName
from app.models.helper import Candi<PERSON><PERSON><PERSON>per
from datetime import datetime, time, date, timedelta
from typing import Optional
import csv
import io
import pandas as pd


def check_read_permission(
    request: Request,
):
    """
    Check if the user has read permission for the route.
    This function can be used as a dependency in route handlers.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_ANALYTICS_MANAGEMENT,
        perm_names=PermissionName.READ_ONLY,
    )

    return True


# Create the router
router = APIRouter(
    prefix="/analytics",
    tags=["wildcard Hiring Analytics Management"],
    dependencies=[
        Depends(WildcardAuthHelper.get_current_employee),
        Depends(check_read_permission),
    ],
)


@router.get(
    "",
    summary="List of Hiring Analytics Management Stats",
    description="List of Hiring Analytics Management Stats",
    response_model=SuccessResponse,
)
async def get_hiring_stats(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):

    try:
        job_query = Opportunity.select().where(Opportunity.business_id == business.id)

        # Query to count active jobs
        total_active_jobs = job_query.where(Opportunity.status == 1).count()

        # Query to count Registered jobs
        total_jobs = job_query.count()

        # Query to count Reg candidates
        total_candidates = (
            Candidate.select()
            .where(Candidate.business_id == business.id, Candidate.blacklist == 0)
            .count()
        )

        # Query to count shortlisted candidates
        total_hired = (
            CandidateInterview.select()
            .where(
                CandidateInterview.business_id == business.id,
                CandidateInterview.status == 3,
            )
            .count()
        )

        stats = {
            "totalJobs": total_jobs,
            "activeJobs": total_active_jobs,
            "totalCandidates": total_candidates,
            "totalHiring": total_hired,
        }
        return SuccessResponse(message="statistics fetched successfully.", data=stats)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/candidate-analytic-stats",
    summary="Retrieve Line Chart Statistics",
    description="Fetch data for the line chart, representing hiring analytics trends over time.",
    response_model=SuccessResponse,
)
def get_hired_candidates_stats(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    filter: Optional[str] = Query(
        None, description="Filter for the line chart data, e.g., [monthly, yearly]"
    ),
):
    try:
        current_date = datetime.today()
        business_id = business.id

        if filter not in ["monthly", "yearly"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid type. Must be 'weekly', 'monthly', 'daily', or 'yearly'.",
            )
        labels, counts, title, xLabel, yLabel, has_data = (
            CandidateHelper.get_shortlist_candidate_by_group(
                current_date, filter, [business_id]
            )
        )
        hired_candidates_stats = {
            "label": labels,
            "data": [counts.get(label, 0) for label in labels],
            "title": title,
            "xLabel": xLabel,
            "yLabel": yLabel,
            "has_data": has_data,
        }
        stats = {"hired_candidates_stats": hired_candidates_stats, "filter": filter}
        return SuccessResponse(message="Statistics retrieved successfully", data=stats)

    except Exception as e:
        print(e, "Error in get_hired_candidates_stats")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/candidate-status-distribution-stats",
    summary="Retrieve Line Chart Statistics",
    description="Fetch data for the line chart, representing hiring analytics trends over time.",
    response_model=SuccessResponse,
)
def get_status_distribution_stats(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    try:
        # Get candidate status counts
        pie_stats_data, title, has_data = CandidateHelper.get_candidate_status_counts(
            [business.id]
        )

        pie_stats = {
            "data": pie_stats_data,
            "title": title,
            "has_data": has_data,
        }

        return SuccessResponse(
            message="Statistics retrieved successfully", data=pie_stats
        )
    except Exception as e:
        print(e, "Error in get_hired_candidates_stats")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/export-stats",
    summary="Export Hiring Stats CSV",
    description="Returns hiring analytics stats as CSV string grouped by date",
    response_class=StreamingResponse,
)
async def export_hiring_stats_csv(
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
):
    try:
        if not start_date or not end_date:
            raise ValueError("Both start_date and end_date are required.")

        daily_stats_csv = io.StringIO()
        stats_writer = csv.writer(daily_stats_csv)
        stats_writer.writerow(
            [
                "Date",
                "Total Resumes Received",
                "Total Candidates Shortlisted",
                "Total Interviews Scheduled",
                "Total Interviews Completed",
                "Total Offers Generated",
            ]
        )

        current_date = start_date
        while current_date <= end_date:
            day_start = datetime.combine(current_date, time.min)
            day_end = datetime.combine(current_date, time.max)

            candidate_filter = (
                (Candidate.business_id == business.id)
                & (Candidate.created_at >= day_start)
                & (Candidate.created_at <= day_end)
            )

            interview_filter = (
                (CandidateInterview.business_id == business.id)
                & (CandidateInterview.created_at >= day_start)
                & (CandidateInterview.created_at <= day_end)
            )

            resumes_received = Candidate.select().where(candidate_filter).count()
            shortlisted = (
                Candidate.select()
                .where(candidate_filter, Candidate.status == 2)
                .count()
            )
            interviews_scheduled = (
                CandidateInterview.select().where(interview_filter).count()
            )

            interviews_completed = (
                CandidateInterview.select()
                .where(interview_filter, CandidateInterview.status == 3)
                .count()
            )

            offers_generated = (
                CandidateInterview.select()
                .where(interview_filter, CandidateInterview.offer_sent == 1)
                .count()
            )

            total_hires = (
                Candidate.select()
                .where(candidate_filter, Candidate.status == 5)
                .count()
            )

            stats_writer.writerow(
                [
                    current_date.isoformat(),
                    resumes_received,
                    shortlisted,
                    interviews_scheduled,
                    interviews_completed,
                    offers_generated,
                ]
            )
            current_date += timedelta(days=1)

        # --- Second CSV: Candidate Detailed Report ---
        candidate_csv = io.StringIO()
        candidate_writer = csv.writer(candidate_csv)
        candidate_writer.writerow(
            [
                "Candidate ID",
                "Candidate Name",
                "Position Applied For",
                "Application Date",
                "Job Status",
                "Interview Scheduled Date",
                "Interview Date",
                "Interview Feedback",
                "Test Score",
                "AI Screening Result",
                "Hiring Status",
                "Offer Generated Date",
                "Candidate Source",
                "Diversity Indicator",
            ]
        )

        all_candidates = Candidate.select().where(
            (Candidate.business_id == business.id)
            & (Candidate.created_at >= datetime.combine(start_date, time.min))
            & (Candidate.created_at <= datetime.combine(end_date, time.max))
        )

        for candidate in all_candidates:

            interview = (
                ScheduleInterview.select()
                .where(ScheduleInterview.candidate_id == candidate.id)
                .order_by(ScheduleInterview.created_at.desc())
                .first()
            )

            offer_letter = (
                CandidateOfferLetter.select()
                .where(CandidateOfferLetter.candidate == candidate.id)
                .first()
            )

            opportunity_title = getattr(candidate.opportunity, "title", "")
            if interview:
                opportunity_title = getattr(interview.opportunity, "title", None)

            candidate_writer.writerow(
                [
                    candidate.id,
                    candidate.name or "",
                    opportunity_title or "",
                    (
                        candidate.created_at.date().isoformat()
                        if candidate.created_at
                        else ""
                    ),
                    (
                        candidate.current_interview.status.name
                        if candidate and candidate.current_interview
                        else ""
                    ),
                    (
                        interview.interview_at.date().isoformat()
                        if interview and interview.interview_at
                        else ""
                    ),
                    (
                        interview.created_at.date().isoformat()
                        if interview and interview.created_at
                        else ""
                    ),
                    interview.feedback if interview else "",
                    interview.score if interview else "",
                    interview.status.name if interview and interview.status else "",
                    candidate.status or "",
                    (
                        offer_letter.created_at.isoformat()
                        if offer_letter and offer_letter.created_at
                        else ""
                    ),
                    candidate.resume_source or "",
                    candidate.gender or "",
                ]
            )

        daily_stats_csv.seek(0)
        candidate_csv.seek(0)
        df_stats = pd.read_csv(daily_stats_csv)
        df_candidates = pd.read_csv(candidate_csv)

        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine="xlsxwriter") as writer:
            df_stats.to_excel(writer, sheet_name="Daily Stats", index=False)
            df_candidates.to_excel(writer, sheet_name="Candidate Stats", index=False)
        excel_buffer.seek(0)

        headers = {"Content-Disposition": "attachment; filename=analytic-stats.xlsx"}
        return StreamingResponse(
            excel_buffer,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers=headers,
        )

    except Exception as e:
        print(e, "Error generating CSV: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
