from fastapi import APIRouter, Body, Response, Depends, Request
from app.models import Employee, EmployeeLoginResponse, AuthToken, Business, Node
from app.schema import MessageResponse, SuccessResponse
from app.exceptions import CustomValidationError, UnauthorizedException
from app.validations import PasswordValidate
from app.helper import Wild<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.tasks import EmployeeTask
from typing import Union
from app.constants import ConstantMessages
import logging


async def get_login_response(
    request: Request,
    response: Response,
    employee: Employee,
    message: str,
    auth_token: Union[AuthToken, None] = None,
) -> EmployeeLoginResponse:
    # If auth_token is not provided, create a new one
    if auth_token is None:
        auth_token = AuthToken.create(
            object_type=employee.__class__.__name__, object_id=employee.id
        )
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token
        # Set the auth token cookie with the newly created access_token
        await WildcardAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=access_token
        )
    else:
        access_token = (
            auth_token.id
        )  # If auth_token is provided, use its id as the access_token

    # Fetch permissions and nodes associated with the employee's role and business
    nodes = Node.get_permissions_with_employee_nodes(
        employee_role_id=employee.employee_role_id, business_id=employee.business_id
    )

    # Return an EmployeeLoginResponse object with the employee's details and the fetched nodes
    return EmployeeLoginResponse(
        message=message,
        data={
            "id": employee.id,
            "email": employee.email,
            "first_name": employee.first_name,
            "last_name": employee.last_name,
            "employee_role_id": employee.employee_role_id,
            "employee_role": employee.employee_role.name,
            "business_id": employee.business_id,
            "business_name": employee.business.name,
            "business_contact": employee.business.contact_number,
            "contact": employee.contact_number,
            "access_token": access_token,
            "nodes": nodes,
        },
    )


router = APIRouter(prefix="/auth", tags=["Wildcard Subdomain Authentication"])


@router.post(
    "/login",
    summary="Employee Login",
    description="Employee Login with Email and Password.",
    response_model=EmployeeLoginResponse,
)
async def login(
    request: Request,
    response: Response,
    current_business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "password",
        },
    ),
):
    """
    Endpoint for employee login.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing email and password.

    Returns:
        EmployeeLoginResponse: The response containing employee data and login status.
    """
    try:
        email = body.get("email")
        password = body.get("password")

        employee = await WildcardAuthHelper.authenticate_employee(
            current_business.id, email, password
        )

        if not employee:
            raise UnauthorizedException(error="Incorrect username or password.")

        login_res = await get_login_response(
            request=request,
            response=response,
            employee=employee,
            message="Logged in successfully",
        )
        return login_res
    except UnauthorizedException as e:
        raise e
    except Exception as e:
        logging.error(f"Login Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/forgot-password",
    summary="Forgot Password",
    description="Forgot Password.",
    response_model=MessageResponse,
)
async def forgot_password(
    request: Request,
    response: Response,
    current_business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to handle forgot password requests.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing the email.

    Returns:
        MessageResponse: The response indicating the result of the forgot password request.
    """
    try:
        email = body.get("email")

        employee = Employee.get_or_none(
            email=email, status=1, business_id=current_business.id
        )
        if employee is None:
            raise CustomValidationError(
                error="Employee with this email does not exist."
            )

        employee.generate_otp()
        EmployeeTask.reset_password_email.delay(employee.id)
        await WildcardAuthHelper.set_otp_cookie(
            request=request, response=response, email=employee.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your email address."
        )
    except ValueError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/verify-forgot-password-otp",
    summary="Verify OTP",
    description="Verify OTP.",
    response_model=MessageResponse,
)
async def verify_forgot_password_otp(
    request: Request,
    response: Response,
    employee: Employee = Depends(WildcardAuthHelper.get_email_otp_employee),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to verify the OTP for forgotten password.

    Args:
        response (Response): The response object to set cookies.
        employee (Employee): The employee instance, provided by dependency injection.
        body (dict): The request body containing the OTP and email.

    Returns:
        MessageResponse: The response indicating the result of the OTP verification.
    """
    try:
        otp = body.get("otp")

        if employee.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != employee.otp:
            raise CustomValidationError(error="OTP is invalid")

        await WildcardAuthHelper.set_otp_cookie(
            request=request, response=response, email=employee.email
        )
        return MessageResponse(message="OTP verified successfully.")
    except ValueError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/resend-forgot-password-otp",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=MessageResponse,
)
async def resend_verify_email(
    request: Request,
    response: Response,
    employee: Employee = Depends(WildcardAuthHelper.get_email_otp_employee),
):
    """
    Endpoint to resend the OTP for password reset.

    Args:
        response (Response): The response object to set cookies.
        employee (Employee): The employee instance, provided by dependency injection.
        body (dict): The request body containing the OTP.

    Returns:
        MessageResponse: The response indicating the result of the OTP resend operation.
    """
    try:
        employee.generate_otp()
        EmployeeTask.resend_otp_email.delay(
            employee_id=employee.id,
            resend_text="Please use this OTP to reset your password.",
        )
        await WildcardAuthHelper.set_otp_cookie(
            request=request, response=response, email=employee.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to reset your password."
        )
    except Exception as e:
        logging.error(f"Resend Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/change-password",
    summary="Update Password",
    description="Update Password.",
    response_model=EmployeeLoginResponse,
)
async def change_password(
    request: Request,
    response: Response,
    employee: Employee = Depends(WildcardAuthHelper.get_email_otp_employee),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "password": "123456",
            "confirm_password": "123456",
        },
    ),
):
    """
    Endpoint to update the employee's password.

    Args:
        response (Response): The response object to set cookies.
        employee (Employee): The employee instance, provided by dependency injection.
        body (dict): The request body containing the OTP, new password, and confirm password.

    Returns:
        MessageResponse: The response indicating the result of the password update.
    """
    try:
        otp = body.get("otp")

        if employee.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != employee.otp:
            raise CustomValidationError(error="OTP is invalid")

        password = body.get("password")
        confirm_password = body.get("confirm_password")

        password_hash = PasswordValidate(
            password, field="Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Password", min_length=8, max_length=20
        )

        if password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        employee.password = password_hash
        employee.save()
        # Assuming `set_otp_cookie` handles the logic for resetting OTP or related operations.
        await WildcardAuthHelper.clear_otp_cookie(request=request, response=response)

        login_res = await get_login_response(
            request=request,
            response=response,
            employee=employee,
            message="Password Changed successfully",
        )
        return login_res
    except CustomValidationError as e:
        raise e
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Change Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/me",
    summary="Verify Current Employee",
    description="Verify Current Employee",
    response_model=EmployeeLoginResponse,
)
async def verify_employee(
    request: Request,
    response: Response,
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    auth_token: AuthToken = Depends(WildcardAuthHelper.get_current_auth_token),
):
    """
    Endpoint to verify the current employee.

    Args:
        response (Response): The response object to set cookies.
        employee (Employee): The current employee, provided by dependency injection.

    Returns:
        EmployeeLoginResponse: The response containing employee data and login status.
    """
    try:
        auth_token.update_expiration()
        await WildcardAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=auth_token.id
        )

        login_res = await get_login_response(
            request=request,
            response=response,
            employee=employee,
            message="Logged in successfully",
            auth_token=auth_token,
        )
        return login_res
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.put(
    "/update-password",
    summary="Update Current Employee Password",
    description="Update the password for the current employee",
    response_model=SuccessResponse,
)
async def update_password(
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    body: dict = Body(...),
):
    """
    Endpoint to update the current employee's password.

    Args:
        employee (Employee): The current employee, provided by dependency injection.
        body (UpdatePasswordRequest): Request body containing old and new passwords.

    Returns:
        SuccessResponse: The response containing status and optional data.
    """
    try:
        old_password = body.get("old_password")
        new_password = body.get("new_password")
        confirm_password = body.get("confirm_password")

        # Check if the old password matches
        if not employee.match_password(old_password):
            raise CustomValidationError(error="Old password does not match.")

        password_hash = PasswordValidate(
            new_password, field="New Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Confirm Password", min_length=8, max_length=20
        )

        if new_password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        employee.password = password_hash
        employee.save()

        return SuccessResponse(message="Password updated successfully")
    except CustomValidationError as e:
        raise e
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.delete(
    "/logout",
    summary="Logout Current Employee",
    description="Logout the current employee by deleting the authentication token.",
    response_model=MessageResponse,
)
async def logout(
    request: Request,
    response: Response,
    auth_token: AuthToken = Depends(WildcardAuthHelper.get_current_auth_token),
):
    """
    Logout the current employee by deleting the authentication token.

    - **auth_token**: The authentication token associated with the current employee.
    """

    # Delete the authentication token instance from the database
    await WildcardAuthHelper.clear_auth_cookie(request=request, response=response)
    try:
        auth_token.delete_instance()
    except Exception as e:
        logging.error(f"Exception in delete auth token: {str(e)}")

    # Return a success message
    return MessageResponse(message="Logged out successfully.")
