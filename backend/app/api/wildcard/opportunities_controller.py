from fastapi import APIRout<PERSON>, Depends, Query, HTTPException, status, Body, Request
from app.schema import PaginationResponse, SuccessResponse
from app.models import (
    Candidate,
    Opportunity,
    Employee,
    Business,
    Location,
    ApiKeyUsage,
    CandidateInterview,
    ResumeExtractor,
    ScheduleInterview,
)
from app.exceptions import RecordNotFoundException
from app.helper import (
    WildcardAuthHelper,
    RequestHeaderHelper,
)
from extraction_modules.process_job import JobProcessor
from typing import Optional
from peewee import fn, JOIN
from app.utils import AppTemplates
from app.constants import NodeName, PermissionName
from app.tasks.opportunity_task import OpportunityTask
import logging
import json


# @before_actions
async def get_opportunity(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
) -> Opportunity:
    """
    Retrieve an Opportunity by its ID.

    Args:
        id (int): The ID of the Opportunity to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Opportunity: The Opportunity object if found.

    Raises:
        RecordNotFoundException: If the Opportunity does not exist.
    """
    opportunity = Opportunity.get_or_none(id=id, business_id=business.id)
    if not opportunity:
        raise RecordNotFoundException(message="Opportunity does not exist")
    return opportunity


# @before_actions
async def get_user_opportunity(
    id: int,
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
) -> Opportunity:
    """
    Retrieve an Opportunity by its ID.

    Args:
        id (int): The ID of the Opportunity to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Opportunity: The Opportunity object if found.

    Raises:
        RecordNotFoundException: If the Opportunity does not exist.
    """
    opportunity = Opportunity.get_or_none(id=id, business_id=business.id)
    if not opportunity:
        raise RecordNotFoundException(message="Opportunity does not exist")
    if (
        not current_employee.is_admin
        and opportunity.created_by_id != current_employee.id
    ):
        raise RecordNotFoundException(message="Not auuthorized to do this action")
    return opportunity


def set_opportunity_params(opportunity_data: dict, business: Business):
    location_ids = opportunity_data.get("location")

    all_locations = []
    for location_id in location_ids:
        locations = Location.get_or_none(
            Location.id == location_id,
            Location.business_id == business.id,
            Location.is_deleted == 0,
        )
        if not locations:
            raise ValueError(f"Location with id {location_id} must exist")
        all_locations.append(location_id)
    opportunity_data["location"] = all_locations
    opportunity_data["salary"] = opportunity_data.get("salary") or 0
    opportunity_data["experience"] = opportunity_data.get("experience") or 0
    opportunity_data["extra_fields"] = opportunity_data.get("extra_fields") or {}
    return opportunity_data


async def process_opportunity(
    request: Request, opportunity: Opportunity, business_id: int, employee_id: int
):
    # get question for that job.
    try:
        request_data = RequestHeaderHelper.get_request_ip_basic(request)
        info_json = json.dumps(request_data, ensure_ascii=False)
        OpportunityTask.extract_opportunity_details.delay(
            opportunity_id=opportunity.id,
            business_id=business_id,
            employee_id=employee_id,
            request_data=info_json,
        )
    except Exception as e:
        print(e, "exception")


async def create_job_descriptions(
    request: Request, business_id: int, employee_id: int, job_info: dict
):
    # get the job description and roles &  responsibilities to create a job
    try:
        request_data = RequestHeaderHelper.get_request_ip_basic(request)
        default_usage_dict: dict = dict(
            {
                "business_id": business_id,
                "created_by_id": employee_id,
                "response_status": "in-progress",
                **request_data,
            }
        )
        db_model = ApiKeyUsage(**default_usage_dict)
        cv_processor = JobProcessor(db_model=db_model)
        response = await cv_processor.creating_job_description(job_info)

        return response

    except Exception as e:
        print(e, "exception")
        return ""


# ------------------------------ router functions ------------------------------

router = APIRouter(
    prefix="/opportunities",
    tags=["Opportunity API"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)


@router.get("", summary="List Opportunities", response_model=PaginationResponse)
def get_opportunities(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter opportunity"
    ),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve a paginated list of Opportunities.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of Opportunities per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of Opportunities.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        base_query = Opportunity.select().where(Opportunity.business_id == business.id)

        total_records = base_query.count()
        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit).order_by(Opportunity.id.desc())
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in opportunity list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post("", summary="Create Opportunity", response_model=SuccessResponse)
async def create_opportunity(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    opportunity_data: dict = Body(...),
):
    """
    Endpoint to register a new Opportunity.

    Args:
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing Opportunity information.

    Returns:
        SuccessResponse: Success message along with Opportunity details upon successful registration.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_WRITE,
    )

    try:
        business_id = current_employee.business_id
        # Validate title and ensure it is unique
        title = opportunity_data.get("title")
        opportunity_exist = Opportunity.get_record_exist_with_lower(
            Opportunity.title, title, business_id=business.id
        )

        if opportunity_exist:
            raise ValueError("Opportunity already exists with this title")

        contact_person_id = opportunity_data.get("contact_person_id")
        contact_person = Employee.get_or_none(
            id=contact_person_id, business_id=business_id, status=1
        )
        if not contact_person:
            raise ValueError("Contact Person must exist")

        opportunity_data = set_opportunity_params(opportunity_data, business)

        opportunity = Opportunity.create(
            **opportunity_data,
            business_id=current_employee.business_id,
            created_by_id=current_employee.id,
        )

        await process_opportunity(
            request=request,
            opportunity=opportunity,
            business_id=business.id,
            employee_id=current_employee.id,
        )

        return SuccessResponse(
            message="Opportunity created successfully.", data=opportunity.info()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get("/{id}", summary="Get Opportunity by ID", response_model=SuccessResponse)
async def get_opportunity_detail(
    request: Request,
    opportunity: Opportunity = Depends(get_opportunity),
):
    """
    Endpoint to retrieve Opportunity details.

    Args:
        opportunity (Opportunity): The Opportunity instance, provided by dependency injection.

    Returns:
        SuccessResponse: Success message along with Opportunity details.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_ONLY,
    )

    try:
        return SuccessResponse(
            message="Opportunity details fetched successfully.", data=opportunity.info()
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put("/{id}", summary="Update Opportunity", response_model=SuccessResponse)
async def update_opportunity(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    opportunity: Opportunity = Depends(get_user_opportunity),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    opportunity_data: dict = Body(...),
):
    """
    Endpoint to update an existing Opportunity.

    Args:
        opportunity (Opportunity): The Opportunity instance to be updated, provided by dependency injection.
        current_employee (Employee): The current employee, provided by dependency injection.
        body (dict): The request body containing updated Opportunity information.

    Returns:
        SuccessResponse: Success message upon successful update.
    """

    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        contact_person_id = opportunity_data.get("contact_person_id")
        contact_person = Employee.get_or_none(
            id=contact_person_id, business_id=business.id, status=1
        )
        title = opportunity_data.get("title")
        opportunity_exist = Opportunity.get_record_exist_with_lower(
            Opportunity.title, title, business_id=business.id
        )

        if opportunity_exist and opportunity_exist.id != opportunity.id:
            raise ValueError("Opportunity already exists with this title")

        opportunity_data = set_opportunity_params(opportunity_data, business)
        if not contact_person:
            raise ValueError("Contact Person must exist")

        for field, value in opportunity_data.items():
            opportunity.set_value(field, value)

        if opportunity.is_dirty():
            opportunity.save()

        # Send email to candidate
        if opportunity.is_changed:
            await process_opportunity(
                request=request,
                opportunity=opportunity,
                business_id=business.id,
                employee_id=current_employee.id,
            )

        return SuccessResponse(
            message="Opportunity updated successfully.", data=opportunity.info()
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.put(
    "/{id}/status", summary="Update Opportunity Status", response_model=SuccessResponse
)
def update_opportunity_status(
    request: Request,
    opportunity: Opportunity = Depends(get_user_opportunity),
    body: dict = Body(..., example={"status": 1}),
):
    """
    Update the status of an Opportunity.

    Args:
        opportunity (Opportunity): The Opportunity instance, provided by dependency injection.
        body (dict): The request body containing the new status.

    Returns:
        SuccessResponse: The response containing the result message and updated Opportunity information.
    """
    WildcardAuthHelper.has_route_permission(
        request=request,
        node_key=NodeName.EMPLOYEE_OPPORTUNITIES,
        perm_names=PermissionName.READ_EDIT,
    )

    try:
        opportunity_status = body.get("status")
        opportunity.status = opportunity_status
        opportunity.save(method="status_update_check")

        return SuccessResponse(
            message="Opportunity Status updated successfully.", data=opportunity.info()
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/options/all",
    summary="Get Opportunity as Options",
    description="Retrieve an options list of opportunities.",
    response_model=SuccessResponse,
)
def get_opportunities_options(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter opportunities"
    ),
    showId: Optional[int] = Query(None, description="ID to prioritize in the ordering"),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    Retrieve an options list of opportunities.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of opportunities per page. Defaults to 10.
        search (Optional[str]): Search term to filter opportunities.
        showId (Optional[int]): ID to prioritize in the ordering.
    Returns:
    """
    try:
        base_query = Opportunity.select().where(
            (Opportunity.business_id == business.id) & (Opportunity.status == 1)
        )

        if search:
            search = search.strip().lower()
            base_query = base_query.where(fn.LOWER(Opportunity.title).contains(search))

        if showId is not None:
            base_query = base_query.orwhere(Opportunity.id == showId)
            base_query = base_query.order_by(
                fn.IF(Opportunity.id == showId, 0, 1), Opportunity.title.asc()
            )
        else:
            base_query = base_query.order_by(Opportunity.title.asc())

        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit)
        rows = [
            {"label": record.title, "value": record.id, "disabled": record.status == 0}
            for record in records
        ]

        return SuccessResponse(data=rows, message="Options fetched successfully")
    except Exception as e:
        logging.error(f"Exception in employee list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")


@router.get(
    "/{id}/email/template", summary="Get Email Template", response_model=SuccessResponse
)
def get_opportunity_email_template(
    request: Request, opportunity: Opportunity = Depends(get_opportunity)
):
    """
    Retrieve and render an email template for a given Opportunity.

    Args:
        opportunity (Opportunity): The Opportunity instance provided by dependency injection.

    Returns:
        SuccessResponse: A JSON response containing the subject and body of the rendered email template.

    Raises:
        HTTPException: If the Opportunity record is not found or any other error occurs during processing.
    """
    try:
        subject = opportunity.title
        context = {"request": None, "opportunity": opportunity}
        body = AppTemplates.get_template("opportunity/email_template.html").render(
            context
        )
        return SuccessResponse(
            message="Opportunity Status updated successfully.",
            data={"subject": subject, "body": body},
        )
    except RecordNotFoundException as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}/shorlisted/candidates",
    summary="List of finalized candidates",
    description="List of candidates who'd cleared all rounds of interview and offered for perticular business",
    response_model=PaginationResponse,
)
def candidates_cleared(
    request: Request,
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    opportunity: Opportunity = Depends(get_opportunity),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    try:
        offset = (page - 1) * limit
        base_query = (
            CandidateInterview.select()
            .join(
                Candidate,
                JOIN.LEFT_OUTER,
                on=(Candidate.id == CandidateInterview.candidate_id),
            )
            .where(
                Candidate.business_id == business.id,
                Candidate.is_deleted == 0,
                CandidateInterview.opportunity_id == opportunity.id,
                CandidateInterview.status.in_([1, 3, 4]),
            )
        )

        total_results = base_query.count()
        data = []
        records = base_query.offset(offset).limit(limit)

        data = [record.info() for record in records]

        has_shortlist = opportunity.finalize_candidates.where(
            CandidateInterview.offer_sent == False
        ).exists()

        return PaginationResponse(
            message="Finalized candidates fetched successfully",
            data={
                "page": page,
                "limit": limit,
                "count": total_results,
                "rows": data,
            },
            meta={
                "has_shortlist": has_shortlist,
            },
        )
    except Exception as e:
        logging.error(f"Exception in candidate finalized list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.post(
    "/generate/job_description",
    summary="Create the Job Description and Roles & Responsibilities",
    description="This end point api create a job description and roles & responsibilities automatically by the taking the job title and year of experince",
    response_model=SuccessResponse,
)
async def create_job_description(
    request: Request,
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(...),
):
    try:
        response = await create_job_descriptions(
            request=request,
            business_id=business.id,
            employee_id=current_employee.id,
            job_info=body,
        )

        return SuccessResponse(
            message="Opportunity Job Description create Successfully.", data=response
        )

    except Exception as e:
        logging.error(f"Error while creating job description: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{id}/top/candidate",
    summary="Create the Job Description and Roles & Responsibilities",
    description="This end point api create a job description and roles & responsibilities automatically by the taking the job title and year of experince",
    response_model=SuccessResponse,
)
async def top_candidates_per_opportunity(
    request: Request,
    opportunity: Opportunity = Depends(get_opportunity),
    current_employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    try:
        # Fetch the opportunity / job profile
        if not opportunity:
            raise HTTPException(status_code=404, detail="Opportunity not found.")

        finalized_candidates = opportunity.finalize_candidates.where(
            CandidateInterview.offer_sent == False
        )

        if not finalized_candidates.exists():
            raise HTTPException(
                status_code=404,
                detail="No Finalize Candidates found for this opportunity.",
            )

        candidate_profiles = []
        for candidate in finalized_candidates:
            logging.info(f"Processing candidate {candidate.id}")

            resume = ResumeExtractor.get_or_none(
                ResumeExtractor.email == candidate.email,
                ResumeExtractor.business_id == business.id,
            )

            resume_data = resume.data if resume else {}

            interviews = ScheduleInterview.select().where(
                ScheduleInterview.candidate_id == candidate.id,
                ScheduleInterview.opportunity_id == opportunity.id,
            )

            interview_feedbacks = [
                {"round": i.interview_round, "score": i.score, "feedback": i.feedback}
                for i in interviews
            ]

            candidate_profiles.append(
                {
                    "id": candidate.id,
                    "name": candidate.name,
                    "designation": candidate.designation,
                    "email": candidate.email,
                    "resume": resume_data,
                    "interview_feedbacks": interview_feedbacks,
                }
            )

        # Build job profile details from Opportunity
        job_profile = {
            "id": opportunity.id,
            "title": opportunity.title,
            "designation": opportunity.designation,
            "description": opportunity.description,
            "responsibilities": opportunity.responsibilities,
            "salary": opportunity.salary,
            "experience": opportunity.experience,
        }

        request_data = RequestHeaderHelper.get_request_ip_basic(request)
        default_usage_dict: dict = dict(
            {
                "business_id": business.id,
                "created_by_id": current_employee.id,
                "response_status": "in-progress",
                **request_data,
            }
        )
        db_model = ApiKeyUsage(**default_usage_dict)

        selector = JobProcessor(db_model=db_model)

        top_candidate_result = await selector.get_top_candidate_from_openai(
            job_profile, candidate_profiles
        )

        # Extract the top_candidate_id from the result
        top_candidate_id = top_candidate_result.get("top_candidate_id")

        if not top_candidate_id:
            raise HTTPException(
                status_code=500, detail="Could not determine the top candidate."
            )

        # Optionally, fetch the Candidate object again
        top_candidate = Candidate.get_or_none(Candidate.id == top_candidate_id)

        if not top_candidate:
            raise HTTPException(
                status_code=404, detail="Top candidate not found in the database."
            )

        top_candidate_data = [top_candidate.info()]

        return SuccessResponse(
            message="Top candidates fetched successfully",
            success=True,
            data=top_candidate_data,
        )

    except Exception as e:
        logging.error(
            f"Error in top_candidates_per_opportunity: {str(e)}", exc_info=True
        )
        raise HTTPException(status_code=500, detail="Internal server error")
