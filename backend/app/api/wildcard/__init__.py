# Import all routers from the API directory
from app.api.wildcard.auth_controller import router as auth_router
from app.api.wildcard.health_check import router as health_check_router
from app.api.wildcard.resume_extractor_controller import (
    router as resume_extractor_router,
)
from app.api.wildcard.departments_controller import router as departments_router
from app.api.wildcard.employee_roles_controller import router as employee_roles_router
from app.api.wildcard.employees_controller import router as employees_router
from app.api.wildcard.opportunities_controller import router as opportunities_router
from app.api.wildcard.candidates_controller import router as candidates_router
from app.api.wildcard.candidate_interviews_controller import (
    router as candidate_interview_router,
)
from app.api.wildcard.dashboard_controller import router as dashboard_router
from app.api.wildcard.candidate_document_controller import (
    router as candidate_document_router,
)
from app.api.wildcard.setting_controller import router as setting_router
from app.api.wildcard.interviews_controller import router as interview_router
from app.api.wildcard.locations_controller import router as locations_router
from app.api.wildcard.email_templates_controller import router as email_template_router
from app.api.wildcard.jobs_controller import router as jobs_router
from app.api.wildcard.job_requests_controller import router as job_requests_router
from app.api.wildcard.businesses_controller import router as businesses_router
from app.api.wildcard.payment_controller import router as payment_router
from app.api.wildcard.candidate_offer_letter_controller import (
    router as candidate_offer_letter_controller,
)
from app.api.wildcard.analytics_management_controller import router as analytics_router

from fastapi import APIRouter, Depends
from app.helper import WildcardAuthHelper

# Create a new APIRouter instance to combine all routers
wildcard_router = APIRouter(
    prefix="/{subdomain}", dependencies=[Depends(WildcardAuthHelper.validate_subdomain)]
)

# Include all routes from regihealth_checkstration
wildcard_router.include_router(health_check_router)

# Include all routes from auth
wildcard_router.include_router(auth_router)

# Include all routes from departments
wildcard_router.include_router(departments_router)

# Include all routes from employee roles management
wildcard_router.include_router(employee_roles_router)

# Include all routes from employees management
wildcard_router.include_router(employees_router)

# Include all routes from auth
wildcard_router.include_router(resume_extractor_router)

# Include all routes from opportunities
wildcard_router.include_router(opportunities_router)

# Include all routes from candidates
wildcard_router.include_router(candidates_router)

# Include all routes from candidates documents
wildcard_router.include_router(candidate_document_router)

# Include all routes from settings
wildcard_router.include_router(setting_router)

# Include all routes from interview
wildcard_router.include_router(interview_router)

# Include all routes from candidate interview
wildcard_router.include_router(candidate_interview_router)

# Include all routes from dashboard
wildcard_router.include_router(dashboard_router)

# Include all routes from email templates
wildcard_router.include_router(email_template_router)

# Include all routes from locations
wildcard_router.include_router(locations_router)

# Include all routes from jobs
wildcard_router.include_router(jobs_router)

# Include all routes from job requests
wildcard_router.include_router(job_requests_router)

# Include all routes from job requests
wildcard_router.include_router(businesses_router)

# Include all routes from payments
wildcard_router.include_router(payment_router)

# Include all routes from candidate_offer_letter_controller
wildcard_router.include_router(candidate_offer_letter_controller)

# Include all routes from session
# api_router.include_router(session.router)

# Include all routes from hiring analytics
wildcard_router.include_router(analytics_router)
