from fastapi import APIRouter, Depends, Body, Request, HTTPException
from app.schema import SuccessResponse
from app.exceptions import CustomValidationError, RecordNotFoundException
from app.helper import <PERSON>card<PERSON><PERSON><PERSON><PERSON><PERSON>, ResumeExtractor<PERSON><PERSON>per, RequestHeader<PERSON>elper
from app.models import (
    Employee,
    Candidate,
    CandidateEducationInformation,
    CandidateExperience,
    CandidateSkill,
    Qualification,
    CandidatesQualification,
    ResumeExtractor,
    Business,
    ApiKeyUsage,
    BusinessJobRequest,
    Opportunity,
)
from app.utils import generate_random_string
from extraction_modules.process_cv import CVProcessor
from datetime import datetime
from app.uploader import ResumeUploader
from database.connection import db
import logging
from starlette.datastructures import UploadFile
from app.constants import ConstantMessages
import json
from typing import Optional
from app.tasks import CandidateTask


# save resume
def save_candidate(
    resume_extractor: ResumeExtractor,
    opportunity_id: Optional[int] = None,
    resume_source: Optional[str] = None,
):
    resume = resume_extractor.data_object
    employee_id = resume_extractor.created_by_id
    business_id = resume_extractor.business_id
    resume_object = ResumeExtractorHelper(resume)
    candidate_params = resume_object.extract_basic_detail()
    candidate_params["created_by_id"] = employee_id
    candidate_params["business_id"] = business_id
    candidate_params["resume"] = resume_extractor.resume
    candidate_params["email"] = resume_extractor.email
    candidate_params["opportunity_id"] = opportunity_id
    candidate_params["resume_source"] = resume_source

    # raise exception if exist
    candidate = Candidate.get_record_exist_with_lower(
        Candidate.email, candidate_params["email"], business_id=business_id
    )
    if candidate:
        candidate.is_deleted = False
        candidate.save()
        Candidate.update(**candidate_params).where(
            Candidate.id == candidate.id
        ).execute()
        CandidatesQualification.delete().where(
            CandidatesQualification.candidate_id == candidate.id
        ).execute()
        CandidateSkill.update(is_deleted=True).where(
            CandidateSkill.candidate_id == candidate.id
        ).execute()
        CandidateEducationInformation.update(is_deleted=True).where(
            CandidateEducationInformation.candidate_id == candidate.id
        ).execute()
        CandidateExperience.update(is_deleted=True).where(
            CandidateExperience.candidate_id == candidate.id
        ).execute()
    else:
        candidate = Candidate.create(**candidate_params)

    candidate_id = candidate.id

    candidate_qualifications = resume_object.extract_candidate_qualification()
    if candidate_qualifications and len(candidate_qualifications) > 0:
        for name in candidate_qualifications:
            qualifications_params = []
            qualification = Qualification.find_or_create_by_name(
                name, business_id=business_id
            )
            qualifications_params.append(
                {"qualification_id": qualification.id, "candidate_id": candidate_id}
            )
        CandidatesQualification.insert_many(qualifications_params).execute()

    candidate_skills_params = resume_object.extract_skills()
    candidate_skills_params = [
        {**record, "candidate_id": candidate_id} for record in candidate_skills_params
    ]
    CandidateSkill.insert_many(candidate_skills_params).execute()

    candidate_educations_params = resume_object.extract_education_info()
    for record in candidate_educations_params:
        qualification = Qualification.find_or_create_by_name(record["degree"])
        record["candidate_id"] = candidate_id
        record["qualification_id"] = qualification.id

    CandidateEducationInformation.insert_many(candidate_educations_params).execute()

    candidate_experiences_params = resume_object.extract_experiences()
    for record in candidate_experiences_params:
        record["candidate_id"] = candidate_id
        if isinstance(record["responsibilities"], list):
            record["responsibilities"] = json.dumps(record["responsibilities"])
        else:
            record["responsibilities"] = json.dumps([])

    CandidateExperience.insert_many(candidate_experiences_params).execute()

    CandidateTask.extract_candidate_scores.delay(candidate_id=candidate.id)
    return candidate.id


# Create the router
router = APIRouter(
    prefix="/resumes",
    tags=["Resume API's"],
    dependencies=[Depends(WildcardAuthHelper.get_current_employee)],
)


@router.post(
    "/upload",
    summary="Upload Multiple Files",
    description="Uploads multiple files and saves them to the local directory.",
    response_model=SuccessResponse,
)
async def upload_file(
    request: Request,
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
):
    """
    This endpoint allows for the upload of multiple files.

    Each file is saved to the local directory specified in UPLOAD_DIRECTORY.
    """

    try:
        business_id = employee.business_id
        form = await request.form()
        opportunity_id: Optional[int] = form.get("opportunity_id")
        resume_source: str = form.get("resume_source")
        files = [value for _, value in form.items() if isinstance(value, UploadFile)]
        request_data = RequestHeaderHelper.get_request_ip_basic(request)

        if not files or len(files) == 0:
            raise CustomValidationError(status_code=400, error="No file uploaded.")

        unique_subdir = f"{employee.id}-{int(datetime.now().timestamp())}-{generate_random_string()}"
        resume_files = []

        if opportunity_id:
            opportunity = Opportunity.get_or_none(
                id=opportunity_id, business_id=business_id
            )
            if not opportunity:
                raise CustomValidationError(status_code=400, error="Job must exist.")

        for file in files:
            resume_uploader = ResumeUploader(file, file.filename, unique_subdir)
            if resume_uploader.valid():
                res = await resume_uploader.upload()
                resume_files.append(res)

        if not resume_files or len(resume_files) == 0:
            raise CustomValidationError(
                status_code=400, error="No valid file uploaded."
            )

        default_usage_dict: dict = dict(
            {
                "business_id": business_id,
                "created_by_id": employee.id,
                "response_status": "in-progress",
                **request_data,
            }
        )

        db_model = ApiKeyUsage(**default_usage_dict)
        cv_processor = CVProcessor(db_model=db_model)

        resumes_data = await cv_processor.process_and_standardize_resumes(resume_files)

        candidate_ids = []
        resume_extractors = set()
        records = []

        print(resumes_data, "resumes_data")

        for resume in resumes_data:
            with db.atomic():
                resume_object = ResumeExtractorHelper(resume)
                candidate_params = resume_object.extract_basic_detail()
                extractor = ResumeExtractor.create(
                    email=candidate_params["email"],
                    data=resume,
                    resume=resume["resume_path"],
                    business_id=business_id,
                    created_by_id=employee.id,
                )

                candidate = Candidate.get_record_exist_with_lower(
                    Candidate.email,
                    candidate_params["email"],
                    business_id=business_id,
                    is_deleted=False,
                )

                if candidate:
                    records.append(
                        {
                            **extractor.info(),
                            "duplicate_record": True,
                            "candidate_id": (candidate and candidate.id) or None,
                        }
                    )
                else:
                    resume_extractors.add(extractor)

        #  if not duplicate then save it
        for resume in resume_extractors:
            with db.atomic():
                candidate_id = save_candidate(
                    resume, opportunity_id=opportunity_id, resume_source=resume_source
                )
                # Perform database operations within this block
                candidate_ids.append(candidate_id)

        response_data = {
            "ids": candidate_ids,
            "opportunity_id": opportunity_id,
            "has_duplicate": len(records) > 0,
            "records": records,
            "resume_source": resume_source,
        }

        return SuccessResponse(
            message="Resume Extracted successfully.", data=response_data
        )
    except ValueError as e:
        logging.error(f"Exception in upload file: {str(e)} 1")
        raise e
    except CustomValidationError as e:
        logging.error(f"Exception in upload file: {str(e)} 2")
        raise e
    except Exception as e:
        logging.error(f"Exception in upload file: {str(e)} 3")
        raise HTTPException(
            status_code=500, detail=ConstantMessages.SOMETHING_WENT_WRONG
        )


@router.post(
    "/save_records",
    summary="Save Duplicate If exist",
    description="Uploads multiple files and saves them to the local directory.",
    response_model=SuccessResponse,
)
async def save_duplicate_record(
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "employee_role_id": 2,
            "permission_ids": [1, 2, 3],
            "opportunity_id": None,
            "resume_source": "JobPortal",
        },
    ),
):
    """
    This endpoint allows for the upload of multiple files.
    """

    try:
        business_id = business.id
        ids = body.get("ids")
        opportunity_id = body.get("opportunity_id")
        resume_source = body.get("resume_source")
        candidate_ids = []
        resume_extractors = ResumeExtractor.select().where(
            ResumeExtractor.business_id == business_id,
            ResumeExtractor.created_by_id == employee.id,
            ResumeExtractor.id.in_(ids),
        )
        for resume in resume_extractors:
            with db.atomic():
                candidate_id = save_candidate(resume, opportunity_id, resume_source)
                # Perform database operations within this block
                candidate_ids.append(candidate_id)

        return SuccessResponse(
            message="Candidate Added successfully.", data={"ids": candidate_ids}
        )
    except ValueError as e:
        logging.error(f"Exception in upload file: {str(e)} 1")
        raise e
    except CustomValidationError as e:
        logging.error(f"Exception in upload file: {str(e)} 2")
        raise e
    except Exception as e:
        logging.error(f"Exception in upload file: {str(e)} 3")
        raise HTTPException(
            status_code=500, detail=ConstantMessages.SOMETHING_WENT_WRONG
        )


@router.post(
    "/save_candidate/{job_request_id}",
    summary="Save Duplicate If exist",
    description="Uploads multiple files and saves them to the local directory.",
    response_model=SuccessResponse,
)
async def save_candidate_from_job(
    request: Request,
    job_request_id: int,
    employee: Employee = Depends(WildcardAuthHelper.get_current_employee),
    business: Business = Depends(WildcardAuthHelper.validate_subdomain),
):
    """
    This endpoint allows for the save candidate.
    """

    try:

        job_request = BusinessJobRequest.get_or_none(
            id=job_request_id, business_id=business.id
        )
        if not job_request:
            raise RecordNotFoundException(message="JobRequest does not exist")
        business_id = business.id
        request_data = RequestHeaderHelper.get_request_ip_basic(request)

        default_usage_dict: dict = dict(
            {
                "business_id": business_id,
                "created_by_id": employee.id,
                "response_status": "in-progress",
                **request_data,
            }
        )

        db_model = ApiKeyUsage(**default_usage_dict)
        cv_processor = CVProcessor(db_model=db_model)

        resume = await cv_processor.process_and_standardize_resume(
            job_request.resume_url
        )
        with db.atomic():
            resume["candidate_personal_info"]["email"] = job_request.email
            extractor = ResumeExtractor.create(
                email=job_request.email,
                data=resume,
                resume=job_request.resume,
                business_id=business_id,
                created_by_id=employee.id,
            )

            save_candidate(extractor, job_request.opportunity_id, "JobPortal")

        return SuccessResponse(
            message="Candidate Added successfully.", data=job_request.info()
        )
    except ValueError as e:
        logging.error(f"Exception in upload file: {str(e)} 1")
        raise e
    except CustomValidationError as e:
        logging.error(f"Exception in upload file: {str(e)} 2")
        raise e
    except Exception as e:
        logging.error(f"Exception in upload file: {str(e)} 3")
        raise HTTPException(
            status_code=500, detail=ConstantMessages.SOMETHING_WENT_WRONG
        )
