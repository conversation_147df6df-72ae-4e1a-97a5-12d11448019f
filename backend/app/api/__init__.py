# Import all routers from the API directory
# from . import auth
from app.api.admin import admin_router
from app.api.candidate import candidate_router
from app.api.wildcard import wildcard_router
from app.api.health_check import health_check_router
from app.api.home import home_router
from fastapi import APIRouter

# Create a new APIRouter instance to combine all routers
api_router = APIRouter()

# candidate api
api_router.include_router(candidate_router)

# admin api
api_router.include_router(admin_router)

# admin api
api_router.include_router(wildcard_router)

# health check
api_router.include_router(health_check_router)

# # Include all routes from registration
# api_router.include_router(auth.router)

# Include all routes from session
# api_router.include_router(session.router)

# Include all routes from the home page
api_router.include_router(home_router)
