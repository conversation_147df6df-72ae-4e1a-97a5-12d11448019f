from fastapi import APIRouter
from app.schema import MessageResponse
from database.connection import db

# Create the router
health_check_router = APIRouter(prefix="/health_check", tags=["Health Check"])


@health_check_router.get(
    "",
    summary="Health Check",
    description="Validates the health of the project.",
    response_model=MessageResponse,
)
async def health_check():
    """
    This endpoint validates the health of the project.

    It is used to ensure that the subdomain setup is functioning correctly.
    """
    health_data = db.execute_sql("SELECT 1;")
    if health_data:
        return MessageResponse(message="200 Ok Validated.")
    else:
        return MessageResponse(message="500 Internal Server Error")
