from datetime import datetime
from fastapi import (
    <PERSON>Router,
    Depends,
    HTTPException,
    status,
    Query,
    Request,
    Body,
    BackgroundTasks,
)
from app.helper import Candidate<PERSON><PERSON><PERSON><PERSON>per
from app.schema import PaginationResponse, SuccessResponse
from app.exceptions import CustomValidationError
from app.models import (
    Candidate,
    Opportunity,
    Business,
    ScheduleInterview,
    ScreeningInterviewQuestion,
    CandidateScreeningAnswer,
    ApiKeyUsage,
    CandidateInterviewSuggestion,
)
from app.models.concern.enum import InterviewStatus, CandidateInterviewStatus
from peewee import JOIN, fn
import logging
from starlette.datastructures import UploadFile
from app.uploader.screening_video_uploader import ScreeningVideoUploader
from app.helper.request_header_helper import <PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>per
from app.validations import StringValidate, NumberValidate
from app.constants import ConstantMessages
from app.uploader.recording_uploader import RecordingUploader
from app.utils.string_utils import generate_random_string
from app.utils.audio_to_text_utils import AudioToTextUtils
from extraction_modules.process_job import JobProcessor
from services.face_detection_service import FaceDetectionService
from extraction_modules.coding_compiler import CodingCompiler
import cv2
import numpy as np


detection_service = FaceDetectionService()


async def calculate_score(
    request: Request, interview: ScheduleInterview, disqualified: bool = False
) -> int:
    """
    Calculates the total score for a candidate based on their answers to
    both objective and subjective questions in an interview.

    The function retrieves objective answers and counts how many are correct
    by comparing them to the expected answers. For subjective answers, it processes
    the answers using a job processor to determine correctness.

    Args:
        request (Request): The incoming request object containing client information.
        interview (ScheduleInterview): The interview instance containing candidate answers
                                       and related details.

    Returns:
        int: The total number of correct answers, combining both objective and
             subjective evaluations.
    """
    try:
        compiler = CodingCompiler()

        business_id = interview.business_id
        request_data = RequestHeaderHelper.get_request_ip_basic(request)
        default_usage_dict: dict = dict(
            {
                "business_id": business_id,
                "response_status": "in-progress",
                **request_data,
            }
        )
        db_model = ApiKeyUsage(**default_usage_dict)
        cv_processor = JobProcessor(db_model=db_model)

        objective_answers = interview.candidate_screening_answers.where(
            CandidateScreeningAnswer.question_type == 0
        )
        subjective_answers = interview.candidate_screening_answers.where(
            CandidateScreeningAnswer.question_type == 1
        )

        coding_answers = interview.candidate_screening_answers.where(
            CandidateScreeningAnswer.question_type == 2
        )

        objective_mark = 0

        for answer in objective_answers:
            if answer.answer == answer.screening_interview_question.answer:
                answer.is_correct = True
                objective_mark += 1
            else:
                answer.is_correct = False

            answer.save()

        subjective_answers_dict = [
            {
                "question_id": record.screening_interview_question_id,
                "question": record.screening_interview_question.question,
                "actual_answer": record.screening_interview_question.answer,
                "given_answer": record.answer,
            }
            for record in subjective_answers
        ]

        subjective_mark, answers = await cv_processor.process_and_validate_answers(
            subjective_answers_dict
        )

        for answer in answers:
            question_id = answer.get("question_id")
            question = subjective_answers.where(
                CandidateScreeningAnswer.screening_interview_question_id == question_id
            ).first()
            if not question:
                continue  # Skip if question wasn't found

            status = answer.get("status", "")
            is_correct = status.lower() != "not matched"

            question.is_correct = is_correct
            explanation = answer.get("explanation", "")
            question.answer_explanation = explanation
            question.save()

        coding_marks = 0
        coding_answer_array = []

        for coding_answer in coding_answers:
            screening_question = coding_answer.screening_interview_question
            test_cases = screening_question.build_test_cases  # Assumes it's a @property
            language = screening_question.language
            code = coding_answer.answer

            success, total_success, total, *rest = await compiler.execute_code(
                code=code, language=language, test_cases=test_cases
            )

            if success:  # and total_success == total:
                coding_answer_array.append(
                    {
                        "question_id": screening_question.id,
                        "question": screening_question.question,
                        "candidate_code": code,
                    }
                )
            else:
                coding_answer.is_correct = False
                coding_answer.answer_explanation = "Failed to compile"

            logging.info(f"Success: {success} - Result  {total_success} out of {total}")

        if coding_answer_array:
            coding_result = await cv_processor.process_and_validate_coding_answers(
                coding_answer_array
            )
            for answer in coding_result["results"]:
                question_id = answer.get("question_id")
                question = subjective_answers.where(
                    CandidateScreeningAnswer.screening_interview_question_id
                    == question_id
                ).first()

                if not question:
                    continue  # Skip if question wasn't found

                if answer.get("verdict", "") == "Correct":
                    coding_marks += 1
                    question.is_correct = True
                    question.answer_explanation = answer.get("explanation", "")
                else:
                    question.is_correct = False
                    question.answer_explanation = answer.get("explanation", "")

                question.save()
        # for process_and_validate_coding_answers()

        total_answers = (objective_mark + subjective_mark + coding_marks) * 5
        interview.score = total_answers
        if disqualified:
            # for disqualified
            interview.status = InterviewStatus(7)
            candidate_interview = interview.candidate_interview
            # update as rejected candidate
            candidate_interview.status = CandidateInterviewStatus(5)
            candidate_interview.save()

        elif total_answers >= (interview.passing_percentage or 50):
            # for passed
            interview.status = InterviewStatus(4)
        else:
            # for rejected
            interview.status = InterviewStatus(3)
            candidate_interview = interview.candidate_interview
            # update as rejected candidate
            candidate_interview.status = CandidateInterviewStatus(2)
            candidate_interview.save()

        interview.save()
        return total_answers
    except Exception as e:
        logging.info(f"Error as {str(e)}")


# Helper function to get candidate by ID
async def get_candidate_interview(
    interview_id: int,
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
) -> Candidate:
    """
    Retrieve a candidate by its ID.

    Args:
        id (int): The ID of the candidate to retrieve.
        business (Business): The current business, provided by dependency injection.

    Returns:
        Candidate: The candidate object if found.

    Raises:
        RecordNotFoundException: If the candidate does not exist.
    """
    opportunity_interview = (
        ScheduleInterview.select()
        .where(
            ScheduleInterview.business_id == business.id,
            ScheduleInterview.candidate_id == current_candidate.id,
            ScheduleInterview.id == interview_id,
        )
        .get()  # Fetch the single latest record
    )

    if not opportunity_interview:
        raise HTTPException("No Interview found with this job")

    return opportunity_interview


# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/interviews",
    tags=["Candidate Interview API"],
    dependencies=[Depends(CandidateAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "/list",
    summary="List of Scheduled Interviews",
    description="Retrieve scheduled interviews for candidates.",
    response_model=PaginationResponse,
)
async def get_interviews(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve scheduled interviews for candidates.

    Args:
    - business: The authenticated business user, injected by dependency injection.
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of scheduled interviews.
    """
    try:
        # Fetch scheduled interviews from the schedule interview table
        base_query = (
            ScheduleInterview.select(
                fn.MAX(ScheduleInterview.interview_at).alias("interview_at"),
                Opportunity.id.alias("job_id"),
                Opportunity.title.alias("job_title"),
                Opportunity.description.alias("job_description"),
                Opportunity.status.alias("job_status"),
            )
            .join(
                Opportunity,
                JOIN.LEFT_OUTER,
                on=(ScheduleInterview.opportunity_id == Opportunity.id),
            )
            .where(
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
            .group_by(
                Opportunity.id,
                Opportunity.title,
                Opportunity.description,
                Opportunity.status,
            )
        )

        # Get total records
        total_records = base_query.count()

        # Pagination
        offset = (page - 1) * limit
        records = (
            base_query.dicts()  # Ensure this is here
            .offset(offset)
            .limit(limit)
            .order_by(fn.MAX(ScheduleInterview.interview_at).desc())
        )

        # Extract data
        rows = []
        for record in records:
            rows.append(
                {
                    "job_id": record["job_id"],
                    "job_title": record["job_title"],
                    "job_description": record["job_description"],
                    "job_status": record["job_status"],
                    "interview_at": record["interview_at"],
                }
            )

        # Return the data using the defined response model
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )

    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/jobs/{opportunity_id}",
    summary="List of Opportunity Scheduled Interviews",
    description="Retrieve scheduled interviews for candidates.",
    response_model=SuccessResponse,
)
async def get_interviews(
    opportunity_id: int,
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve scheduled interviews for candidates.

    Args:
    - business: The authenticated business user, injected by dependency injection.
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of scheduled interviews.
    """
    try:
        # Fetch scheduled interviews from the schedule interview table
        opportunity_interview = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.opportunity_id == opportunity_id,
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
            .get()  # Fetch the single latest record
        )

        if not opportunity_interview:
            raise HTTPException("No Interview found with this job")

        base_query = (
            ScheduleInterview.select()
            .join(
                Opportunity,
                JOIN.LEFT_OUTER,
                on=(ScheduleInterview.opportunity_id == Opportunity.id),
            )
            .where(
                ScheduleInterview.opportunity_id
                == opportunity_interview.opportunity_id,
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
        )

        rows = []

        for record in base_query:
            record_info = record.info()
            opportunity = record.opportunity
            feedback = record.feedback
            row_data = {
                **record_info,  # Spread the info() dictionary from ScheduleInterview
                "opportunity": ((opportunity and opportunity.info()) or None),
                "feedback": ((feedback and feedback.info()) or None),
            }
            rows.append(row_data)

        # Return the data using the defined response model
        return SuccessResponse(
            data=rows,
            message="Data fetched successfully",
        )

    except Exception as e:
        print("exception in active job interviews  ", e)
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/{interview_id}/questions",
    summary="Retrieve 20 random questions",
    description="It Retrieve 20 random questions for the table according to the job id and the job title.",
    response_model=SuccessResponse,
)
def get_questions(
    limit: int = Query(20),
    interview: ScheduleInterview = Depends(get_candidate_interview),
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
):
    """
    Args:
        id (int):It take the id for opportunity to identify the job.
        business (Business): _description_. Defaults to Depends(WildcardAuthHelper.validate_subdomain).

    Raises:
        HTTPException:If the Questions record is not found or any other error occurs during processing.

    Returns:
        SuccessResponse: A JSON response containing the success message and data of the 20 random questions from the table.
    """
    try:

        # Fetching the 20 random questions for the questions table according to the job title.
        records = (
            ScreeningInterviewQuestion.select()
            .where(
                ScreeningInterviewQuestion.opportunity_id == interview.opportunity_id,
                ScreeningInterviewQuestion.business_id == business.id,
            )
            .order_by(fn.Rand())
            .limit(limit)
        )
        # Prepare job request list
        total_records = records.count()

        rows = [
            {
                "id": record.id,
                "type": record.question_type,
                "question": record.question,
                "options": record.question_option_list,
                "test_cases": record.test_cases_list,
                "language": record.language,
                "starter_code": record.starter_code,
            }
            for record in records
        ]

        return SuccessResponse(
            message=" Questions fetch successfully.",
            data={"count": total_records, "questions": rows},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{interview_id}/answers",
    summary="Adding the candidate answers",
    description="Adding the candidate answers in the candidate_answers table according to the candidate_id,job_id and question_id generating the score according to the candidate answer.",
    response_model=SuccessResponse,
)
async def add_candidate_answer(
    request: Request,
    background_tasks: BackgroundTasks,
    interview: ScheduleInterview = Depends(get_candidate_interview),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
):
    """
    Args:
        business (Business): _description_. Defaults to Depends(WildcardAuthHelper.validate_subdomain)
        candidate_id(Candidate): _description_.Take the candidate id to identify the candidate.
        opportunity_id(Opporuntiy):_description_.Take the opportunity id to identify the opporuntiy.
        question_id(Question):_description_.Take the questions id of the each questions with is answer by the candidate.
        answer:_description_.Take the answer of the each questions and stores in the candidate_answers table.

    Raises:
        ValueError: if any records is not found or inactive.
        HTTPException: If the body format is not correct or any other error occurs during processing.

    Returns:
        SuccessResponse: A JSON response containing the success message and the answers with questions id that is added to the candidate_answers table.
    """

    try:
        opportunity_id = interview.opportunity_id
        interview_id = interview.id
        candidate_id = current_candidate.id
        business_id = business.id
        form = await request.form()
        files = [value for _, value in form.items() if isinstance(value, UploadFile)]
        if not files or len(files) == 0:
            raise CustomValidationError(status_code=400, error="Error saving answers.")
        file = files[0]

        unique_subdir = f"{candidate_id}/interviews/{interview.id}"
        video_uploader = ScreeningVideoUploader(file, file.filename, unique_subdir)
        if video_uploader.valid():
            res = await video_uploader.upload()
            interview.screening_video = res["file_path"]
            interview.save()
        else:
            raise CustomValidationError(status_code=400, error="Error saving answers.")

        disqualified = False

        for key, value in form.items():
            if isinstance(value, str) and key == "disqualified":
                disqualified = True
            elif isinstance(
                value, str
            ):  # Assuming that the questions are passed as string data
                question = ScreeningInterviewQuestion.get_or_none(
                    ScreeningInterviewQuestion.id == int(key)
                )
                question_type = (question and question.question_type.value) or 0
                answer = {
                    "screening_interview_question_id": key,
                    "answer": value,
                    "candidate_id": candidate_id,
                    "schedule_interview_id": interview.id,
                    "opportunity_id": opportunity_id,
                    "business_id": business_id,
                    "question_type": question_type,
                }

                question_answer = CandidateScreeningAnswer.get_or_none(
                    CandidateScreeningAnswer.screening_interview_question_id
                    == int(key),
                    CandidateScreeningAnswer.candidate_id == candidate_id,
                    CandidateScreeningAnswer.schedule_interview_id == interview_id,
                    CandidateScreeningAnswer.opportunity_id == opportunity_id,
                    CandidateScreeningAnswer.business_id == business_id,
                )
                if question_answer:
                    question_answer.answer = answer["answer"]
                    question_answer.save()
                else:
                    CandidateScreeningAnswer.create(**answer)

        background_tasks.add_task(calculate_score, request, interview, disqualified)

        if disqualified:
            return SuccessResponse(
                message="Unfortunately, due to multiple warnings, you have been disqualified from the process.",
            )

        return SuccessResponse(
            message="Thanks for interview we will update you soon",
        )

    except Exception as e:
        logging.error(f"Exception in {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.get(
    "/{interview_id}/detail",
    summary="Get Interview Detail",
    description="Get Interview Detail",
    response_model=SuccessResponse,
)
def get_interview_detail(
    interview: ScheduleInterview = Depends(get_candidate_interview),
):
    """
    Args:
        id (int):It take the id for opportunity to identify the job.
        business (Business): _description_. Defaults to Depends(WildcardAuthHelper.validate_subdomain).

    Raises:
        HTTPException:If the Questions record is not found or any other error occurs during processing.

    Returns:
        SuccessResponse: A JSON response containing the success message and data of the 20 random questions from the table.
    """
    try:
        return SuccessResponse(
            message="Interview Detail fetch successfully.",
            data=interview.info(),
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{interview_id}/feedback",
    summary="Provide Interview Feedback",
    description="Provide Interview Feedback",
    response_model=SuccessResponse,
)
def provide_feedback(
    interview: ScheduleInterview = Depends(get_candidate_interview),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "password",
        },
    ),
):
    """
    Args:
        id (int):It take the id for opportunity to identify the job.
        business (Business): _description_. Defaults to Depends(WildcardAuthHelper.validate_subdomain).

    Raises:
        HTTPException:If the Questions record is not found or any other error occurs during processing.

    Returns:
        SuccessResponse: A JSON response containing the success message and data of the 20 random questions from the table.
    """
    try:

        interview_feedback = CandidateInterviewSuggestion.get_or_none(
            CandidateInterviewSuggestion.candidate_id == current_candidate.id,
            CandidateInterviewSuggestion.schedule_interview_id == interview.id,
        )

        if interview_feedback:
            raise ValueError("Feedback already saved")

        suggestion = StringValidate(
            body.get("suggestion"), field="Suggestion", required=False, strip=True
        )
        rating = NumberValidate(
            body.get("rating"),
            field="Rating",
            allow_zero=False,
            min_value=1,
            max_value=5,
        )

        rating_fields = body.get("rating_fields") or {}
        approved_status = body.get("approved_status") or 0

        # save interview feedback
        CandidateInterviewSuggestion.create(
            suggestion=suggestion,
            rating=rating,
            candidate_id=current_candidate.id,
            schedule_interview_id=interview.id,
            approved_status=approved_status,
            rating_fields=rating_fields,
        )

        return SuccessResponse(message="Feedback saved successfully.")
    except Exception as e:
        print("errors as e", e)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )


@router.post(
    "/{interview_id}/convert-audio",
    summary="Provide Interview Feedback",
    description="Provide Interview Feedback",
    response_model=SuccessResponse,
)
async def convert_audio(
    request: Request,
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    # Convert incoming file to audio format (e.g., WAV)
    try:
        form = await request.form()
        files = [value for _, value in form.items() if isinstance(value, UploadFile)]
        if not files or len(files) == 0:
            raise CustomValidationError(status_code=400, error="No file uploaded.")
        file = files[0]
        unique_subdir = f"{current_candidate.id}-{int(datetime.now().timestamp())}-{generate_random_string()}"

        recording = RecordingUploader(file, file.filename, unique_subdir)
        if recording.valid():
            res = await recording.upload()
            audio_text = ""
            if res["on_cloud"]:
                audio_text = AudioToTextUtils.transcribe_audio_from_url(res["url"])
            else:
                audio_text = AudioToTextUtils.audio_to_text(
                    file_path=res["file_path"], delete_file=True
                )

            return SuccessResponse(
                message="Audio Converted Successfully",
                data={"valid": True, "text": audio_text},
            )
        else:
            return SuccessResponse(message="Invalid File/Format", data={"valid": False})
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        print("errors as e", e)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=ConstantMessages.SOMETHING_WENT_WRONG,
        )


@router.post(
    "/detect/face",
    summary="Detect Face Expression",
    description="Detect Face Expression",
    response_model=SuccessResponse,
)
async def detect_face(request: Request):
    form = await request.form()
    files = [value for _, value in form.items() if isinstance(value, UploadFile)]
    if not files or len(files) == 0:
        raise CustomValidationError(status_code=400, error="No file uploaded.")
    image = files[0]

    contents = await image.read()
    npimg = np.frombuffer(contents, np.uint8)
    img = cv2.imdecode(npimg, cv2.IMREAD_COLOR)

    if img is None:
        # Return a standardized response for invalid image file
        return SuccessResponse(message="Invalid File/Format", data={"valid": False})

    result = detection_service.detect(img)
    # If the detection service returns a status of "ok", we mark it as valid
    if result.get("status") == "ok":
        return SuccessResponse(message=result.get("message"), data={"valid": True})
    else:
        return SuccessResponse(message=result.get("message"), data={"valid": False})
