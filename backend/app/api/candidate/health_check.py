from fastapi import APIRouter
from app.schema import MessageResponse

# Create the router
router = APIRouter(prefix="/health_check", tags=["Wildcard Subdomain Health Check"])


@router.post(
    "",
    summary="WildCard Subdomain Health Check",
    description="Validates the health of the wildcard subdomain.",
    response_model=MessageResponse,
)
async def health_check():
    """
    This endpoint validates the health of the wildcard subdomain.

    It is used to ensure that the subdomain setup is functioning correctly.
    """
    return MessageResponse(message="Subdomain Validated.")
