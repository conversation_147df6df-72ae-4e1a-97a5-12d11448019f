from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.helper import Candidate<PERSON>uthHelper
from app.schema import SuccessResponse
from app.models import (
    Candidate,
    Opportunity,
    Business,
    ScheduleInterview,
)
from peewee import JOIN

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/dashboard",
    tags=["Candidate Dashboard API"],
    dependencies=[Depends(CandidateAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "/active_job",
    summary="List of Scheduled Interviews",
    description="Retrieve scheduled interviews for candidates.",
    response_model=SuccessResponse,
)
async def get_interviews(
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve scheduled interviews for candidates.

    Args:
    - business: The authenticated business user, injected by dependency injection.
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of scheduled interviews.
    """
    try:
        # Fetch scheduled interviews from the schedule interview table
        last_interview = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
            .order_by(
                ScheduleInterview.interview_at.desc()
            )  # Order by latest interview time
            .get()  # Fetch the single latest record
        )

        if not last_interview:
            raise HTTPException("No Active Interview found")

        opportunity = last_interview.opportunity

        # Return the data using the defined response model
        return SuccessResponse(
            data=opportunity.info(),
            message="Data fetched successfully",
        )

    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/active_job/interviews",
    summary="List of Scheduled Interviews",
    description="Retrieve scheduled interviews for candidates.",
    response_model=SuccessResponse,
)
async def get_interviews(
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve scheduled interviews for candidates.

    Args:
    - business: The authenticated business user, injected by dependency injection.
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of scheduled interviews.
    """
    try:
        # Fetch scheduled interviews from the schedule interview table
        last_interview = (
            ScheduleInterview.select()
            .where(
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
            .order_by(
                ScheduleInterview.interview_at.desc()
            )  # Order by latest interview time
            .get()  # Fetch the single latest record
        )

        if not last_interview:
            raise HTTPException("No Active Interview found")

        base_query = (
            ScheduleInterview.select()
            .join(
                Opportunity,
                JOIN.LEFT_OUTER,
                on=(ScheduleInterview.opportunity_id == Opportunity.id),
            )
            .where(
                ScheduleInterview.opportunity_id == last_interview.opportunity_id,
                ScheduleInterview.business_id == business.id,
                ScheduleInterview.candidate_id == current_candidate.id,
            )
        )

        rows = []

        for record in base_query:
            record_info = record.info()
            opportunity = record.opportunity
            feedback = record.feedback
            row_data = {
                **record_info,  # Spread the info() dictionary from ScheduleInterview
                "opportunity": ((opportunity and opportunity.info()) or None),
                "feedback": ((feedback and feedback.info()) or None),
            }
            rows.append(row_data)

        # Return the data using the defined response model
        return SuccessResponse(
            data=rows,
            message="Data fetched successfully",
        )

    except Exception as e:
        print("exception in active job interviews  ", e)
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
