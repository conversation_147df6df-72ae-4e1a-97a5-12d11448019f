from fastapi import APIRouter, Depends, HTTPException, status, Query
from app.helper import Candidate<PERSON>uthHelper
from app.schema import SuccessResponse, PaginationResponse
from app.models import (
    Candidate,
    CandidateSkill,
    CandidateExperience,
    CandidateEducationInformation,
    CandidateInterviewFeedback,
    CandidateDocument,
)
from peewee import JOIN
import logging

# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/profile",
    tags=["Candidate Dashboard API"],
    dependencies=[Depends(CandidateAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "",
    summary="Get current Candidate Profile",
    description="Retrieve the profile of the currently authenticated candidate.",
    response_model=SuccessResponse,
)
async def get_profile(
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve the profile of the currently authenticated candidate.

    Args:
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and the candidate's profile information.
    """
    try:
        # Return the success response with the candidate's information
        candidate_detail = current_candidate.info()
        education_informations = (
            current_candidate.candidate_education_informations.where(
                CandidateEducationInformation.is_deleted == False
            )
        )
        candidate_experiences = current_candidate.candidate_experiences.where(
            CandidateExperience.is_deleted == False
        )
        candidate_skills = current_candidate.candidate_skills.where(
            CandidateSkill.is_deleted == False
        )
        candidate_qualifications = current_candidate.qualifications
        candidate_detail["educations_informations"] = [
            record.info() for record in education_informations
        ]
        candidate_detail["experiences"] = [
            record.info() for record in candidate_experiences
        ]
        candidate_detail["skills"] = [record.info() for record in candidate_skills]
        candidate_detail["qualifications"] = [
            record.info() for record in candidate_qualifications
        ]

        return SuccessResponse(
            message="Data fetched successfully", data=candidate_detail
        )
    except Exception as e:
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )


@router.get(
    "/feedbacks",
    summary="List of Interview Feedbacks",
    description="Retrieve a paginated list of interviews.",
    response_model=PaginationResponse,
)
async def get_interview_feedbacks(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve a paginated list of interview feedbacks.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interview feedbacks.
    """
    try:
        # Fetch total count
        base_query = CandidateInterviewFeedback.where(
            {"candidate_id": current_candidate.id}
        )

        base_query = base_query.order_by(CandidateInterviewFeedback.id.desc())
        total_records = base_query.count()

        # Paginate results
        if total_records > 0:
            offset = (page - 1) * limit
            records = base_query.offset(offset).limit(limit)
        else:
            records = []

        # Prepare interview list
        rows = [record.info() for record in records]

        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in interview list: {str(e)}")
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": 0, "rows": []},
            message=f"Failed to fetch data: {str(e)}",
        )


@router.get(
    "/documents",
    summary="List of self documents",
    description="List of self documents",
    response_model=SuccessResponse,
)
async def get_interview_feedbacks(
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve a paginated list of interview feedbacks.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        limit (int): The maximum number of interviews per page. Defaults to 10.

    Returns:
        PaginationResponse: Paginated list of interview feedbacks.
    """
    try:
        # Fetch total count
        base_query = CandidateDocument.where({"candidate_id": current_candidate.id})

        records = base_query.order_by(CandidateDocument.name.asc())

        # Prepare document list
        rows = [record.info() for record in records]

        return SuccessResponse(
            data=rows,
            message="Data fetched successfully",
        )
    except Exception as e:
        logging.error(f"Exception in candidate document list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data: {str(e)}")
