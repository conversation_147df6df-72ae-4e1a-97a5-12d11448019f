from fastapi import APIRouter, Depends, HTTPException, Body, Path
from app.helper import Candidate<PERSON>uthHelper
from app.schema import SuccessResponse
from app.models import Candidate, ScreeningInterviewQuestion
from extraction_modules.coding_compiler import CodingCompiler
import logging


# Initialize the APIRouter with updated tag
router = APIRouter(
    prefix="/coding-rounds",
    tags=["Candidate Coding Round"],
    dependencies=[Depends(CandidateAuthHelper.get_current_auth_token)],
)


@router.post(
    "/{question_id}/run-code",
    summary="Run candidate's code for a question",
    description="Executes the candidate's submitted code for the given interview question.",
    response_model=SuccessResponse,
)
async def run_code(
    question_id: int = Path(..., description="ID of the interview question"),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    body: dict = Body(
        ...,
        example={"code": "print('Hello, World!')", "language": "python"},
    ),
):
    """
    Executes code submitted by a candidate for a specific interview question.

    Args:
        question_id (int): ID of the interview question.
        current_candidate (Candidate): Authenticated candidate information.
        body (dict): Dictionary containing 'code' and 'language'.

    Returns:
        SuccessResponse: Status of the code execution.
    """
    try:
        logging.info(
            f"Candidate {current_candidate.id} is running code on question {question_id}"
        )
        code = body.get("code")
        language = body.get("language")

        if not code or not language:
            raise HTTPException(
                status_code=400,
                detail="Both 'code' and 'language' fields are required.",
            )

        logging.info(f"Code: {code}")
        logging.info(f"Language: {language}")

        interview_question = ScreeningInterviewQuestion.get_or_none(id=question_id)

        test_cases = interview_question.build_test_cases

        logging.info(f"test_cases: {test_cases}")

        compiler = CodingCompiler()

        success, total_success, total, message, data = await compiler.execute_code(
            code=code, language=language, test_cases=test_cases
        )

        logging.info(f"Success: {success} - Result  {total_success} out of {total}")

        if not success:
            raise ValueError(message)

        return SuccessResponse(
            message=message, success=True, data=data["test_case_results"]
        )
        # Placeholder for actual code execution logic
    except Exception as e:
        logging.error(f"Exception in run_code: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to execute code: {str(e)}")
