from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    Query,
)
from app.helper import Candidate<PERSON>uthHelper
from app.schema import PaginationResponse
from app.models import (
    Candidate,
    Opportunity,
    Business,
)
from peewee import fn
from typing import Optional


# Initialize the APIRouter with a tag for categorizing endpoints in the documentation.
router = APIRouter(
    prefix="/opportunities",
    tags=["Candidate Interview API"],
    dependencies=[Depends(CandidateAuthHelper.get_current_auth_token)],
)

# ------------------------------ functions ------------------------------


@router.get(
    "",
    summary="List of Matching jobs",
    description="Retrieve matching jobs for candidates.",
    response_model=PaginationResponse,
)
async def get_interviews(
    page: int = Query(1, gt=0),
    limit: int = Query(10, gt=0, le=100),
    search: Optional[str] = Query(
        None, description="Search term to filter opportunity"
    ),
    business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    current_candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
):
    """
    Retrieve scheduled interviews for candidates.

    Args:
    - business: The authenticated business user, injected by dependency injection.
    - current_candidate: The authenticated candidate, injected by dependency injection.

    Returns:
    - A response model containing a message and a list of scheduled interviews.
    """
    try:
        # Fetch scheduled interviews from the schedule interview table
        base_query = Opportunity.select().where(Opportunity.business_id == business.id)

        if search:
            search = search.strip().lower()
            base_query = base_query.where(
                fn.lower(Opportunity.title).contains(search)
                | fn.lower(Opportunity.designation).contains(search)
                | fn.lower(Opportunity.description).contains(search)
            )

        # Get total records
        total_records = base_query.count()
        print(total_records, "totooo", search, base_query)

        # Pagination
        offset = (page - 1) * limit
        records = base_query.offset(offset).limit(limit).order_by(Opportunity.id.desc())

        # Extract data
        rows = [record.info() for record in records]

        # Return the data using the defined response model
        return PaginationResponse(
            data={"page": page, "limit": limit, "count": total_records, "rows": rows},
            message="Data fetched successfully",
        )

    except Exception as e:
        print("error as e", e)
        # If any exception occurs, raise an HTTP 500 error with the exception details
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)
        )
