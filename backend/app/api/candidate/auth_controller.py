from fastapi import APIRouter, Body, Response, Depends, Request
from app.models import (
    Candidate,
    CandidateAuthLoginResponse,
    AuthToken,
    Business,
)
from app.schema import MessageResponse, SuccessResponse
from app.exceptions import CustomValidationError, UnauthorizedException
from app.validations import PasswordValidate
from app.helper import Candidate<PERSON><PERSON><PERSON><PERSON><PERSON>
from app.tasks import CandidateTask
from typing import Union
from app.constants import ConstantMessages
import logging


async def get_login_response(
    request: Request,
    response: Response,
    candidate: Candidate,
    message: str,
    auth_token: Union[AuthToken, None] = None,
) -> CandidateAuthLoginResponse:
    # If auth_token is not provided, create a new one
    if auth_token is None:
        auth_token = AuthToken.create(
            object_type=candidate.__class__.__name__, object_id=candidate.id
        )
        access_token = (
            auth_token.id
        )  # Assign the id of the created auth token to access_token
        # Set the auth token cookie with the newly created access_token
        await CandidateAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=access_token
        )
    else:
        access_token = (
            auth_token.id
        )  # If auth_token is provided, use its id as the access_token

    # Fetch permissions and nodes associated with the candidate's role and business
    # Return an CandidateAuthLoginResponse object with the candidate's details and the fetched nodes
    return CandidateAuthLoginResponse(
        message=message,
        data={
            "id": candidate.id,
            "email": candidate.email,
            "name": candidate.name,
            "business_id": candidate.business_id,
            "total_experience": candidate.total_experience,
            "is_fresher": candidate.is_fresher,
            "access_token": access_token,
        },
    )


router = APIRouter(prefix="/auth", tags=["Wildcard Subdomain Authentication"])


@router.post(
    "/login",
    summary="Candidate Auth Login",
    description="Candidate Auth Login with Email and Password.",
    response_model=CandidateAuthLoginResponse,
)
async def login(
    request: Request,
    response: Response,
    current_business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
            "password": "password",
        },
    ),
):
    """
    Endpoint for candidate login.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing email and password.

    Returns:
        CandidateAuthLoginResponse: The response containing candidate data and login status.
    """
    try:
        email = body.get("email")
        password = body.get("password")

        candidate = await CandidateAuthHelper.authenticate_candidate(
            current_business.id, email, password
        )

        if not candidate:
            raise UnauthorizedException(error="Incorrect username or password.")

        login_res = await get_login_response(
            request=request,
            response=response,
            candidate=candidate,
            message="Logged in successfully",
        )
        return login_res
    except UnauthorizedException as e:
        raise e
    except Exception as e:
        logging.error(f"Login Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/forgot-password",
    summary="Forgot Password",
    description="Forgot Password.",
    response_model=MessageResponse,
)
async def forgot_password(
    request: Request,
    response: Response,
    current_business: Business = Depends(CandidateAuthHelper.validate_subdomain),
    body: dict = Body(
        ...,
        example={
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to handle forgot password requests.

    Args:
        response (Response): The response object to set cookies.
        body (dict): The request body containing the email.

    Returns:
        MessageResponse: The response indicating the result of the forgot password request.
    """
    try:
        email = body.get("email")

        candidate = await CandidateAuthHelper.get_auth_candidate(
            business_id=current_business.id, email=email
        )
        if candidate is None:
            raise CustomValidationError(
                error="Candidate with this email does not exist."
            )

        candidate.candidate_auth.generate_otp()
        CandidateTask.reset_password_email.delay(candidate.id)
        await CandidateAuthHelper.set_otp_cookie(
            request=request, response=response, email=candidate.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to verify your email address."
        )
    except ValueError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Forgot Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/verify-forgot-password-otp",
    summary="Verify OTP",
    description="Verify OTP.",
    response_model=MessageResponse,
)
async def verify_forgot_password_otp(
    request: Request,
    response: Response,
    candidate: Candidate = Depends(CandidateAuthHelper.get_email_otp_candidate),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "email": "<EMAIL>",
        },
    ),
):
    """
    Endpoint to verify the OTP for forgotten password.

    Args:
        response (Response): The response object to set cookies.
        candidate (CandidateAuth): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the OTP and email.

    Returns:
        MessageResponse: The response indicating the result of the OTP verification.
    """
    try:
        otp = body.get("otp")
        candidate_auth = candidate.candidate_auth

        if candidate_auth.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != candidate_auth.otp:
            raise CustomValidationError(error="OTP is invalid")

        await CandidateAuthHelper.set_otp_cookie(
            request=request, response=response, email=candidate.email
        )
        return MessageResponse(message="OTP verified successfully.")
    except ValueError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except CustomValidationError as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=e)
    except Exception as e:
        logging.error(f"Verify Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/resend-forgot-password-otp",
    summary="Resend OTP",
    description="Resend OTP.",
    response_model=MessageResponse,
)
async def resend_verify_email(
    request: Request,
    response: Response,
    candidate: Candidate = Depends(CandidateAuthHelper.get_email_otp_candidate),
    body: dict = Body(..., example={"otp": "123456"}),
):
    """
    Endpoint to resend the OTP for password reset.

    Args:
        response (Response): The response object to set cookies.
        candidate (Candidate): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the OTP.

    Returns:
        MessageResponse: The response indicating the result of the OTP resend operation.
    """
    try:
        candidate_auth = candidate.candidate_auth
        candidate_auth.generate_otp()
        CandidateTask.resend_otp_email.delay(
            candidate_id=candidate.id,
            resend_text="Please use this OTP to reset your password.",
        )

        await CandidateAuthHelper.set_otp_cookie(
            request=request, response=response, email=candidate.email
        )
        return MessageResponse(
            message="An email containing the OTP has been sent to your email address. Please use this OTP to reset your password."
        )
    except Exception as e:
        logging.error(f"Resend Forgot Password OTP Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/change-password",
    summary="Update Password",
    description="Update Password.",
    response_model=CandidateAuthLoginResponse,
)
async def change_password(
    request: Request,
    response: Response,
    candidate: Candidate = Depends(CandidateAuthHelper.get_email_otp_candidate),
    body: dict = Body(
        ...,
        example={
            "otp": "123456",
            "password": "123456",
            "confirm_password": "123456",
        },
    ),
):
    """
    Endpoint to update the candidate's password.

    Args:
        response (Response): The response object to set cookies.
        candidate (Candidate): The candidate instance, provided by dependency injection.
        body (dict): The request body containing the OTP, new password, and confirm password.

    Returns:
        MessageResponse: The response indicating the result of the password update.
    """
    try:
        otp = body.get("otp")
        candidate_auth = candidate.candidate_auth

        if candidate_auth.is_otp_expired():
            raise CustomValidationError(error="OTP has expired")

        if not otp or otp != candidate_auth.otp:
            raise CustomValidationError(error="OTP is invalid")

        password = body.get("password")
        confirm_password = body.get("confirm_password")

        password_hash = PasswordValidate(
            password, field="Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Password", min_length=8, max_length=20
        )

        if password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        candidate_auth.password = password_hash
        candidate_auth.save()
        # Assuming `set_otp_cookie` handles the logic for resetting OTP or related operations.
        await CandidateAuthHelper.clear_otp_cookie(request=request, response=response)

        login_res = await get_login_response(
            request=request,
            response=response,
            candidate=candidate,
            message="Password Changed successfully",
        )
        return login_res
    except CustomValidationError as e:
        raise e
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Change Password Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.post(
    "/me",
    summary="Verify Current Candidate",
    description="Verify Current Candidate",
    response_model=CandidateAuthLoginResponse,
)
async def verify_candidate(
    request: Request,
    response: Response,
    candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    auth_token: AuthToken = Depends(CandidateAuthHelper.get_current_auth_token),
):
    """
    Endpoint to verify the current candidate.

    Args:
        response (Response): The response object to set cookies.
        candidate (Candidate): The current candidate, provided by dependency injection.

    Returns:
        CandidateAuthLoginResponse: The response containing candidate data and login status.
    """
    try:
        auth_token.update_expiration()
        await CandidateAuthHelper.set_auth_token_cookie(
            request=request, response=response, access_token=auth_token.id
        )

        login_res = await get_login_response(
            request=request,
            response=response,
            candidate=candidate,
            message="Logged in successfully",
            auth_token=auth_token,
        )
        return login_res
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.put(
    "/update-password",
    summary="Update Current Candidate Password",
    description="Update the password for the current candidate",
    response_model=SuccessResponse,
)
async def update_password(
    candidate: Candidate = Depends(CandidateAuthHelper.get_current_candidate),
    body: dict = Body(...),
):
    """
    Endpoint to update the current candidate's password.

    Args:
        candidate (Candidate): The current candidate, provided by dependency injection.
        body (UpdatePasswordRequest): Request body containing old and new passwords.

    Returns:
        SuccessResponse: The response containing status and optional data.
    """
    try:
        old_password = body.get("old_password")
        new_password = body.get("new_password")
        confirm_password = body.get("confirm_password")
        candidate_auth = candidate.candidate_auth

        # Check if the old password matches
        if not candidate_auth.match_password(old_password):
            raise CustomValidationError(error="Old password does not match.")

        password_hash = PasswordValidate(
            new_password, field="New Password", min_length=8, max_length=20
        )
        PasswordValidate(
            confirm_password, field="Confirm Password", min_length=8, max_length=20
        )

        if new_password != confirm_password:
            raise CustomValidationError(error="Confirm password does not match.")

        candidate_auth.password = password_hash
        candidate_auth.save()

        return SuccessResponse(message="Password updated successfully")
    except CustomValidationError as e:
        raise e
    except ValueError as e:
        raise e
    except Exception as e:
        logging.error(f"Verify Current Admin Exception: {str(e)}")
        raise CustomValidationError(error=ConstantMessages.SOMETHING_WENT_WRONG)


@router.delete(
    "/logout",
    summary="Logout Current Candidate",
    description="Logout the current candidate by deleting the authentication token.",
    response_model=MessageResponse,
)
async def logout(
    request: Request,
    response: Response,
    auth_token: AuthToken = Depends(CandidateAuthHelper.get_current_auth_token),
):
    """
    Logout the current candidate by deleting the authentication token.

    - **auth_token**: The authentication token associated with the current candidate.
    """

    # Delete the authentication token instance from the database
    await CandidateAuthHelper.clear_auth_cookie(request=request, response=response)
    try:
        auth_token.delete_instance()
    except Exception as e:
        logging.error(f"Exception in delete auth token: {str(e)}")

    # Return a success message
    return MessageResponse(message="Logged out successfully.")
