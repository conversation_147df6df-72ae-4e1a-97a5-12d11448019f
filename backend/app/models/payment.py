from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    CharField,
    DateTimeField,
    BigIntegerField,
    SQL,
    IntegerField,
    DecimalField,
)
from app.models.mixins.versioning_mixin import VersioningMixin


class Payment(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.business import Business
    from app.models.payment_plan import PaymentPlan

    id = BigIntegerField(primary_key=True)
    business_id = BigIntegerField(null=False)

    stripe_payment_id = CharField(unique=True)
    amount_received = DecimalField(max_digits=10, decimal_places=2)
    currency = CharField(max_length=3)
    event_status = CharField()
    payment_plan_id = IntegerField(null=True)
    credit = BigIntegerField(null=False, default=0)
    business_id = IntegerField(null=True)
    payment_intent_id = CharField(null=True)  # Optional field for Payment Intent ID
    payment_method = CharField(null=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    business = ForeignKeyField(Business, null=False, backref="payments", lazy_load=True)

    payment_plan = ForeignKeyField(
        PaymentPlan, backref="payments", null=True
    )  # Foreign Key reference to Payment Plan

    class Meta:
        table_name = "payments"

    @classmethod
    def save_payment(cls, payment_intent_data):
        """
        Save a Stripe payment intent in the database.
        """
        from app.models.payment_plan import PaymentPlan

        plan_id = payment_intent_data["metadata"].get("payment_plan_id")

        payment_plan = PaymentPlan.get(PaymentPlan.id == plan_id)

        payment = Payment.create(
            stripe_payment_id=payment_intent_data["id"],
            amount_received=payment_intent_data["amount_received"]
            / 100.0,  # Stripe stores amounts in cents
            currency=payment_intent_data["currency"],
            event_status=payment_intent_data["status"],
            business_id=payment_intent_data["metadata"].get("business_id"),
            payment_plan_id=payment_plan.id,
            credit=payment_plan.credit,
            payment_intent_id=payment_intent_data["id"],
            payment_method=payment_intent_data["payment_method"],
        )
        business = payment.business

        if payment.event_status == "succeeded":
            business.payment_status = 1
            business.save()

        return payment

    def info(self):
        return {
            "id": self.id,
            "business_id": self.business_id,
            "stripe_payment_id": self.stripe_payment_id,
            "amount_received": float(self.amount_received),  # Convert Decimal to float
            "currency": self.currency,
            "event_status": self.event_status,
            "payment_plan_id": self.payment_plan_id,
            "credit": self.credit,
            "payment_intent_id": self.payment_intent_id,
            "payment_method": self.payment_method,
        }
