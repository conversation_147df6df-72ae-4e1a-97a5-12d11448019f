from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    BigIntegerField,
    SQL,
    ForeignKeyField,
    TextField,
)
import json


class ResumeExtractor(ActiveRecord):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business

    id = BigIntegerField(primary_key=True, index=True)
    email = CharField(null=False)
    resume = TextField(null=True)
    data = TextField(constraints=[SQL("DEFAULT {}")])
    business_id = BigIntegerField(index=True)
    created_by_id = BigIntegerField(index=True)

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="resume_extractors", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="resume_extractors", lazy_load=True
    )

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    @property
    def resume_url(self):
        return self.get_url(self.resume)

    @property
    def data_object(self):
        try:
            return json.loads(self.data)
        except Exception:
            return None

    def info(self):
        return {
            "id": self.id,
            "email": self.email,
            "resume_url": self.resume_url or "",
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    # set in list
    def save(self, *args, **kwargs):
        # Ensure achievements is a JSON-encoded string
        if isinstance(self.data, dict):
            self.data = json.dumps(self.data)
        else:
            self.data = json.dumps({})
        super(ResumeExtractor, self).save(*args, **kwargs)

    class Meta:
        table_name = "resume_extractors"
