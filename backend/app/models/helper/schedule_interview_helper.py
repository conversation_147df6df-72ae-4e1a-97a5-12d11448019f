from typing import Tuple, Dict, List
from peewee import fn
from collections import defaultdict
from datetime import datetime, timedelta


class ScheduleInterviewHelper:
    @staticmethod
    def get_interviews_count_by_grouping(
        today: datetime, grouping_type: str, business_id: int, employee_id: int
    ):
        # prevent circular import
        from app.models.schedule_interview import ScheduleInterview

        title = f"Interviews Stats"
        yLabel = "Total Interviews"

        # Base query
        query = ScheduleInterview.select(
            fn.COUNT(ScheduleInterview.id).alias("count")
        ).where(
            (ScheduleInterview.business_id == business_id)
            & (
                (ScheduleInterview.created_by_id == employee_id)
                | (ScheduleInterview.interviewer_id == employee_id)
            )
        )

        if grouping_type == "weekly":
            # Calculate the start date for the last 6 weeks
            six_weeks_ago = today - timedelta(weeks=6)
            week_labels = []
            week_counts = defaultdict(lambda: 0)

            # Prepare the query for weekly count for the last 6 weeks using YEARWEEK
            query = (
                query.select(
                    fn.YEARWEEK(ScheduleInterview.interview_at, 1).alias("week"),
                    fn.COUNT(ScheduleInterview.id).alias("count"),
                )
                .where(ScheduleInterview.interview_at >= six_weeks_ago)
                .group_by(fn.YEARWEEK(ScheduleInterview.interview_at, 1))
                .order_by(fn.YEARWEEK(ScheduleInterview.interview_at, 1))
            )

            # Generate labels for the last 6 weeks
            for i in range(6):
                week_start = today - timedelta(weeks=i)
                week_start_date = week_start - timedelta(
                    days=week_start.weekday()
                )  # Monday of the week
                week_end_date = week_start_date + timedelta(days=6)
                week_label = f"{week_start_date.strftime('%b %d')} - {week_end_date.strftime('%b %d')}"
                week_labels.append(week_label)

            week_labels.reverse()  # Reverse to show in order from oldest to newest

            # Execute the query
            results = list(query)

            # Fill in the query results in the dictionary
            for result in results:
                year = result.week // 100
                week = result.week % 100
                # Calculate the start date of the week
                week_start_date = datetime.strptime(f"{year} {week - 1} 1", "%Y %U %w")
                week_end_date = week_start_date + timedelta(days=6)
                week_key = f"{week_start_date.strftime('%b %d')} - {week_end_date.strftime('%b %d')}"
                week_counts[week_key] = result.count

            # Return labels and counts
            first_week = today - timedelta(weeks=5)
            last_week = today - timedelta(weeks=0)

            # Adjust to the start of the week (assuming week_start is Monday)
            first_week_date = first_week - timedelta(days=first_week.weekday())
            last_week_date = (
                last_week - timedelta(days=last_week.weekday()) + timedelta(days=6)
            )

            # Format the date range
            xLabel = f"Week ({first_week_date.strftime('%d').lstrip('0')}th {first_week_date.strftime('%b')} to {last_week_date.strftime('%d').lstrip('0')}th {last_week_date.strftime('%b')})"

            # Return labels and counts
            return week_labels, week_counts, title, xLabel, yLabel

        elif grouping_type == "monthly":
            # Calculate the start date for the last 12 months
            twelve_months_ago = today.replace(day=1) - timedelta(days=365)
            month_labels = []
            month_counts = defaultdict(lambda: 0)

            # Prepare the query for monthly count for the last 12 months using DATE_FORMAT
            query = (
                query.select(
                    fn.DATE_FORMAT(ScheduleInterview.interview_at, "%Y-%m").alias(
                        "month"
                    ),
                    fn.COUNT(ScheduleInterview.id).alias("count"),
                )
                .where(ScheduleInterview.interview_at >= twelve_months_ago)
                .group_by(fn.DATE_FORMAT(ScheduleInterview.interview_at, "%Y-%m"))
                .order_by(fn.DATE_FORMAT(ScheduleInterview.interview_at, "%Y-%m"))
            )

            # Generate labels for the last 12 months
            for i in range(12):
                month = today.replace(day=1) - timedelta(days=i * 30)
                month_label = month.strftime("%b %Y")  # or '%Y %b' for "2023 Oct"
                month_labels.append(month_label)

            month_labels.reverse()  # Reverse to show in order from oldest to newest

            # Execute the query
            results = list(query)

            # Fill in the query results in the dictionary
            for result in results:
                month_key = datetime.strptime(result.month, "%Y-%m").strftime(
                    "%b %Y"
                )  # or '%Y %b'
                month_counts[month_key] = result.count

            xLabel = f"Month ({month_labels[0]} - {month_labels[-1]})"
            # Return labels and counts
            return month_labels, month_counts, title, xLabel, yLabel

        elif grouping_type == "daily":
            # Calculate the start date for the last 30 days
            thirty_days_ago = today - timedelta(days=30)
            day_labels = []
            day_counts = defaultdict(lambda: 0)

            # Prepare the query for daily count for the last 30 days using DATE
            query = (
                query.select(
                    fn.DATE(ScheduleInterview.interview_at).alias("date"),
                    fn.COUNT(ScheduleInterview.id).alias("count"),
                )
                .where(ScheduleInterview.interview_at >= thirty_days_ago)
                .group_by(fn.DATE(ScheduleInterview.interview_at))
                .order_by(fn.DATE(ScheduleInterview.interview_at))
            )

            # Generate labels for the last 30 days
            for i in range(30):
                day = today - timedelta(days=i)
                day_label = day.strftime("%b %d, %Y")  # or '%Y-%m-%d'
                day_labels.append(day_label)

            day_labels.reverse()  # Reverse to show in order from oldest to newest

            # Execute the query
            results = list(query)

            # Fill in the query results in the dictionary
            for result in results:
                day_key = datetime.strptime(result.date, "%Y-%m-%d").strftime(
                    "%b %d, %Y"
                )  # or '%Y-%m-%d'
                day_counts[day_key] = result.count

            xLabel = f"Daily ({day_labels[0]} - {day_labels[-1]})"

            return day_labels, day_counts, title, xLabel, yLabel

        elif grouping_type == "yearly":
            # Calculate the start year for the last 5 years
            five_years_ago = today.year - 5
            year_labels = []
            year_counts = defaultdict(lambda: 0)

            # Prepare the query for yearly count for the last 5 years using YEAR
            query = (
                query.select(
                    fn.YEAR(ScheduleInterview.interview_at).alias("year"),
                    fn.COUNT(ScheduleInterview.id).alias("count"),
                )
                .where(fn.YEAR(ScheduleInterview.interview_at) >= five_years_ago)
                .group_by(fn.YEAR(ScheduleInterview.interview_at))
                .order_by(fn.YEAR(ScheduleInterview.interview_at))
            )

            # Generate labels for the last 5 years
            for i in range(5):
                year_label = str(today.year - i)
                year_labels.append(year_label)

            year_labels.reverse()  # Reverse to show in order from oldest to newest

            # Execute the query
            results = list(query)

            # Fill in the query results in the dictionary
            for result in results:
                year_key = str(result.year)
                year_counts[year_key] = result.count

            xLabel = f"Year ({year_labels[0]} - {year_labels[-1]})"

            # Return labels and counts
            return year_labels, year_counts, title, xLabel, yLabel

        else:
            raise ValueError(f"Invalid grouping type: {grouping_type}")

    @staticmethod
    def get_interviews_status_counts(
        business_id: int,
        employee_id: int,
    ) -> Tuple[List[Dict[str, any]], str]:
        # prevent circular import
        from app.models.schedule_interview import ScheduleInterview

        status_counts = {"Scheduled": 0, "Cancelled": 0, "Rejected": 0, "Others": 0}

        # Base query to get counts by status
        query = (
            ScheduleInterview.select(
                ScheduleInterview.status, fn.COUNT(ScheduleInterview.id).alias("count")
            )
            .where(
                (ScheduleInterview.business_id == business_id)
                & (
                    (ScheduleInterview.created_by_id == employee_id)
                    | (ScheduleInterview.interviewer_id == employee_id)
                )
            )
            .group_by(ScheduleInterview.status)
        )

        results = list(query)

        # Populate status_counts with results
        for result in results:
            status = result.status
            count = result.count
            status_name = status.name
            if status_name == "Scheduled" or status_name == "Rescheduled":
                status_counts["Scheduled"] = count
            elif status_name == "Cancelled":
                status_counts["Cancelled"] = count
            elif status_name == "Rejected":
                status_counts["Rejected"] = count
            else:
                status_counts["Others"] = count

        # Convert to the desired format
        response_data = [
            {"id": i, "value": status_counts[label], "label": label}
            for i, label in enumerate(["Scheduled", "Cancelled", "Rejected", "Others"])
        ]

        if len(results) == 0:
            response_data = [{"id": 0, "value": 1, "label": "No Data"}]

        title = "Interview Status Distribution"
        return response_data, title
