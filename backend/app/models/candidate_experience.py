from app.models.base import ActiveRecord
from peewee import (
    CharF<PERSON>,
    ForeignKeyField,
    BooleanField,
    DateTimeField,
    BigIntegerField,
    SQL,
    DateField,
    TextField,
)
import json


class CandidateExperience(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=True)
    is_current_experience = BooleanField(constraints=[SQL("DEFAULT false")])
    employer = CharField()
    position = CharField()
    start_date = DateField()
    end_date = DateField(null=True)
    is_deleted = BooleanField(default=False)
    responsibilities = TextField(constraints=[SQL("DEFAULT []")])

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # association
    candidate = ForeignKeyField(
        Candidate, backref="candidate_experiences", lazy_load=True
    )

    @property
    def responsibilities_list(self):
        # Decode JSON string to list, with error handling
        try:
            return json.loads(self.responsibilities)
        except json.JSONDecodeError as e:
            print(e, "error")
            return []

    # set in list
    def save(self, *args, **kwargs):
        # Ensure responsibilities is a JSON-encoded string
        if isinstance(self.responsibilities, list):
            self.responsibilities = json.dumps(self.responsibilities)
        else:
            self.responsibilities = json.dumps([])
        super(CandidateExperience, self).save(*args, **kwargs)

    def info(self):
        return {
            "id": self.id,
            "employer": self.employer,
            "position": self.position,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "responsibilities": self.responsibilities_list,
            "is_current_experience": self.is_current_experience,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_experiences"
