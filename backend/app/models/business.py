from app.models.base import ActiveRecord
from peewee import (
    SmallIntegerField,
    CharField,
    ForeignKeyField,
    BooleanField,
    DateTimeField,
    BigIntegerField,
    SQL,
    fn,
    TextField,
    DoesNotExist,
)
from datetime import datetime, timedelta
from pytz import UTC
from app.utils import SubdomainUtils
from pydantic import BaseModel
import random
from app.models.mixins.versioning_mixin import VersioningMixin


class Business(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.user import User
    from app.models.payment_plan import PaymentPlan

    id = BigIntegerField(primary_key=True, index=True)
    name = CharField()
    website = CharField(unique=True, index=True)
    subdomain = CharField(unique=True, index=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    email = CharField(unique=True, index=True)
    email_verified = BooleanField(constraints=[SQL("DEFAULT false")])
    contact_number = CharField()
    location = TextField()
    otp = CharField(null=True)
    otp_expire_at = DateTimeField(null=True)
    payment_status = SmallIntegerField(constraints=[SQL("DEFAULT 0")])
    timezone = CharField(max_length=100, default="UTC")
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    user_id = BigIntegerField()
    user = ForeignKeyField(User, backref="businesses", lazy_load=True)

    # Generate a 6-digit OTP
    def generate_otp(self):
        self.otp = "".join(random.choices("0123456789", k=6))
        self.otp_expire_at = datetime.now(UTC) + timedelta(minutes=15)
        self.save()
        return self

    def is_otp_expired(self):
        if self.otp_expire_at is None:
            return True

        expire_at = self.otp_expire_at
        if self.otp_expire_at.tzinfo:
            expire_at = self.otp_expire_at.astimezone(UTC)
        return datetime.utcnow() > expire_at

    def info(self, show_user: bool = False):
        info = {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "website": self.website,
            "subdomain": self.subdomain,
            "contact_number": self.contact_number,
            "location": self.location,
            "email_verified": self.email_verified,
            "payment_status": self.payment_status,
            "status": self.status,
            "timezone": self.timezone,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
            "plan_name": (
                (self.current_payment and self.current_payment.payment_plan.name) or "-"
            ),
        }

        if show_user:
            info = {**info, "user_email": self.user.email}
        return info

    @property
    def current_payment(self):
        try:
            from app.models.payment import Payment

            # Assuming 'payments' is a related model (like a foreign key or backref)
            return (
                self.payments.where(Payment.event_status == "succeeded")
                .order_by(Payment.created_at.desc())
                .first()
            )
        except DoesNotExist:
            return None

    @property
    def total_credits(self):
        try:
            current_payment = self.current_payment
            if current_payment is None:
                return 0
            from app.models.payment import Payment

            # Assuming 'payments' is a related model (like a foreign key or backref)
            remaining_credit = self.remaining_credits
            total_credits = 0
            all_payments = self.payments.where(
                Payment.event_status == "succeeded"
            ).order_by(
                Payment.id.desc()
            )  # Assuming 'id' represents the most recent order

            # Convert query into a list for easier handling
            payments = list(all_payments)

            # Iterate through payments incrementally (last 1, last 2, last 3, etc.)
            for i in range(1, len(payments) + 1):
                current_sum = sum(
                    p.credit for p in payments[:i]
                )  # Sum credits for the last `i` payments

                if remaining_credit > payments[0].credit:
                    # Include payments up to `i` and continue summing if remaining_credit exceeds the first payment
                    total_credits = current_sum
                elif remaining_credit == payments[0].credit:
                    # Stop at `i` payments if remaining_credit equals the first payment
                    total_credits = current_sum
                    break
                elif remaining_credit < payments[0].credit:
                    # Use only the first payment's credit if remaining_credit is less
                    total_credits = payments[0].credit
                    break

            return total_credits
        except DoesNotExist:
            return None

    @property
    def remaining_credits(self):
        try:
            from app.models.payment import Payment
            from app.models.api_key_usage import ApiKeyUsage

            # Sum the `credit` field where `event_status` is 'succeeded'
            # Assuming 'payments' is a related model (like a foreign key or backref)
            total_available_credit = (
                self.payments.select(
                    fn.SUM(Payment.credit).alias("total_available_credit")
                )
                .where(Payment.event_status == "succeeded")
                .scalar()
                or 0  # If no records found, return 0
            )

            total_used_credit = (
                ApiKeyUsage.select()
                .where(
                    ApiKeyUsage.business == self.id,
                    ApiKeyUsage.response_status == "Completed",
                )
                .count()
                or 0  # Return 0 if no records are found
            )
            remaining_credit = (
                total_available_credit - total_used_credit
                if total_available_credit
                else 0
            )
            return int(remaining_credit)

        except Exception as e:
            print("Exception as e", e)
            return 0

    def create_business_accounts(self):
        from app.models.user import User
        from app.models.user_type import UserType
        from app.models.employee_role import EmployeeRole
        from app.models.employee import Employee

        super_admin_const = "Super Admin"
        employee_role = EmployeeRole.get_or_none(EmployeeRole.name == super_admin_const)
        if not employee_role:
            return None

        super_admin_obj = UserType.get_or_none(UserType.name == super_admin_const)
        employees_data = []

        users = User.select().where(User.user_type_id == super_admin_obj.id)

        for user in users:
            employee_data = {
                "email": user.email,
                "password": user.password,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "status": user.status,
                "employee_role_id": employee_role.id,
                "business_id": self.id,
            }
            employees_data.append(employee_data)

        # Add business owner as employee
        business_owner_data = {
            "email": self.user.email,
            "password": self.user.password,
            "first_name": self.user.first_name,
            "last_name": self.user.last_name,
            "status": self.user.status,
            "employee_role_id": employee_role.id,
            "business_id": self.id,
        }
        if self.user.user_type.name != super_admin_const:
            employees_data.append(business_owner_data)
        Employee.insert_many(employees_data).execute()

    def create_default_node_permissions(self):
        from app.models.node import Node
        from app.models.permission import Permission
        from app.models.employee_role import EmployeeRole
        from app.models.business_employee_role_permission import (
            BusinessEmployeeRolePermission,
        )

        nodes = Node.where({"name": "Dashboard", "type": "EmployeeNode"})
        permission_ids = [permission.id for permission in Permission.all()]
        node_ids = [node.id for node in nodes]
        employee_roles = EmployeeRole.select().where(EmployeeRole.business_id.is_null())
        employee_role_ids = [employee_role.id for employee_role in employee_roles]
        row_query = [
            {
                "business_id": self.id,
                "permission_id": perm_id,
                "node_id": node_id,
                "employee_role_id": emp_role_id,
            }
            for perm_id in permission_ids
            for node_id in node_ids
            for emp_role_id in employee_role_ids
        ]
        BusinessEmployeeRolePermission.insert_many(row_query).execute()

    @classmethod
    def generate_unique_subdomain(cls, name):
        subdomain = SubdomainUtils.generate_subdomain(business_name=name)
        if len(subdomain) > 50:
            subdomain = subdomain[:40]
        while cls.select().where(Business.subdomain == subdomain).exists():
            subdomain = SubdomainUtils.ensure_unique_subdomain(business_name=name)
        return subdomain

    @classmethod
    def is_website_exists(cls, website):
        # Normalize the domain
        website = (
            website.lower()
            .replace("http://", "")
            .replace("https://", "")
            .replace("www.", "")
        )

        normalized_website = fn.REPLACE(
            fn.REPLACE(
                fn.REPLACE(fn.LOWER(cls.website), "http://", ""), "https://", ""
            ),
            "www.",
            "",
        )

        # Check if the domain exists in the database
        return cls.select().where(normalized_website == website).exists()

    # class meta
    class Meta:
        table_name = "businesses"


class BusinessResponse(BaseModel):
    id: int
    name: str
    email: str
    email_verified: bool
    subdomain: str
    website: str
