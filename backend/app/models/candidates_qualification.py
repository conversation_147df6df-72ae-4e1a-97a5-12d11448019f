from app.models.base import ActiveRecord
from peewee import CompositeKey, ForeignKeyField, BigIntegerField


class CandidatesQualification(ActiveRecord):
    # to prevent circular import
    from app.models.qualification import Qualification
    from app.models.candidate import Candidate

    qualification_id = BigIntegerField()
    candidate_id = BigIntegerField()

    # association
    candidate = ForeignKeyField(Candidate, backref="candidate_qualifications")
    qualification = ForeignKeyField(Qualification, backref="candidate_qualifications")

    class Meta:
        table_name = "candidates_qualifications"
        primary_key = CompositeKey("candidate", "qualification")
