from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    CharField,
    DateTimeField,
    BigIntegerField,
    SmallIntegerField,
    SQL,
)


class Qualification(ActiveRecord):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business

    id = <PERSON>Inte<PERSON><PERSON>ield(primary_key=True)
    name = Char<PERSON>ield(null=False)
    description = CharField()

    business_id = BigIntegerField(null=True)
    created_by_id = BigIntegerField(null=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations

    created_by = ForeignKeyField(
        Employee, null=True, backref="qualifications", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="qualifications", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    @classmethod
    def find_or_create_by_name(cls, name: str, **fields):
        qualification = cls.get_record_exist_with_lower(Qualification.name, name)
        if qualification is None:
            qualification = cls.create(**fields, name=name)
        return qualification

    @property
    def candidates(self):
        from app.models import Candidate, CandidatesQualification

        return (
            Candidate.select()
            .join(CandidatesQualification)
            .where(CandidatesQualification.qualification == self)
        )

    class Meta:
        table_name = "qualifications"
        indexes = (("name", "business"), True)
