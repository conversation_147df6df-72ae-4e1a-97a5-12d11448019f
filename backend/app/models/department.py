from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    CharField,
    DateTimeField,
    BigIntegerField,
    SmallIntegerField,
    SQL,
)
from pydantic import BaseModel
from app.models.mixins.versioning_mixin import VersioningMixin


class Department(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business

    id = BigIntegerField(primary_key=True)
    name = Char<PERSON><PERSON>(null=False)
    description = Char<PERSON>ield()

    business_id = BigIntegerField(null=True)
    created_by_id = BigIntegerField(null=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    is_default = SmallIntegerField(constraints=[SQL("DEFAULT 0")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="departments", lazy_load=True
    )
    business = Foreign<PERSON>eyField(
        Business, null=True, backref="departments", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status,
            "is_default": self.is_default,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "departments"


class DepartmentResponse(BaseModel):
    id: int
    business_id: int
