from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>TimeField,
    BigAutoField,
    ForeignKeyField,
    BigIntegerField,
    SQL,
)
from app.models.base import ActiveRecord


class ApiKeyUsage(ActiveRecord):
    # to prevent circular import
    from app.models.business import Business
    from app.models.employee import Employee

    id = BigAuto<PERSON>ield(primary_key=True)
    api_key = Char<PERSON>ield()
    created_by_id = BigIntegerField()
    business_id = BigIntegerField()
    ip = CharField()
    host = Char<PERSON><PERSON>()
    origin = CharField()
    user_agent = CharField()
    model_name = CharField()
    token_used = BigIntegerField(constraints=[SQL("DEFAULT 0")])

    response_status = CharField()
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="api_key_usages", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=False, backref="api_key_usages", lazy_load=True
    )

    class Meta:
        table_name = "api_key_usages"

    def info(self):
        return {
            "id": self.id,
            "api_key": self.api_key,
            "business_id": self.business_id,
            "ip": self.ip,
            "host": self.host,
            "origin": self.origin,
            "user_agent": self.user_agent,
            "model_name": self.model_name,
            "token_used": self.token_used,
            "response_status": self.response_status,
            "created_at": self.created_at,
        }
