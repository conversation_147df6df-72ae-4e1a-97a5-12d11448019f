from app.models.base import ActiveRecord
from peewee import (
    CharField,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SQL,
    DateField,
    BooleanField,
)


class CandidateEducationInformation(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate
    from app.models.qualification import Qualification

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=True)
    qualification_id = BigIntegerField(null=True)
    degree = CharField()
    university = CharField()
    grade = CharField(null=True)
    start_date = DateField()
    end_date = DateField()
    is_deleted = BooleanField(default=False)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # association
    candidate = ForeignKeyField(
        Candidate, backref="candidate_education_informations", lazy_load=True
    )
    qualification = ForeignKeyField(
        Qualification, backref="candidate_education_informations", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "degree": self.degree,
            "university": self.university,
            "grade": self.grade,
            "start_date": self.start_date,
            "end_date": self.end_date,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_education_informations"
