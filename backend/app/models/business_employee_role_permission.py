from app.models.base import ActiveRecord
from peewee import Date<PERSON><PERSON><PERSON><PERSON>, BigIntegerField, SQL, ForeignKeyField


class BusinessEmployeeRolePermission(ActiveRecord):
    # to prevent circular import
    from app.models.permission import Permission
    from app.models.employee_role import EmployeeRole
    from app.models.node import Node
    from app.models.business import Business

    id = BigIntegerField(primary_key=True)

    node_id = BigIntegerField(null=False)
    permission_id = BigIntegerField(null=False)
    employee_role_id = BigIntegerField(null=False)
    business_id = BigIntegerField(null=False)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    node = ForeignKeyField(
        Node, backref="business_employee_role_permissions", lazy_load=True
    )
    business = ForeignKeyField(
        Business, backref="business_employee_role_permissions", lazy_load=True
    )
    permission = ForeignKeyField(
        Permission, backref="business_employee_role_permissions", lazy_load=True
    )
    employee_role = ForeignKeyField(
        EmployeeRole, backref="business_employee_role_permissions", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "node_id": self.node_id,
            "permission_id": self.permission_id,
            "employee_role_id": self.employee_role_id,
        }

    class Meta:
        table_name = "business_employee_role_permissions"
