from app.models.base import ActiveRecord
from peewee import (
    BigIntegerField,
    DateTimeField,
    TextField,
    SQL,
    ForeignKeyField,
    BooleanField,
)
from app.models.concern.enum import EnumField, QuestionType
import json


class ScreeningInterviewQuestion(ActiveRecord):
    # from prevent circular import.
    from app.models.opportunity import Opportunity
    from app.models.business import Business

    id = BigIntegerField(primary_key=True, index=True)
    question = TextField(null=False)
    question_type = EnumField(QuestionType)
    answer = TextField(null=False)
    options = TextField()
    starter_code = TextField()
    language = TextField()
    test_cases = TextField()
    is_deleted = BooleanField(default=False)
    opportunity_id = BigIntegerField(null=False)
    business_id = BigIntegerField(null=False)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    opportunity = ForeignKeyField(
        Opportunity, null=False, backref="screening_interview_questions", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=False, backref="screening_interview_questions", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "opportunity_id": self.opportunity_id,
            "business_id": self.business_id,
            "question": self.question,
            "question_type": self.question_type,
            "options": self.question_option_list,
            "answer": self.answer,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    def maskedinfo(self):
        return {
            "id": self.id,
            "opportunity_id": self.opportunity_id,
            "business_id": self.business_id,
            "question": self.question,
            "question_type": self.question_type,
            "options": self.options,
            "test_cases_list": self.test_cases_list,
            "created_at": str(f"{self.created_at} utc"),
        }

    @property
    def test_cases_list(self):
        # Decode JSON string to list, with error handling
        try:
            return json.loads(self.test_cases)
        except json.JSONDecodeError as e:
            print(e, "error")
            return []

    @property
    def question_option_list(self):
        # Decode JSON string to list, with error handling
        try:
            questions_data = json.loads(self.options)
            return [
                str(item).strip()
                for item in questions_data
                if item is not None and str(item).strip()
            ]
        except json.JSONDecodeError as e:
            print(e, "error")
            return []

    # set in list
    def save(self, *args, **kwargs):
        # Ensure achievements is a JSON-encoded string
        if isinstance(self.test_cases, list):
            self.test_cases = json.dumps(self.test_cases)
        else:
            self.options = json.dumps([])

        if isinstance(self.options, list):
            self.options = json.dumps(self.options)
        else:
            self.options = json.dumps([])
        super(ScreeningInterviewQuestion, self).save(*args, **kwargs)

    @property
    def build_test_cases(self):
        return [
            {
                "input": str(case["input"]),
                "expected_output": str(case["expected_output"]),
            }
            for case in self.test_cases_list
        ]

    class Meta:
        table_name = "screening_interview_questions"
        indexes = (("opportunity_id", "business_id"), True)
