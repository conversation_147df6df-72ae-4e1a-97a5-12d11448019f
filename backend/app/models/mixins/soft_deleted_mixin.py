from peewee import <PERSON><PERSON><PERSON><PERSON>ield, Model


class SoftDeletedMixin(Model):
    is_deleted = BooleanField(default=False)

    @classmethod
    def get(cls, *query, with_deleted=False, **kwargs):
        if hasattr(cls, "is_deleted") and not with_deleted:
            query = list(query) + [cls.is_deleted == False]
        return super().get(*query, **kwargs)

    @classmethod
    def get_or_none(cls, *query, with_deleted=False, **kwargs):
        try:
            return cls.get(*query, with_deleted=with_deleted, **kwargs)
        except cls.DoesNotExist:
            return None

    def delete_instance(self, *args, **kwargs):
        """
        Soft delete the instance by setting `is_deleted` to True.
        """
        self.is_deleted = True
        super().save(*args, **kwargs)

    def restore(self):
        """
        Restore a soft-deleted instance by setting `is_deleted` to False.
        """
        self.is_deleted = False
        super().save()

    @classmethod
    def select(cls, *selection):
        """
        Override the select method to exclude soft-deleted records by default.
        """
        return super().select(*selection).where(cls.is_deleted == False)

    @classmethod
    def with_deleted(cls, *selection):
        """
        Return all records, including soft-deleted ones.
        """
        return super().select(*selection)

    @classmethod
    def only_deleted(cls, *selection):
        """
        Return only soft-deleted records.
        """
        return super().select(*selection).where(cls.is_deleted == True)
