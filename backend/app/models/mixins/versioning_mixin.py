import json
from datetime import datetime
from peewee import ForeignKeyField
from enum import Enum
from decimal import Decimal


class CustomInstanceEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Enum):
            return obj.value  # Serialize Enum by its value
        elif isinstance(obj, Decimal):
            return float(obj)  # Convert Decimal to float
        return super().default(obj)


class VersioningMixin:
    # this class is basically used to save history done by any changes

    def create_version_entry(self, action):
        try:
            from app.models.version import Version
            from app.context import get_whodoneit, get_request_headers

            data = {
                field.name: getattr(self, field.name)
                for field in self._meta.fields.values()
                if not isinstance(field, ForeignKeyField)
            }

            current_user = get_whodoneit()
            request_headers = get_request_headers()
            version = Version.create(
                whodoneit=(
                    f"{current_user.__class__.__name__}_{current_user.id}"
                    if current_user
                    else "unknown"
                ),
                record=json.dumps(data, cls=CustomInstanceEncoder),
                model_name=self.__class__.__name__,
                model_id=self._pk,
                action=action,
                request_headers=json.dumps(request_headers),
            )
            version.save()
        except Exception as e:
            print(f"Exception in  ------ Save History --- {str(e)}")
