from app.models.base import <PERSON><PERSON><PERSON>ord
from peewee import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, BigIntegerField, SQL, ForeignKeyField
from pydantic import BaseModel
from typing import Optional
from datetime import datetime, timedelta
from pytz import UTC
from app.utils.password_utils import PasswordUtils
import random


class CandidateAuth(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate
    from app.models.business import Business

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField()

    password = CharField()
    otp = CharField(null=True)
    otp_expire_at = DateTimeField(null=True)

    # associations
    candidate = ForeignKeyField(Candidate, backref="candidate_auths", lazy_load=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    def generate_otp(self):
        # Generate a 6-digit OTP
        self.otp = "".join(random.choices("0123456789", k=6))
        self.otp_expire_at = datetime.now(UTC) + timedelta(minutes=15)
        self.save()
        return self

    @staticmethod
    def authenticate(candidate_id: int, password: str):
        candidate = CandidateAuth.get_or_none(candidate_id=candidate_id)
        if not candidate:
            return False

        # Verify the password
        if not candidate.match_password(password):
            return False
        return candidate

    def match_password(self, password):
        return PasswordUtils.verify_password(password, self.password)

    def is_otp_expired(self):
        if self.otp_expire_at is None:
            return True

        expire_at = self.otp_expire_at
        if self.otp_expire_at.tzinfo:
            expire_at = self.otp_expire_at.astimezone(UTC)
        return datetime.utcnow() > expire_at

    def info(self):
        return {
            "id": self.candidate_id,
            "name": self.candidate.name,
            "email": self.candidate.email,
            "business_id": self.candidate.business_id,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    # Override the default select query in Meta
    @classmethod
    def select_candidate(cls, *selection):
        from app.models.candidate import Candidate

        base_query = CandidateAuth.select(
            CandidateAuth, Candidate.name, Candidate.email, Candidate.business_id
        ).join(Candidate, on=(CandidateAuth.candidate_id == Candidate.id))

        # Return base query or apply additional selections if provided
        if selection:
            return base_query.select(*selection)
        return base_query

    class Meta:
        table_name = "candidate_auths"


class CandidateAuthResponse(BaseModel):
    id: int
    email: str
    name: str = ""
    business_id: int
    access_token: str


class CandidateAuthLoginResponse(BaseModel):
    message: str = "Login Success"
    success: bool = True
    status_code: int = 200
    data: Optional[CandidateAuthResponse] = None
