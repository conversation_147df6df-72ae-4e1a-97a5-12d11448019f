from app.models.base import ActiveRecord
from peewee import (
    IntegerField,
    CharField,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SmallIntegerField,
    SQL,
    BooleanField,
)
from pydantic import BaseModel
from datetime import datetime, timed<PERSON>ta
from pytz import UTC
from app.utils.password_utils import PasswordUtils
from typing import Optional, List
from app.models.mixins import VersioningMixin, SoftDeletedMixin
import random


class Employee(ActiveRecord, SoftDeletedMixin, VersioningMixin):
    # to prevent circular import
    from app.models.user import User
    from app.models.business import Business
    from app.models.employee_role import EmployeeRole

    id = BigIntegerField(primary_key=True)
    first_name = Char<PERSON>ield(max_length=100, null=True)
    last_name = Char<PERSON>ield(max_length=100, null=True)
    email = CharField(max_length=255, null=False)  # unique with business id
    password = CharField(max_length=255, null=True)

    employee_role_id = IntegerField(null=True)
    business_id = BigIntegerField()
    created_by_id = BigIntegerField(null=True)
    contact_number = Char<PERSON>ield(null=True)

    otp = CharField(null=True)
    otp_expire_at = DateTimeField(null=True)

    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    created_by = ForeignKeyField(
        "self", null=True, backref="created_employees", lazy_load=True
    )
    business = ForeignKeyField(Business, backref="employees", lazy_load=True)
    employee_role = ForeignKeyField(EmployeeRole, backref="employees", lazy_load=True)

    def full_name(self):
        first_name = self.first_name if self.first_name is not None else ""
        last_name = self.last_name if self.last_name is not None else ""
        return f"{first_name} {last_name}".strip()

    def generate_otp(self):
        # Generate a 6-digit OTP
        self.otp = "".join(random.choices("0123456789", k=6))
        self.otp_expire_at = datetime.now(UTC) + timedelta(minutes=15)
        self.save()
        return self

    @staticmethod
    def authenticate(business_id: int, email: str, password: str):
        employee = Employee.get_or_none(business_id=business_id, email=email, status=1)
        if not employee:
            return False

        # Verify the password
        if not employee.match_password(password):
            return False
        return employee

    def match_password(self, password):
        return PasswordUtils.verify_password(password, self.password)

    def is_otp_expired(self):
        if self.otp_expire_at is None:
            return True

        expire_at = self.otp_expire_at
        if self.otp_expire_at.tzinfo:
            expire_at = self.otp_expire_at.astimezone(UTC)
        return datetime.utcnow() > expire_at

    def info(self):
        return {
            "id": self.id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "contact_number": self.contact_number,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "employee_role_id": self.employee_role_id,
            "employee_role_name": (
                self.employee_role.name if self.employee_role else None
            ),
            "email": self.email,
            "status": self.status,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    @property
    def is_admin(self):
        return self.employee_role.name in ["Super Admin", "Admin"]

    class Meta:
        table_name = "employees"
        indexes = (("email", "business_id"), True)  # unique email with business


class SidebarNode(BaseModel):
    name: str
    path: str
    icon: Optional[str]
    type: str
    childrens: List["SidebarNode"] = []


class EmployeeResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: Optional[str] = ""
    employee_role_id: int
    employee_role: str
    business_id: int
    business_name: str
    access_token: str
    nodes: List[SidebarNode] = []


class EmployeeLoginResponse(BaseModel):
    message: str = "Login Success"
    success: bool = True
    status_code: int = 200
    data: Optional[EmployeeResponse] = None
