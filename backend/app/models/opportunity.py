import json
from app.models.base import ActiveRecord
from peewee import (
    ForeignKeyField,
    CharField,
    DateTimeField,
    BigIntegerField,
    SmallIntegerField,
    SQL,
    IntegerField,
    TextField,
)
from app.models.mixins.versioning_mixin import VersioningMixin
from app.models.concern.json_field import JSONField


class Opportunity(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business
    from app.models.department import Department
    from app.models.location import Location

    id = BigIntegerField(primary_key=True)
    title = CharField(max_length=100, null=False)  # unique with business id
    designation = CharField(max_length=100)

    number_of_vacancies = IntegerField()
    description = TextField()
    ai_response = TextField()
    responsibilities = TextField()
    salary = BigIntegerField(default=0)
    experience = IntegerField(default=0)
    questions = TextField()
    extra_fields = JSONField()

    contact_person_id = BigIntegerField(null=False)
    department_id = BigIntegerField(null=False)
    business_id = BigIntegerField(null=False)
    created_by_id = BigIntegerField(null=False)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    location = CharField(max_length=255)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    contact_person = ForeignKeyField(
        Employee, null=True, backref="opportunities", lazy_load=True
    )
    department = ForeignKeyField(Department, backref="opportunities", lazy_load=True)
    created_by = ForeignKeyField(
        Employee, null=True, backref="opportunities", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=False, backref="opportunities", lazy_load=True
    )

    @property
    def contact_person_detail(self):
        try:
            return {
                "contact_name": self.contact_person.full_name(),
                "contact_email": self.contact_person.email,
                "contact_phone": self.contact_person.contact_number,
                "contact_person_id": self.contact_person_id,
            }
        except Exception:
            return {
                "contact_name": "Deleted",
                "contact_email": None,
                "contact_phone": None,
                "contact_person_id": None,
            }

    @property
    def extra_field_data(self):
        def reverse_format_field_name(name: str) -> str:
            return " ".join(word.capitalize() for word in name.split("_"))

        if self.extra_fields:
            return {
                reverse_format_field_name(key): value
                for key, value in self.extra_fields.items()
            }
        return {}

    def info(self):
        return {
            "id": self.id,
            "title": self.title,
            "designation": self.designation,
            "number_of_vacancies": self.number_of_vacancies,
            "description": self.description,
            "salary": "" if self.salary == 0 else self.salary,
            "experience": "" if self.experience == 0 else self.experience,
            "questions": self.questions_list,
            "responsibilities": self.responsibilities,
            "department_id": self.department_id,
            "department_name": self.department.name,
            "location_names": self.location_names,
            "location_ids": self.location_ids,
            "skills": self.skills,
            "status": self.status,
            "extra_fields": self.extra_fields or {},
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
            **self.contact_person_detail,
        }

    @property
    def questions_list(self):
        # Decode JSON string to list, with error handling
        try:
            questions_data = json.loads(self.questions)
            return [
                str(item).strip()
                for item in questions_data
                if item is not None and str(item).strip()
            ]
        except json.JSONDecodeError as e:
            print(e, "error")
            return []

    @property
    def location_ids(self):
        # Decode JSON string to list, with error handling
        try:
            return json.loads(self.location)
        except json.JSONDecodeError as e:
            print(e, "error")
            return []

    @property
    def location_names(self):
        # Decode JSON string to list, with error handling
        try:
            from app.models.location import Location

            names = []
            for location in Location.select().where(Location.id.in_(self.location_ids)):
                names.append(location.full_address)
            return names
        except Exception:
            return []

    @property
    def response(self):
        # Decode JSON string to list, with error handling
        try:
            return json.loads(self.ai_response)
        except json.JSONDecodeError:
            return {}

    @property
    def skills(self):
        try:
            from app.helper import ResumeExtractorHelper

            response_skills = ResumeExtractorHelper.clean_string_only(
                self.response["skills"]
            )
            return (", ").join(response_skills)
        except:
            return None

    @property
    def skills_list(self):
        try:
            from app.helper import ResumeExtractorHelper

            return ResumeExtractorHelper.clean_string_only(self.response["skills"])
        except:
            return []

    # set in list
    def save(self, *args, **kwargs):
        if "method" not in kwargs.keys():
            # Ensure questions is a JSON-encoded string
            if isinstance(self.questions, list):
                self.questions = json.dumps(self.questions)
            else:
                self.questions = json.dumps([])
            if isinstance(self.location, list):
                self.location = json.dumps(self.location)
            else:
                self.location = json.dumps([])
            super(Opportunity, self).save(*args, **kwargs)
        else:
            super(Opportunity, self).save()

    @property
    def extract_ai_response(self):
        try:
            return {
                "skills": (self.skills_list),
                "qualifications": (
                    (self.response and self.response["qualifications"]) or []
                ),
                "total_experience": "" if self.experience == 0 else self.experience,
            }
        except:
            return None

    def extract_detail(self):
        return {
            "job_title": self.title,
            "job_type": self.department.name,
            "job_description": self.description,
            "job_questions": self.questions,
            "job_designation": self.designation,
            "responsibilities": self.responsibilities,
            "total_experience": "" if self.experience == 0 else self.experience,
        }

    @property
    def finalize_candidates(self):
        from app.models.candidate import Candidate
        from app.models.candidate_interview import CandidateInterview
        from app.models.concern.enum import CandidateInterviewStatus

        candidates = (
            Candidate.select()
            .join(CandidateInterview)
            .where(
                (CandidateInterview.opportunity_id == self.id)
                & (CandidateInterview.status == CandidateInterviewStatus.Finalize)
            )
        )

        return candidates

    class Meta:
        table_name = "opportunities"
        indexes = (("title", "business_id"), True)  # unique email with business

    def save_response(self, response: dict):
        ai_response = json.dumps(response)
        self.set_value("ai_response", ai_response)
        self.save_only()
