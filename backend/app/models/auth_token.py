from app.models.base import ActiveRecord
from peewee import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, BigIntegerField, SQL
from datetime import datetime, timedelta
from pytz import UTC
import uuid


class AuthToken(ActiveRecord):
    id = Char<PERSON>ield(primary_key=True, default=uuid.uuid4)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    expires_at = DateTimeField(
        constraints=[SQL("DEFAULT DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 60 MINUTE)")]
    )
    object_id = BigIntegerField(null=False)
    object_type = CharField(null=False)

    class Meta:
        table_name = "auth_tokens"

    def update_expiration(self):
        self.expires_at = datetime.now(UTC) + timedelta(minutes=60)
        self.save()

    def is_expired(self):
        if self.expires_at is None:
            return True

        expire_at = self.expires_at
        if self.expires_at.tzinfo:
            expire_at = self.expires_at.astimezone(UTC)
        return datetime.utcnow() > expire_at

    class Meta:
        table_name = "auth_tokens"
