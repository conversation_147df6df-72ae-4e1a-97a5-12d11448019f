from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    SmallIntegerField,
    IntegerField,
    SQL,
    BigIntegerField,
    ForeignKeyField,
)
from app.models.base import ActiveRecord
from app.models.mixins.versioning_mixin import VersioningMixin


class EmployeeRole(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.business import Business

    id = IntegerField(primary_key=True, index=True)
    name = CharField(unique=True)
    business_id = BigIntegerField(null=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    business = ForeignKeyField(
        Business, null=False, backref="api_key_usages", lazy_load=True
    )

    def info(self):
        return {"id": self.id, "name": self.name}

    class Meta:
        table_name = "employee_roles"
