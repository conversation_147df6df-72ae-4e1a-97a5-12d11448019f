from peewee import <PERSON>r<PERSON><PERSON>, DateTimeField, SmallIntegerField, IntegerField, SQL
from app.models.base import ActiveRecord


class UserType(ActiveRecord):
    id = IntegerField(primary_key=True, index=True)
    name = CharField(unique=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    class Meta:
        table_name = "user_types"
