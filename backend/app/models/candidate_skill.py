from app.models.base import ActiveRecord
from peewee import (
    CharField,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SQL,
    BooleanField,
)


class CandidateSkill(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate

    id = <PERSON>IntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=True)
    name = CharField()
    skill_type = Char<PERSON><PERSON>(choices=[("Technical", "Technical"), ("Soft", "Soft")])
    sub_skill_type = CharField()
    is_deleted = BooleanField(default=False)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # association
    candidate = ForeignKeyField(Candidate, backref="candidate_skills", lazy_load=True)

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "skill_type": self.skill_type,
            "sub_skill_type": self.sub_skill_type,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_skills"
