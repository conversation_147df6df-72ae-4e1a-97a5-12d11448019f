from app.models.base import ActiveRecord
from peewee import (
    Date<PERSON><PERSON>Field,
    BigIntegerField,
    SQL,
    ForeignKeyField,
    TextField,
)
from app.models.mixins.versioning_mixin import VersioningMixin


class CandidateOfferLetter(ActiveRecord, VersioningMixin):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.candidate import Candidate
    from app.models.opportunity import Opportunity

    id = BigIntegerField(primary_key=True, index=True)
    letter = TextField(null=True)

    created_by_id = BigIntegerField(null=True)
    candidate_id = BigIntegerField(null=True)
    opportunity_id = BigIntegerField(null=True)

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="candidate_offer_letters", lazy_load=True
    )
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_offer_letters", lazy_load=True
    )
    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidate_offer_letters", lazy_load=True
    )

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    @property
    def letter_url(self):
        return self.get_url(self.letter)

    class Meta:
        table_name = "candidate_offer_letters"
