from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SQL,
    BooleanField,
)
from app.utils import generate_unique_key
from datetime import datetime, timedelta


class DocumentUploadLinkRequest(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate
    from app.models.employee import Employee

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=False)
    employee_id = BigIntegerField(null=False)
    unique_key = Char<PERSON>ield(max_length=255, unique=True)
    expired = BooleanField(default=False)
    submitted = <PERSON>oleanField(default=False)
    adhar_certificate = <PERSON>oleanField(default=True)
    pan_certificate = BooleanField(default=True)
    degree_certificate = BooleanField(default=True)
    other_certificate = BooleanField(default=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    candidate = ForeignKeyField(
        Candidate, backref="document_upload_link_requests", lazy_load=True
    )
    employee = ForeignKeyField(
        Employee, backref="document_upload_link_requests", lazy_load=True
    )

    @classmethod
    def create_unique_record(cls, candidate_id: int, employee_id: int, columns: dict):
        unique_key = generate_unique_key(80)

        while cls.select().where(cls.unique_key == unique_key).exists():
            unique_key = generate_unique_key(80)

        record = cls.create(
            **columns,
            candidate_id=candidate_id,
            employee_id=employee_id,
            unique_key=unique_key,
            expired=False,
        )

        return record

    @property
    def expired_at(self):
        return self.created_at + timedelta(days=1)

    @property
    def is_expired(self):
        return datetime.now() > self.expired_at or self.expired

    def info(self):
        return {
            "id": self.id,
            "name": self.candidate.name,
            "email": self.candidate.email,
            "adhar_certificate": self.adhar_certificate,
            "pan_certificate": self.pan_certificate,
            "degree_certificate": self.degree_certificate,
            "other_certificate": self.other_certificate,
            "created_at": str(f"{self.created_at} utc"),
            "expired_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "document_upload_link_requests"
