from app.models.base import ActiveRecord
from peewee import DateTimeField, TextField, BigIntegerField, ForeignKeyField, SQL


class CandidateProfileComment(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate
    from app.models.business import Business
    from app.models.employee import Employee

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField()
    business_id = BigIntegerField()
    employee_id = BigIntegerField()

    comment = TextField(null=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    candidate = ForeignKeyField(Candidate, backref="candidates", lazy_load=True)
    business = ForeignKeyField(Business, backref="businesses", lazy_load=True)
    employee = ForeignKeyField(Employee, backref="employees", lazy_load=True)

    def info(self):
        return {
            "id": self.id,
            "candidate_id": self.candidate_id,
            "business_id": self.business_id,
            "employee_id": self.employee_id,
            "employee_name": (
                (self.get_instance("employee") and self.employee.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "comment": self.comment,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_profile_comments"
