from app.models.base import ActiveRecord
from peewee import BigIntegerField, CharField, TextField, DateTimeField, SQL


class ContactEnquery(ActiveRecord):

    id = BigIntegerField(primary_key=True, index=True)
    email = Char<PERSON>ield(index=True, null=False)

    first_name = Char<PERSON>ield(null=False)
    last_name = CharField()

    subject = CharField()
    message = TextField()

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    def info(self):
        return {
            "first_name": self.first_name,
            "last_name": self.last_name,
            "email": self.email,
            "subject": self.subject,
            "message": self.message,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "contact_equeries"
        indexes = (("email"), True)
