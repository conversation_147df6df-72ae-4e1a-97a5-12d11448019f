from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    BigIntegerField,
    SQL,
    IntegerField,
    ForeignKeyField,
)
from app.models.business import Business


class Location(ActiveRecord):
    id = BigIntegerField(primary_key=True, index=True)
    address = Char<PERSON>ield(null=False)
    city = CharField(null=False)
    state = CharField(null=False)
    country = CharField(null=False)
    pincode = Char<PERSON>ield(null=False)
    is_deleted = IntegerField(default=0)  # Set default to 0
    business_id = BigIntegerField()
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    deleted_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    business = ForeignKeyField(
        Business, null=False, backref="opportunities", lazy_load=True
    )

    def info(self):
        return {
            "id": self.id,
            "address": self.address,
            "city": self.city,
            "state": self.state,
            "pincode": self.pincode,
            "country": self.country,
            "is_deleted": self.is_deleted,
            "created_at": str(self.created_at),
            "updated_at": str(self.updated_at),
            "deleted_at": str(self.deleted_at),
        }

    @property
    def full_address(self):
        return (
            f"{self.address}, {self.city}, {self.state}, {self.pincode}, {self.country}"
        )

    class Meta:
        table_name = "locations"
