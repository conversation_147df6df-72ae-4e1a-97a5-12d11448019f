from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    BigIntegerField,
    TextField,
    IntegerField,
    SQL,
    DecimalField,
)
from app.models.mixins.versioning_mixin import VersioningMixin


class PaymentPlan(ActiveRecord, VersioningMixin):
    id = BigIntegerField(primary_key=True)
    name = Char<PERSON>ield(max_length=50, null=False, unique=True)  # Name is unique
    description = TextField(null=True)

    amount = DecimalField(max_digits=10, decimal_places=2, null=False)
    currency = CharField(max_length=3, null=False)

    status = IntegerField(default=1)  # TINYINT is represented as IntegerField
    credit = BigIntegerField(null=True)

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    class Meta:
        table_name = "payment_plans"

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "amount": float(self.amount),
            "currency": self.currency,
            "credit": self.credit,
            "created_at": self.created_at,
        }
