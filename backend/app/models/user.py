from app.models.base import <PERSON>R<PERSON>ord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    ForeignKeyField,
    BooleanField,
    DateTimeField,
    BigAutoField,
    SmallIntegerField,
    SQL,
    DateField,
)
from datetime import datetime, date, timedelta
from pytz import UTC
from app.utils.password_utils import PasswordUtils
import random
from pydantic import BaseModel
from typing import Optional, List


class User(ActiveRecord):
    # to prevent circular import
    from app.models.user_type import UserType

    id = BigAutoField(primary_key=True, index=True)
    email = CharField(unique=True, index=True)
    password = Char<PERSON>ield()
    first_name = Char<PERSON>ield()
    last_name = Cha<PERSON><PERSON><PERSON>(null=True)
    gender = Cha<PERSON><PERSON><PERSON>(default="Male")
    dob = DateField(default=date(2000, 1, 1))
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    email_verified = BooleanField(constraints=[SQL("DEFAULT false")])
    otp = CharField(null=True)
    otp_expire_at = DateTimeField(null=True)
    created_at = DateTimeField(default=datetime.now(UTC))
    updated_at = DateTimeField(default=datetime.now(UTC))
    user_type = ForeignKeyField(UserType, backref="users", lazy_load=True)

    def full_name(self):
        first_name = self.first_name if self.first_name is not None else ""
        last_name = self.last_name if self.last_name is not None else ""
        return f"{first_name} {last_name}".strip()

    def generate_otp(self):
        # Generate a 6-digit OTP
        self.otp = "".join(random.choices("0123456789", k=6))
        self.otp_expire_at = datetime.now(UTC) + timedelta(minutes=15)
        self.save()
        return self

    @staticmethod
    def authenticate(email: str, password: str):
        user = User.get_or_none(email=email, status=1)
        if not user:
            return False

        # Verify the password
        if not user.match_password(password):
            return False
        return user

    def match_password(self, password):
        return PasswordUtils.verify_password(password, self.password)

    def is_otp_expired(self):
        if self.otp_expire_at is None:
            return True

        expire_at = self.otp_expire_at
        if self.otp_expire_at.tzinfo:
            expire_at = self.otp_expire_at.astimezone(UTC)
        return datetime.utcnow() > expire_at

    @property
    def is_super_admin(self):
        return self.user_type.name == "Super Admin"

    # class meta
    class Meta:
        table_name = "users"


class SidebarNode(BaseModel):
    name: str
    path: str
    icon: Optional[str]
    type: str
    childrens: List["SidebarNode"] = []


class UserResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str = ""
    user_type_id: int
    user_type: str
    email_verified: bool
    access_token: str
    nodes: List[SidebarNode] = []


class UserLoginResponse(BaseModel):
    message: str = "Login Success"
    success: bool = True
    status_code: int = 200
    data: Optional[UserResponse] = None
