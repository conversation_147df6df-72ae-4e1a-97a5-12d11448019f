from app.models.base import ActiveRecord
from peewee import (
    Date<PERSON><PERSON><PERSON><PERSON>,
    BigIntegerField,
    SQL,
    TextField,
    ForeignKeyField,
    SmallIntegerField,
)
from app.models.mixins.versioning_mixin import VersioningMixin
from app.models.concern.json_field import <PERSON><PERSON><PERSON><PERSON>
from app.models.concern.enum import <PERSON><PERSON><PERSON><PERSON>, InterviewFeedbackStatus


class CandidateInterviewFeedback(ActiveRecord, VersioningMixin):
    from app.models.employee import Employee
    from app.models.candidate import Candidate
    from app.models.schedule_interview import ScheduleInterview

    id = BigIntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=False)
    created_by_id = BigIntegerField(null=False)
    schedule_interview_id = BigIntegerField(null=False)

    interview_round = SmallIntegerField(null=False)
    rating = SmallIntegerField(null=False)
    feedback = TextField(null=True)

    approved_status = EnumField(InterviewFeedbackStatus)
    rating_fields = J<PERSON>NField()

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="candidate_interview_feedbacks", lazy_load=True
    )
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_interview_feedbacks", lazy_load=True
    )
    schedule_interview = ForeignKeyField(
        ScheduleInterview,
        null=False,
        backref="candidate_interview_feedbacks",
        lazy_load=True,
    )

    @property
    def opportunity(self):
        try:
            return self.schedule_interview and self.schedule_interview.opportunity
        except Exception:
            return None

    @property
    def rating_field_data(self):
        def reverse_format_field_name(name: str) -> str:
            return " ".join(word.capitalize() for word in name.split("_"))

        if self.rating_fields:
            return {
                reverse_format_field_name(key): value
                for key, value in self.rating_fields.items()
            }
        return {}

    def info(self):
        return {
            "id": self.id,
            "candidate_id": self.candidate_id,
            "candidate_name": self.candidate.name,
            "candidate_email": self.candidate.email,
            "interview_round": self.interview_round,
            "rating": self.rating,
            "feedback": self.feedback,
            "rating_fields": self.rating_fields or {},
            "approved_status": self.approved_status.name,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "job_id": (self.opportunity and self.opportunity.id),
            "job_title": (self.opportunity and self.opportunity.title or ""),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_interview_feedbacks"
