from database.connection import db
from peewee import Model, ForeignKeyField, fn, DoesNotExist
from app.models.concern.enum import EnumField, Enum
from datetime import datetime
from pytz import UTC
from typing import Union, Type, Optional
from app.config import S3Client, UPLOAD_TO_S3, API_URL


class ActiveRecord(Model):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._original_data = None
        self._existing_record = False

    def _load_data(self):
        """Load the current values of all fields into `_original_data`."""
        is_update = self._pk is not None
        if not is_update:
            return None

        if self._original_data is None:  # Only load data if not already loaded
            record = self.__class__.get(self._pk)
            if record:
                self._existing_record = True
                self._original_data = {
                    field.name: getattr(record, field.name)
                    for field in record._meta.fields.values()
                    if not isinstance(field, ForeignKeyField)
                }

    def __str__(self):
        attributes = ", ".join(
            f"{key}={getattr(self, key)!r}"
            for key, field in self._meta.fields.items()
            if not key.startswith("_") and not isinstance(field, ForeignKeyField)
        )
        return f"<{self.__class__.__name__}({attributes})>"

    def _has_changes(self) -> bool:
        """Compare current instance state with initial state."""
        if not super().is_dirty():
            return False

        try:
            self._load_data()
            dirty_fields = set()
            for field in self.dirty_fields:
                field_name = field.name
                # Check if the field is a foreign key
                if isinstance(field, ForeignKeyField):
                    # Compare by primary key values
                    field_name += "_id"

                original_value = self._original_data[field_name]
                current_value = getattr(self, field_name)

                if isinstance(field, EnumField):
                    # Compare by primary key values
                    if isinstance(original_value, Enum):
                        original_value = original_value.value
                    if isinstance(current_value, Enum):
                        current_value = current_value.value

                if original_value != current_value:
                    dirty_fields.add(field_name)

            self._dirty = dirty_fields
            return len(dirty_fields) > 0
        except Exception:
            return True

    @property
    def is_changed(self):
        """Return the default value for is_changed."""
        return getattr(self, "_is_changed", False)

    @is_changed.setter
    def is_changed(self, value):
        """Set a value for is_changed in memory."""
        self._is_changed = value

    class Meta:
        database = db
        abstract = True

    def reload(self):
        if self.id is None:
            raise ValueError("Instance does not have a primary key.")

        # Retrieve the latest data from the database
        refreshed_instance = type(self).get(type(self).id == self.id)

        # Update the current instance with the latest data
        for key, value in refreshed_instance.__data__.items():
            setattr(self, key, value)

        # Clear dirty fields
        self._dirty.clear()

    def is_dirty(self) -> bool:
        has_dirty = super().is_dirty()
        if not has_dirty:
            return False

        return self._has_changes()

    def save(self, *args, **kwargs):
        if hasattr(self, "validates"):
            self.validates()
        is_update = self._pk is not None
        self._is_changed = False
        if self.is_dirty():
            self.updated_at = datetime.now(UTC)
            for attr, value in kwargs.items():
                setattr(self, attr, value)
            res = super().save(*args, **kwargs)
            self.reload()

            # track version history
            if hasattr(self, "create_version_entry"):
                if is_update:
                    self.create_version_entry("update")
                else:
                    self.create_version_entry("create")
            self._is_changed = True
            return res
        else:
            for attr, value in kwargs.items():
                setattr(self, attr, value)
            res = super().save(*args, **kwargs)
            self.reload()
            return res

    def save_only(self, *args, **kwargs):
        for attr, value in kwargs.items():
            setattr(self, attr, value)
        res = super().save(*args, **kwargs)
        self.reload()
        return res

    def delete_instance(self, *args, **kwargs):
        if hasattr(self, "create_version_entry"):
            self.create_version_entry("delete")
        super().delete_instance(*args, **kwargs)

    def dup(self):
        # List of fields to exclude (primary key, created_at, updated_at)
        exclude_fields = [self._meta.primary_key.name, "created_at", "updated_at"]

        # Duplicate the object, excluding the specified fields
        dup_data = {
            field: getattr(self, field)
            for field in self._meta.fields
            if field not in exclude_fields
        }

        # Create a new instance with the duplicated data
        return self.__class__(**dup_data)

    @classmethod
    def count(cls):
        return cls.select().count()

    @classmethod
    def where(cls, conditions):
        query = cls.select()
        for key, condition in conditions.items():
            field = getattr(cls, key)
            if isinstance(condition, dict):
                for op, value in condition.items():
                    if op == "eq":
                        query = query.where(field == value)
                    elif op == "ne":
                        query = query.where(field != value)
                    elif op == "lt":
                        query = query.where(field < value)
                    elif op == "lte":
                        query = query.where(field <= value)
                    elif op == "gt":
                        query = query.where(field > value)
                    elif op == "gte":
                        query = query.where(field >= value)
                    elif op == "like":
                        query = query.where(field**value)
                    else:
                        query = query.where(getattr(field, op)(value))
            else:
                query = query.where(field == condition)
        return query

    @classmethod
    def find_by(cls, **kwargs):
        try:
            query = cls.select()
            for key, value in kwargs.items():
                field = getattr(cls, key)
                query = query.where(field == value)
            return (
                query.get()
            )  # Retrieves the first matching record or raises DoesNotExist
        except cls.DoesNotExist:
            return None  # Returns None if no matching record is found

    @classmethod
    def pluck(cls, *fields):
        result = cls.select(*[getattr(cls, field) for field in fields])
        if len(fields) == 1:
            # If there's only one field, return a flat list
            return [getattr(row, fields[0]) for row in result]
        else:
            # If there are multiple fields, return a list of lists
            return [[getattr(row, field) for field in fields] for row in result]

    @classmethod
    def pluck_from_query(cls, query, *fields):
        selected_fields = [getattr(cls, field) for field in fields]
        result = query.select(*selected_fields)

        if len(fields) == 1:
            # If there's only one field, return a flat list
            return [getattr(row, fields[0]) for row in result]
        else:
            # If there are multiple fields, return a list of lists
            return [[getattr(row, field) for field in fields] for row in result]

    @classmethod
    def first(cls):
        query = cls.select().order_by(cls._meta.primary_key.asc())
        try:
            return query.get()
        except cls.DoesNotExist:
            return None

    @classmethod
    def last(cls):
        query = cls.select().order_by(cls._meta.primary_key.desc())
        try:
            return query.get()
        except cls.DoesNotExist:
            return None

    def get_url(self, file_path: Union[str, None]) -> Union[str, None]:
        if isinstance(file_path, str):
            if UPLOAD_TO_S3:
                client = S3Client()
                return client.build_s3_presigned_url(
                    object_key=file_path, expiration=3600
                )
            else:
                return f"{API_URL}/{file_path}"
        return None

    def get_instance(self, name: str):
        try:
            return getattr(self, name)
        except DoesNotExist:
            return None

    @classmethod
    def all(cls):
        return list(cls.select())

    @classmethod
    def check_record_exist_with_lower(
        cls: Type[Model], lower_name_field, lower_name_value, **conditions
    ):
        """
        Check if a record with the given name and additional conditions exists in the specified model.

        Args:
            lower_name_field (peewee.Field): The field in the model representing the name.
            lower_name_value (str): The value to check for in the name field.
            **conditions: Additional conditions to check for (e.g., business_id).

        Raises:
            ValueError: If a record with the specified name and conditions already exists.
        """
        if cls.get_record_exist_with_lower(
            lower_name_field, lower_name_value, **conditions
        ):
            raise ValueError(
                f"{cls.__name__} already exists with this {lower_name_field.name}"
            )

    @classmethod
    def get_record_exist_with_lower(
        cls: Type[Model], lower_name_field, lower_name_value: str, **conditions
    ) -> Optional[Model]:
        """
        Check if a record with the given name and additional conditions exists in the specified model.

        Args:
            lower_name_field (peewee.Field): The field in the model representing the name.
            lower_name_value (str): The value to check for in the name field.
            **conditions: Additional conditions to check for (e.g., business_id).

        Returns:
            Model: The record if it exists, otherwise None.
        """
        query = cls.select().where(
            fn.LOWER(lower_name_field) == lower_name_value.lower()
        )
        for field, value in conditions.items():
            query = query.where(getattr(cls, field) == value)

        return query.first() if query.exists() else None

    def set_value(self, field, value):
        model_field = getattr(self.__class__, field)
        if isinstance(model_field, EnumField):
            setattr(self, field, model_field.python_value(value))
        else:
            setattr(self, field, value)
