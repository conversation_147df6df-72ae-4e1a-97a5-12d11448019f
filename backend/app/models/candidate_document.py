from app.models.base import ActiveRecord
from peewee import (
    CharField,
    ForeignKeyField,
    DateTimeField,
    BigIntegerField,
    SQL,
    SmallIntegerField,
)


class CandidateDocument(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate

    id = <PERSON>IntegerField(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=True)
    document = CharField()
    name = CharField()
    content_type = CharField()
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # association
    candidate = ForeignKeyField(
        Candidate, backref="candidate_documents", lazy_load=True
    )

    @property
    def document_url(self):
        return self.get_url(self.document)

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "candidate_id": self.candidate_id,
            "candidate_email": self.candidate.email,
            "candidate_name": self.candidate.name,
            "content_type": self.content_type,
            "document_url": self.document_url,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_documents"
