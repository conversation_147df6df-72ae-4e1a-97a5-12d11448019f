from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON><PERSON><PERSON>,
    DateTimeField,
    BigIntegerField,
    SQL,
    ForeignKeyField,
    TextField,
    SmallIntegerField,
)


class BusinessJobRequest(ActiveRecord):
    # to prevent circular import
    from app.models.business import Business
    from app.models.opportunity import Opportunity

    id = BigIntegerField(primary_key=True, index=True)
    name = CharField()
    email = CharField(null=False)
    resume = TextField(null=True)
    status = SmallIntegerField(constraints=[SQL("DEFAULT 1")])
    business_id = BigIntegerField(null=False)
    opportunity_id = BigIntegerField(null=False)

    # associations
    business = ForeignKeyField(
        Business, null=True, backref="candidates", lazy_load=True
    )
    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidates", lazy_load=True
    )

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    def info(self):
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "resume_url": self.resume_url or "",
            "status": self.status,
            "opportunity_id": self.opportunity_id,
            "opportunity_title": (
                (self.opportunity and self.opportunity.title) or "N/A"
            ),
            "has_running_interview": (self.has_running_interview),
            "has_job_interview": (self.has_job_interview),
            "candidate_exist": self.candidate_exist,
            "candidate_id": ((self.candidate and self.candidate.id) or None),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    @property
    def resume_url(self):
        return self.get_url(self.resume)

    @property
    def candidate_exist(self):
        try:
            from app.models.candidate import Candidate

            return (
                Candidate.get(
                    Candidate.email == self.email,
                    Candidate.business_id == self.business_id,
                    Candidate.is_deleted == False,
                )
                is not None
            )
        except Exception:
            return None

    @property
    def has_running_interview(self):
        try:
            return self.candidate.running_interviews.exists()
        except Exception:
            return False

    @property
    def has_job_interview(self):
        try:
            from app.models.candidate_interview import CandidateInterview

            return self.candidate.candidate_interviews.where(
                CandidateInterview.opportunity_id == self.opportunity_id
            ).exists()
        except Exception:
            return False

    @property
    def candidate(self):
        try:
            from app.models.candidate import Candidate

            return Candidate.get(
                Candidate.email == self.email,
                Candidate.business_id == self.business_id,
                Candidate.is_deleted == False,
            )
        except Exception:
            return None

    class Meta:
        table_name = "business_job_requests"
        indexes = (("business_id"), True)  # unique email with business
