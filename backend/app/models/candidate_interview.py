from app.models.base import ActiveRecord
from peewee import (
    <PERSON><PERSON>an<PERSON>ield,
    DateTimeField,
    BigIntegerField,
    SQL,
    BooleanField,
    ForeignKeyField,
)
from app.models.concern.enum import EnumField, CandidateInterviewStatus


class CandidateInterview(ActiveRecord):
    # to prevent circular import
    from app.models.employee import Employee
    from app.models.business import Business
    from app.models.opportunity import Opportunity
    from app.models.candidate import Candidate
    from app.models.location import Location

    id = BigIntegerField(primary_key=True, index=True)
    status = EnumField(
        CandidateInterviewStatus, default=CandidateInterviewStatus.Scheduled
    )
    offer_sent = BooleanField(default=False)
    candidate_id = BigIntegerField(null=True)
    business_id = BigIntegerField(null=True)
    created_by_id = BigIntegerField(null=True)
    opportunity_id = BigIntegerField(null=True)
    location_id = BigIntegerField(null=True)

    # associations
    created_by = ForeignKeyField(
        Employee, null=True, backref="candidate_interviews", lazy_load=True
    )
    business = ForeignKeyField(
        Business, null=True, backref="candidate_interviews", lazy_load=True
    )
    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidate_interviews", lazy_load=True
    )
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_interviews", lazy_load=True
    )
    location = ForeignKeyField(
        Location, null=False, backref="schedule_interviews", lazy_load=True
    )

    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    @property
    def status_name(self):
        return self.status and self.status.name.replace("_", " ")

    @property
    def status_id(self):
        return self.status.value

    def info(self):
        return {
            "id": self.id,
            "status_id": self.status_id,
            "status_name": self.status_name,
            "status": self.status,
            "offer_sent": self.offer_sent,
            "candidate_id": self.candidate_id,
            "candidate_name": self.candidate.name,
            "candidate_email": self.candidate.email,
            "candidate_designation": self.candidate.designation,
            "candidate_exist": self.candidate.is_deleted == False,
            "created_by_id": self.created_by_id,
            "created_by_name": (
                (self.get_instance("created_by") and self.created_by.full_name())
                or "Recruitease Pro - Employee (Deleted)"
            ),
            "opportunity_id": self.opportunity_id,
            "opportunity_title": (
                (self.opportunity and self.opportunity.title) or "N/A"
            ),
            "business_name": (
                (self.business and self.business.name) or "Recruitease Pro"
            ),
            "location_id": (self.location_id),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    def custom_info(self):
        info_data = self.info()
        from app.models import ScheduleInterview

        schedule_interviews = self.schedule_interviews.order_by(
            ScheduleInterview.id.desc()
        )
        schdule_interviews_data = [
            schdule_interview.info() for schdule_interview in schedule_interviews
        ]
        info_data["schedule_interviews"] = schdule_interviews_data
        return info_data

    class Meta:
        table_name = "candidate_interviews"
