from app.models.base import ActiveRecord
from peewee import (
    BigIntegerField,
    ForeignKeyField,
    FloatField,
)


class CandidateOpportunityScore(ActiveRecord):
    # to prevent circular import
    from app.models.candidate import Candidate
    from app.models.opportunity import Opportunity

    id = BigIntegerField(primary_key=True, index=True)

    # Score fields
    contact_info = FloatField(default=0.0)
    summary = FloatField(default=0.0)
    experience = FloatField(default=0.0)
    skills = FloatField(default=0.0)
    education = FloatField(default=0.0)
    certifications = FloatField(default=0.0)
    projects = FloatField(default=0.0)
    overall_impression = FloatField(default=0.0)
    total = FloatField(default=0.0)

    candidate_id = BigIntegerField(null=True)
    opportunity_id = BigIntegerField(null=True)

    # associations
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_opportunity_scores", lazy_load=True
    )

    opportunity = ForeignKeyField(
        Opportunity, null=True, backref="candidate_opportunity_scores", lazy_load=True
    )

    def info(self):
        return {
            "contact_info": self.contact_info,
            "summary": self.summary,
            "experience": self.experience,
            "skills": self.skills,
            "education": self.education,
            "certifications": self.certifications,
            "projects": self.projects,
            "overall_impression": self.overall_impression,
            "total": self.total,
        }

    class Meta:
        table_name = "candidate_opportunity_scores"
        indexes = (
            ("candidate_id", "opportunity_id"),
            True,
        )  # unique email with business
