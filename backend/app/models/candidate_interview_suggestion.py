from app.models.base import ActiveRecord
from peewee import (
    DateField,
    BigIntegerField,
    SQL,
    TextField,
    ForeignKeyField,
    SmallIntegerField,
)


class CandidateInterviewSuggestion(ActiveRecord):
    from app.models.candidate import Candidate
    from app.models.schedule_interview import ScheduleInterview

    id = <PERSON>Integer<PERSON>ield(primary_key=True, index=True)
    candidate_id = BigIntegerField(null=False)
    schedule_interview_id = BigIntegerField(null=False)

    rating = SmallIntegerField(null=False)
    suggestion = TextField(null=False)

    created_at = DateField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    # associations
    candidate = ForeignKeyField(
        Candidate, null=True, backref="candidate_interview_suggestions", lazy_load=True
    )
    schedule_interview = ForeignKeyField(
        ScheduleInterview,
        null=True,
        backref="candidate_interview_suggestions",
        lazy_load=True,
    )

    def info(self):
        return {
            "id": self.id,
            "candidate_id": self.candidate_id,
            "schedule_interview_id": self.schedule_interview_id,
            "rating": self.rating,
            "suggestion": self.suggestion,
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "candidate_interview_suggestions"
        indexes = (
            ("candidate_id", "schedule_interview_id"),
            True,
        )
