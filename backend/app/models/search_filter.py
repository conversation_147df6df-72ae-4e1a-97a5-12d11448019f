from peewee import (
    Date<PERSON><PERSON>Field,
    IntegerField,
    SQL,
    BigIntegerField,
    ForeignKeyField,
    DateField,
)
from app.models.base import ActiveRecord
from app.models.concern.enum import CandidateStatus, EnumField


class SearchFilter(ActiveRecord):
    # to prevent circular import
    from app.models import Business, Employee, Opportunity, Qualification

    id = IntegerField(primary_key=True, index=True)
    opportunity_id = BigIntegerField(null=True)
    opportunity = ForeignKeyField(Opportunity, backref="search_filters", lazy_load=True)
    qualification_id = BigIntegerField(null=True)
    qualification = ForeignKeyField(
        Qualification, backref="search_filters", lazy_load=True
    )
    experience = IntegerField(null=True)
    status = EnumField(CandidateStatus, default=None)
    resume_uploaded_after = DateField()
    created_by_id = BigIntegerField()
    created_by = ForeignKeyField(Employee, backref="search_filters", lazy_load=True)
    business_id = BigIntegerField()
    business = ForeignKeyField(Business, backref="search_filters", lazy_load=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    @property
    def status_name(self):
        return self.status and self.status.name.replace("_", " ")

    def info(self):
        return {
            "id": self.id,
            "opportunity_id": self.opportunity_id,
            "opportunity": (
                self.opportunity.title if self.opportunity_id is not None else None
            ),
            "qualification_id": self.qualification_id,
            "qualification": (
                self.qualification.name if self.qualification_id is not None else None
            ),
            "experience": self.experience if self.experience is not None else None,
            "status": self.status_name if self.status is not None else None,
            "created_by_id": self.created_by_id,
            "created_by": self.created_by.email if self.created_by else None,
            "business_id": self.business_id,
            "business": self.business.name if self.business else None,
            "resume_uploaded_after": str(self.resume_uploaded_after),
            "created_at": str(f"{self.created_at} utc"),
            "updated_at": str(f"{self.updated_at} utc"),
        }

    class Meta:
        table_name = "search_filters"
