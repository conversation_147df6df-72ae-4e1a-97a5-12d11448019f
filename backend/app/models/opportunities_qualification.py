from app.models.base import ActiveRecord
from peewee import CompositeKey, ForeignKeyField, BigIntegerField


class OpportunitiesQualification(ActiveRecord):
    # to prevent circular import
    from app.models.qualification import Qualification
    from app.models.opportunity import Opportunity

    qualification_id = BigIntegerField()
    opportunity_id = BigIntegerField()

    # association
    opportunity = ForeignKeyField(Opportunity, backref="qualifications")
    qualification = ForeignKeyField(Qualification, backref="opportunities")

    class Meta:
        table_name = "opportunities_qualifications"
        primary_key = CompositeKey("opportunity", "qualification")
