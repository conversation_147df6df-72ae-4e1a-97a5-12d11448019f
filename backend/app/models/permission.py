from app.models.base import ActiveRecord
from peewee import <PERSON><PERSON><PERSON><PERSON>, DateTimeField, IntegerField, SQL


class Permission(ActiveRecord):
    # to prevent circular import

    id = IntegerField(primary_key=True)
    name = CharField(max_length=255, unique=True)
    created_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])
    updated_at = DateTimeField(constraints=[SQL("DEFAULT CURRENT_TIMESTAMP")])

    def info(self):
        return {"id": self.id, "name": self.name}

    class Meta:
        table_name = "permissions"
