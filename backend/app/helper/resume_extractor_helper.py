import ast
from typing import Dict, Any, List, Union, Optional
from datetime import datetime
from dateutil.relativedelta import relativedelta
from app.validations import WebsiteURLValidate
import logging


class ResumeExtractorHelper:
    def __init__(self, json_data: Dict[str, Any]):
        """
        Initialize the ResumeExtractorHelper with JSON data.

        Args:
        - json_data (dict): JSON data containing resume information.
        """
        self.json_data = json_data

    def extract_detail(self) -> Dict[str, Any]:
        """
        Extracts and formats resume information from the stored JSON data.

        Returns:
        - transformed_data (dict): Formatted resume information.
        """
        transformed_data = self.extract_basic_detail()

        # Extract experiences (current and past)
        transformed_data["experiences"] = self.extract_experiences()

        # Extract candidate_skills (technical skills and soft skills)
        transformed_data["skills"] = self.extract_skills()

        # Extract candidate_education_info
        transformed_data["educations"] = self.extract_education_info()

        return transformed_data

    def extract_basic_detail(self) -> Dict[str, Any]:
        """
        Extracts and formats resume information from the stored JSON data.

        Returns:
        - transformed_data (dict): Formatted resume information.
        """
        transformed_data = {}

        # Extract candidate_personal_info
        personal_info = self.dict_object(
            ResumeExtractorHelper.__get_with_default(
                self.json_data, "candidate_personal_info", {}
            )
        )
        transformed_data["name"] = ResumeExtractorHelper.__get_with_default(
            personal_info, "name", ""
        )
        transformed_data["email"] = ResumeExtractorHelper.__get_with_default(
            personal_info, "email", ""
        )
        transformed_data["contact"] = ResumeExtractorHelper.__get_with_default(
            personal_info, "contact"
        ) or ResumeExtractorHelper.__get_with_default(personal_info, "phone")

        transformed_data["linkedin"] = self.validate_url(
            ResumeExtractorHelper.__get_with_default(personal_info, "linkedin", None),
            "Linkedin",
        )
        transformed_data["github"] = self.validate_url(
            ResumeExtractorHelper.__get_with_default(personal_info, "github", None),
            "Github",
        )
        transformed_data["website"] = self.validate_url(
            ResumeExtractorHelper.__get_with_default(personal_info, "website", None),
            "Website",
        )

        transformed_data["professional_title"] = (
            ResumeExtractorHelper.__get_with_default(
                self.json_data, "professional_title", None
            )
        )
        transformed_data["summary"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "summary", None
        )
        transformed_data["gender"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "gender", None
        )
        transformed_data["dob"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "dob", None
        )
        transformed_data["designation"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_designation", None
        )

        transformed_data["is_fresher"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "is_fresher", None
        )
        transformed_data["resume_source"] = ResumeExtractorHelper.__get_with_default(
            self.json_data, "resume_source", None
        )

        # first extract all experiences
        candidate_experiences_params = self.extract_experiences()

        # compute_experience = self.total_experience_years(candidate_experiences_params)
        # transformed_data["total_experience"] = compute_experience if compute_experience != 0 else self.json_data.get("total_experience")
        # transformed_data["total_experience"] = self.json_data.get("total_experience")
        transformed_data["total_experience"] = self.total_experience_years(
            candidate_experiences_params
        )
        transformed_data["total_gap"] = self.total_gap_years(
            candidate_experiences_params
        )

        # Extract candidate_achievements
        transformed_data["achievements"] = self.extract_achievements()

        return transformed_data

    def extract_candidate_qualification(self) -> List[str]:
        """
        Extracts the candidate's qualifications from the JSON data.

        This method retrieves the 'candidate_qualification' field from the
        provided JSON data. If the field is not present, it returns an empty list.

        Returns:
            List[str]: A list of candidate qualifications. If no qualifications
            are found, an empty list is returned.
        """
        qualifications = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_qualification", []
        )
        return self._filter_none_values(qualifications)

    def extract_experiences(self) -> List[dict]:
        """
        Extracts experiences (both current and past) from the stored JSON data.

        Returns:
        - experiences (list of dict): List of dictionaries containing formatted experience details.
        """
        experiences = []

        # Extract candidate_current_experience
        current_data = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_current_experience", {}
        )
        current_experiences = self.list_object(current_data)
        if current_experiences:
            current_experience = current_experiences[0]
        else:
            current_experience = None

        if current_experience:
            end_date = ResumeExtractorHelper.__get_with_default(
                current_experience, "end_date", None
            )
            end_date = (
                None
                if end_date and end_date.lower() == "present" or "till date"
                else end_date
            )
            responsibilities_data = ResumeExtractorHelper.__get_with_default(
                current_experience, "responsibilities", []
            )
            responsibilities = self._filter_none_values(responsibilities_data)
            experiences.append(
                {
                    "employer": ResumeExtractorHelper.__get_with_default(
                        current_experience, "employer", ""
                    ),
                    "position": ResumeExtractorHelper.__get_with_default(
                        current_experience, "role", ""
                    ),
                    "start_date": self._parse_date(
                        ResumeExtractorHelper.__get_with_default(
                            current_experience, "start_date"
                        )
                    ),
                    "end_date": self._parse_date(end_date),
                    "responsibilities": responsibilities,
                    "is_current_experience": True,
                }
            )

        # Extract candidate_past_experience
        past_data = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_past_experience", []
        )
        past_experiences = self.list_object(past_data)
        for past_experience in past_experiences:
            responsibilities_data = ResumeExtractorHelper.__get_with_default(
                past_experience, "responsibilities", []
            )
            responsibilities = self._filter_none_values(responsibilities_data)
            experiences.append(
                {
                    "employer": ResumeExtractorHelper.__get_with_default(
                        past_experience, "employer", ""
                    ),
                    "position": ResumeExtractorHelper.__get_with_default(
                        past_experience, "role", ""
                    ),
                    "start_date": self._parse_date(
                        ResumeExtractorHelper.__get_with_default(
                            past_experience, "start_date"
                        )
                    ),
                    "end_date": self._parse_date(end_date),
                    "responsibilities": responsibilities,
                    "is_current_experience": False,
                }
            )

        return experiences

    def extract_skills(self) -> List[Dict[str, Any]]:
        """
        Extracts skills (both technical and soft) from the stored JSON data.

        Returns:
        - skills (list of dict): List of dictionaries containing formatted skill details.
        """
        skills = []

        # Technical skills
        self.extract_skills_function(skills)

        # Soft skills
        soft_skills = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_soft_skills", []
        )
        soft_skills = self._filter_none_values(soft_skills)
        skills.extend(
            [
                {
                    "name": skill,
                    "skill_type": "Soft",
                    "sub_skill_type": "Soft Skill",
                }
                for skill in soft_skills
            ]
        )
        return skills

    def extract_skills_function(self, skills_final_list: list):
        """
        Extracts skills (both technical and soft) from the stored JSON data.

        Returns:
        - skills (list of dict): List of dictionaries containing formatted skill details.
        """
        skills_list = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_technical_skills", []
        )

        # Validate that the retrieved data is a list
        if not isinstance(skills_list, list):
            raise ValueError("The 'skills' key should contain a list.")

        # Iterate through each item in the skills list
        for skill_entry in skills_list:
            # Ensure each item is a list with exactly two elements
            if isinstance(skill_entry, list) and len(skill_entry) == 2:
                skill_name, skill_type = skill_entry
                if isinstance(skill_name, str) and isinstance(skill_type, str):
                    skills_final_list.extend(
                        [
                            {
                                "name": skill_name,
                                "skill_type": "Technical",
                                "sub_skill_type": skill_type.replace("_", " ").title(),
                            }
                        ]
                    )
                else:
                    raise ValueError(
                        "Each skill entry must contain two strings: skill name and skill type."
                    )
            else:
                raise ValueError(
                    "Each skill entry should be a list with exactly two elements."
                )

    def extract_achievements(self) -> List[Any]:
        """
        Extracts achievements from the stored JSON data.

        Returns:
        - achievements (list): List of achievements.
        """

        achievements = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_achievements", []
        )
        achievements = self._filter_none_values(achievements)
        return achievements

    def extract_education_info(self) -> List[Dict[str, Any]]:
        """
        Extracts education information from the stored JSON data.

        Returns:
        - education_info (list of dict): List of dictionaries containing formatted education details.
        """
        education_info = ResumeExtractorHelper.__get_with_default(
            self.json_data, "candidate_education_info", []
        )

        # Handle case where education info is provided as a dictionary instead of a list
        if isinstance(education_info, dict):
            education_info = [education_info]

        # Process each education entry
        formatted_education_info = []
        for education in education_info:
            formatted_education_info.append(
                {
                    "degree": ResumeExtractorHelper.__get_with_default(
                        education, "degree", ""
                    ),
                    "university": ResumeExtractorHelper.__get_with_default(
                        education, "university", ""
                    ),
                    "grade": ResumeExtractorHelper.__get_with_default(
                        education, "university"
                    ),
                    "start_date": self._parse_date(
                        ResumeExtractorHelper.__get_with_default(
                            education, "start_date", None
                        )
                    ),
                    "end_date": self._parse_date(
                        ResumeExtractorHelper.__get_with_default(
                            education, "end_date", None
                        )
                    ),
                }
            )

        return formatted_education_info

    # Extract months of experience
    def experience_months(self, start_date: str | None, end_date: str | None) -> int:
        """Returns the number of months of experience
        Args:
            start_date (str | None): Starting date in YYYY-MM-DD format
            end_date (str | None): Ending date in YYYY-MM-DD format
        Returns:
            int: Experience in months
        """
        total_months_exp = 0
        if start_date == None:
            return total_months_exp
        else:
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        if end_date == None:
            current_date = datetime.now()
            formatted_date = current_date.strftime("%Y-%m-%d")
            end_date = datetime.strptime(formatted_date, "%Y-%m-%d").date()
        else:
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

        date_diff = relativedelta(end_date, start_date)
        total_months_exp += date_diff.years * 12 + date_diff.months
        return total_months_exp

    # Count total years of experience
    def total_experience_years(self, candidate_experience_list: List[dict]) -> float:
        """Returns the total years of experience
        Args:
            candidate_experience_list (List[dict]): List of dictionaries containing candidate experience details
        Returns:
            float: Experience in years
        """
        total_experience_months = 0
        for record in candidate_experience_list:
            total_experience_months += self.experience_months(
                start_date=record["start_date"], end_date=record["end_date"]
            )

        total_experience_years = round(total_experience_months / 12, 1)
        return total_experience_years

    # Count total gap in experince in year
    def total_gap_years(self, candidate_experience_list: List[dict]) -> float:
        """Returns the total gap years in experience
        Args:
            candidate_experience_list (List[dict]): List of dictionaries containing candidate experience details
        Returns:
            float: Gap years in experience
        """
        try:
            # Sort experiences by start_date in ascending order
            total_gap_years = 0.0
            current_date = datetime.now()

            # Filter and parse dates safely
            parsed_experiences = []
            for exp in candidate_experience_list:
                start_str = exp.get("start_date")
                end_str = exp.get("end_date")

                if not start_str:
                    continue  # Skip if no start date

                try:
                    start = datetime.strptime(start_str, "%Y-%m-%d")
                except ValueError:
                    continue  # Skip invalid start date

                if not end_str or end_str.lower() in ["none", "present"]:
                    end = current_date
                else:
                    try:
                        end = datetime.strptime(end_str, "%Y-%m-%d")
                    except ValueError:
                        end = current_date  # Fallback

                parsed_experiences.append({"start": start, "end": end})

            # Not enough experience to compute any gaps
            if len(parsed_experiences) < 2:
                return 0.0

            # Sort by start date
            parsed_experiences.sort(key=lambda x: x["start"])

            # Calculate gaps between consecutive jobs
            for i in range(len(parsed_experiences) - 1):
                current_end = parsed_experiences[i]["end"]
                next_start = parsed_experiences[i + 1]["start"]

                if next_start > current_end:
                    gap_days = (next_start - current_end).days
                    total_gap_years += gap_days / 365.25

            # Optional: gap from last job to today if not current
            last_end = parsed_experiences[-1]["end"]
            if last_end < current_date:
                gap_days = (current_date - last_end).days
                total_gap_years += gap_days / 365.25

            return round(total_gap_years, 1)
        except ValueError as e:
            print(f"exception error : {str(e)}")
            return None
        except Exception as e:
            print(f"exception error : {str(e)}")
            return None

    # private method
    @staticmethod
    def evaluate_and_clean_string(item: str):
        if item == "None":
            return []  # Return empty list if the string is 'None'

        # Handle strings with commas as CSV-style lists
        if "," in item:
            return [i.strip() for i in item.split(",") if i.strip() != "None"]

        # Handle strings that resemble a list (e.g., ["'Leadership'", "'Problem Solving'"])
        if item.startswith("[") and item.endswith("]"):
            try:
                # Safely evaluate the string to get the list
                evaluated_list = ast.literal_eval(item)
                # Return the list after stripping any quotes/spaces and skipping 'None'
                return [i.strip("' ") for i in evaluated_list if i != "None"]
            except (ValueError, SyntaxError):
                # If parsing fails, return an empty list
                return []

        # If it's not a list-like string, return it as a list
        return [item]

    # private method
    @staticmethod
    def clean_string_only(item: list[str]):
        processed_list = []
        item = ", ".join(item)
        if "[" in item or "]" in item:
            cleaned_item = item.replace("[", "").replace("]", "")
            # Safely evaluate the cleaned item
            try:
                evaluated_list = ast.literal_eval(f"[{cleaned_item}]")
                # Add elements to the result list
                processed_list.extend(
                    i.strip("' ") for i in evaluated_list if i != "None"
                )
            except (ValueError, SyntaxError):
                # If parsing fails, skip this item
                return []
        else:
            # Handle regular strings that don't need parsing
            processed_list.append(item.strip())

        return processed_list

    @staticmethod
    def _filter_none_values(input_list: Optional[Union[List[Any], str]]) -> List[Any]:
        """
        Filters out 'None' values from a list.

        Args:
        - input_list (list): The list from which to filter out 'None' values.

        Returns:
        - filtered_list (list): A new list with 'None' values removed.
        """
        # For non-list and non-string input, return an empty list
        if not isinstance(input_list, (str, List)):
            return []

        if isinstance(input_list, list):
            cleaned_list = []
            new_items = ResumeExtractorHelper.clean_string_only(input_list)
            for item in new_items:
                if isinstance(item, str):
                    # Process each string and extend the cleaned list
                    cleaned_list.extend(
                        ResumeExtractorHelper.evaluate_and_clean_string(item)
                    )
                else:
                    cleaned_list.append(item)  # Append non-string items as-is
            return cleaned_list

        elif isinstance(input_list, str):
            # Directly handle string input including the 'None' case
            return ResumeExtractorHelper.evaluate_and_clean_string(input_list)

    @staticmethod
    def _parse_date(date: Union[str, None]) -> Union[str, None]:
        """
        Parses a date string into a specific format.

        Args:
        - date (str or None): The date string to parse. If None, returns None.

        Returns:
        - str or None: The parsed date string in 'YYYY-MM-DD' format if parsing succeeds,
        or None if the input date string is None or if parsing fails.
        """
        if not date:
            return None
        try:
            parsed_date = datetime.strptime(str(date), "%d/%m/%Y")
            return parsed_date.strftime(
                "%Y-%m-%d"
            )  # Return in 'YYYY-MM-DD' format if parsing is successful
        except ValueError:
            return None  # Return None if parsing fails

    @staticmethod
    def list_object(input_data):
        if not input_data:
            return []
        if isinstance(input_data, list):
            # Return parsed_data if it's a non-empty list
            return input_data
        elif isinstance(input_data, dict):
            # Return parsed_data if it's a non-empty list
            return [input_data]
        else:
            return []

    @staticmethod
    def dict_object(input_data):
        if not input_data:
            return dict({})
        if isinstance(input_data, dict):
            return input_data
        else:
            return dict({})

    @staticmethod
    def str_list_object(input_data):
        try:
            if isinstance(input_data, list):
                # Filter out non-string elements and return a list of strings
                return [str(item) for item in input_data if isinstance(item, str)]
            else:
                return []
        except Exception as e:
            print(f"Error processing input_data: {e}")
            return []

    @staticmethod
    def validate_url(value: Optional[str], type: str) -> Optional[str]:
        if not value:
            return None
        try:
            return WebsiteURLValidate(
                value=value,
                field="f{type} Link",
                allowEmpty=True,
                required=False,
                max_length=150,
                strip=True,
            )
        except Exception as e:
            logging.error(f"Error in  w {type} URL -  {e}")
            return ""

    @staticmethod
    def __get_with_default(d, key, default=None):
        val = d.get(key, default)
        if val is None:
            return default
        return val
