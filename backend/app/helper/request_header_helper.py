from fastapi import Request


class RequestHeaderHelper:
    @staticmethod
    def get_request_ip_header(request: Request):
        headers_dict = dict(request.headers)
        host = request.headers.get("host")
        # Extract origin (be cautious, might be spoofed)
        origin = request.headers.get("Origin")
        # Extract user agent
        user_agent = request.headers.get("User-Agent")
        client_ip = request.headers.get("X-Forwarded-For", None)
        if client_ip is None:
            client_ip = request.client.host

        return {
            "request_headers": headers_dict,
            "ip": client_ip,
            "host": host,
            "origin": origin,
            "user_agent": user_agent,
        }

    @staticmethod
    def get_request_ip_basic(request: Request):
        host = request.headers.get("host")
        # Extract origin (be cautious, might be spoofed)
        origin = request.headers.get("Origin")
        # Extract user agent
        user_agent = request.headers.get("User-Agent")
        client_ip = request.headers.get("X-Forwarded-For", None)
        if client_ip is None:
            client_ip = request.client.host

        return {
            "ip": client_ip,
            "host": host,
            "origin": origin,
            "user_agent": user_agent,
        }
