import json
from typing import Dict, Any, List, Union
from datetime import datetime


class ResumeBuilderHelper:
    def __init__(self, candidate_id: int):
        """
        Initialize the ResumeExtractorHelper with JSON data.

        Args:
        - json_data (dict): JSON data containing resume information.
        """
        from app.models.candidate import Candidate

        candidate = Candidate.get(id=candidate_id)
        self.candidate = candidate
        self.candidate_id = candidate_id

    def build_detail(self) -> Dict[str, Any]:
        """
        Extracts and formats resume information from the stored JSON data.

        Returns:
        - transformed_data (dict): Formatted resume information.
        """
        transformed_data = {}

        transformed_data["candidate_personal_info"] = self.extract_basic_detail()
        transformed_data["total_experience"] = self.candidate.total_experience or 0
        transformed_data["candidate_achievements"] = self.candidate.achievements_list

        transformed_data["processed_degrees"] = self.extract_candidate_qualification()
        transformed_data["candidate_skills"] = self.extract_skills()

        if self.candidate and self.candidate.summary:
            transformed_data["summary"] = self.candidate.summary

        experiences = self.extract_experiences()
        transformed_data["candidate_current_experience"] = experiences[
            "candidate_current_experience"
        ]
        transformed_data["candidate_past_experience"] = experiences[
            "candidate_past_experience"
        ]
        return transformed_data

    def extract_basic_detail(self) -> Dict[str, Any]:
        """
        Extracts and formats resume information from the stored JSON data.

        Returns:
        - transformed_data (dict): Formatted resume information.
        """
        transformed_data = {}

        # Extract candidate_personal_info
        transformed_data["name"] = self.candidate.name
        transformed_data["email"] = self.candidate.email
        transformed_data["phone"] = self.candidate.contact
        transformed_data["linkedin"] = self.candidate.linkedin
        transformed_data["github"] = self.candidate.github
        transformed_data["website"] = self.candidate.website

        return transformed_data

    def extract_candidate_qualification(self) -> List[str]:
        from app.models.qualification import Qualification
        from app.models.candidate import Candidate
        from app.models.candidates_qualification import CandidatesQualification

        query = (
            Qualification.select(Qualification.name)
            .join(CandidatesQualification)
            .join(Candidate)
            .where(CandidatesQualification.candidate == self.candidate_id)
        )

        qualification_names = [qualification.name for qualification in query]
        return qualification_names

    def extract_experiences(self) -> List[Dict[str, Any]]:
        """
        Extracts experiences (both current and past) from the stored JSON data.

        Returns:
        - experiences (list of dict): List of dictionaries containing formatted experience details.
        """
        experiences = {}

        # "candidate_current_experience"
        from app.models.candidate_experience import CandidateExperience

        current_experience = CandidateExperience.get_or_none(
            CandidateExperience.is_current_experience == True,
            CandidateExperience.candidate_id == self.candidate_id,
        )
        past_experiences = CandidateExperience.select().where(
            CandidateExperience.is_current_experience == False,
            CandidateExperience.candidate_id == self.candidate_id,
        )

        if current_experience:
            candidate_current_experience = {
                "employer": current_experience.employer,
                "position": current_experience.position,
                "responsibilities": current_experience.responsibilities_list,
            }
        else:
            candidate_current_experience = None

        candidate_past_experience = []

        for past_experience in past_experiences:
            candidate_past_experience.append(
                {
                    "employer": past_experience.employer,
                    "position": past_experience.position,
                    "responsibilities": past_experience.responsibilities_list,
                }
            )

        experiences["candidate_current_experience"] = candidate_current_experience
        experiences["candidate_past_experience"] = candidate_past_experience

        return experiences

    def extract_skills(self) -> List[Dict[str, Any]]:
        """
        Extracts skills (both technical and soft) from the stored JSON data.

        Returns:
        - skills (list of dict): List of dictionaries containing formatted skill details.
        """
        from app.models.candidate_skill import CandidateSkill

        technical_skills = CandidateSkill.select().where(
            CandidateSkill.candidate_id == self.candidate_id,
            CandidateSkill.skill_type == "Technical",
        )

        skills = [skill.name for skill in technical_skills]

        return skills

    def extract_education_info(self) -> List[Dict[str, Any]]:
        """
        Extracts education information from the stored JSON data.

        Returns:
        - education_info (list of dict): List of dictionaries containing formatted education details.
        """
        education_array = []

        from app.models.candidate_education_information import (
            CandidateEducationInformation,
        )

        education_infos = CandidateEducationInformation.select().where(
            CandidateEducationInformation.candidate_id == self.candidate_id
        )

        # Process each education entry
        formatted_education_info = []
        for education in education_infos:
            formatted_education_info.append(
                {
                    "degree": education.degree,
                    "university": education.university,
                    "grade": education.grade,
                }
            )

        return education_array
