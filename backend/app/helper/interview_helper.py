import datetime


class InterviewHelper:

    @staticmethod
    def schedule_interview_email(schedule_interview: any):
        from app.tasks.candidate_task import CandidateTask
        from app.tasks.employee_task import EmployeeTask

        candidate = schedule_interview.candidate
        interviewer = schedule_interview.interviewer

        # Send initial emails
        CandidateTask.interview_scheduled_mail.apply_async(
            kwargs={
                "candidate_id": candidate.id,
                "schedule_interview_id": schedule_interview.id,
            }
        )

        EmployeeTask.interview_scheduled_mail.apply_async(
            kwargs={
                "employee_id": interviewer.id,
                "schedule_interview_id": schedule_interview.id,
            }
        )

        # Schedule 1 day and 30 minutes reminders
        InterviewHelper.__schedule_email(
            schedule_interview=schedule_interview,
            threshold=schedule_interview.interview_at - datetime.timedelta(days=1),
        )

        InterviewHelper.__schedule_email(
            schedule_interview=schedule_interview,
            threshold=schedule_interview.interview_at - datetime.timedelta(minutes=30),
        )

    @staticmethod
    def __schedule_email(schedule_interview: any, threshold: any):
        from app.tasks.candidate_task import CandidateTask
        from app.tasks.employee_task import EmployeeTask

        current_time = datetime.datetime.utcnow()
        if threshold > current_time:
            candidate_id = schedule_interview.candidate.id
            interviewer_id = schedule_interview.interviewer.id

            # Schedule and store task ids
            candidate_task = CandidateTask.interview_reminder_mail.apply_async(
                kwargs={
                    "candidate_id": candidate_id,
                    "schedule_interview_id": schedule_interview.id,
                },
                eta=threshold,
            )

            interviewer_task = EmployeeTask.interview_reminder_mail.apply_async(
                kwargs={
                    "employee_id": interviewer_id,
                    "schedule_interview_id": schedule_interview.id,
                },
                eta=threshold,
            )

            return [candidate_task, interviewer_task]

        return []
