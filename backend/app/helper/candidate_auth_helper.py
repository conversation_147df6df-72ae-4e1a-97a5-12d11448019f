from fastapi import Request, Body, Response
from app.exceptions import UnauthorizedException
from app.helper.concern.auth_helper_concern import AuthHelperConcern
from app.context import set_whodoneit, set_request_headers
from app.constants import ConstantMessages
from typing import Optional


class CandidateA<PERSON><PERSON><PERSON>per(AuthHelperConcern):
    role = "candidate"
    from app.models.candidate import Candidate
    from app.models.business import Business

    @staticmethod
    async def validate_subdomain(
        request: Request, subdomain: Optional[str] = None
    ) -> Business:
        try:
            if hasattr(request.state, "current_business"):
                return request.state.current_business
            if not request.state.subdomain or request.state.subdomain == "api":
                request.state.subdomain = subdomain
                current_subdomain = subdomain
            else:
                current_subdomain = request.state.subdomain
            business = CandidateAuthHelper.get_business(current_subdomain)
            request.state.current_business = business
            return business
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error="Unauthorized.")

    @staticmethod
    async def authenticate_candidate(
        business_id: int, email: str, password: str
    ) -> Candidate:
        from app.models.candidate import Candidate
        from app.models.candidate_auth import CandidateAuth

        candidate = Candidate.get_or_none(business_id=business_id, email=email.lower())
        if not candidate:
            return None
        auth_user = CandidateAuth.authenticate(
            candidate_id=candidate.id, password=password
        )
        if not auth_user:
            return None
        return candidate

    @staticmethod
    async def get_auth_candidate(business_id: int, email: str) -> Candidate:
        from app.models.candidate import Candidate
        from app.models.candidate_auth import CandidateAuth

        candidate = Candidate.get_or_none(business_id=business_id, email=email.lower())
        if not candidate:
            return None
        auth_user = CandidateAuth.get_or_none(candidate_id=candidate.id)
        if not auth_user:
            return None
        return candidate

    @staticmethod
    async def get_email_otp_candidate(
        request: Request, body: dict = Body(...)
    ) -> Candidate:
        subdomain = request and request.state and request.state.subdomain
        if subdomain is None:
            raise UnauthorizedException(error=ConstantMessages.CANDIDATE_DOES_NOT_EXIST)
        business = CandidateAuthHelper.get_business(subdomain)
        email = CandidateAuthHelper.get_otp_email(request=request, body=body)
        from app.models.candidate import Candidate

        user = Candidate.get_or_none(email=email.lower(), business_id=business.id)
        if user is None:
            raise UnauthorizedException(error=ConstantMessages.CANDIDATE_DOES_NOT_EXIST)
        return user

    @staticmethod
    async def get_current_business(subdomain: str, request: Request) -> Business:
        return CandidateAuthHelper.validate_subdomain(subdomain, request)

    @staticmethod
    async def get_current_candidate(request: Request, response: Response) -> Candidate:
        try:
            if hasattr(request.state, "current_candidate"):
                return request.state.current_candidate
            auth_token = await CandidateAuthHelper.get_current_auth_token(
                request=request,
                response=response,
                api_key_token=request.headers.get("X-API-KEY"),
            )
            if not auth_token or auth_token.object_type != "Candidate":
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            if auth_token.is_expired():
                raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
            from app.models import Candidate

            current_business = request.state.current_business
            current_candidate = Candidate.get_or_none(
                id=auth_token.object_id, business_id=current_business.id
            )
            if not current_candidate:
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            request.state.current_candidate = current_candidate

            # save in thread request headers and current logged in user
            from app.helper.request_header_helper import RequestHeaderHelper

            request_data = RequestHeaderHelper.get_request_ip_basic(request)
            set_whodoneit(current_candidate)
            set_request_headers(request_data)

            return current_candidate
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
