from extraction_modules.process_job import JobProcessor
from extraction_modules.score_matcher import ScoreMatcher


class JobHelper:
    @staticmethod
    async def extract_questions(
        opportunity_id: int,
        business_id: int,
        employee_id: int,
        request_data: dict | None = None,
    ):
        from app.helper.job_detail_extractor_helper import JobDetailExtractorHelper
        from app.helper.screening_question_helper import ScreeningQuestionHelper
        from app.models.api_key_usage import ApiKeyUsage
        from app.models.screening_interview_question import ScreeningInterviewQuestion
        from app.models.opportunity import Opportunity

        if request_data is None:
            request_data = {}
        # get question for that job.
        try:
            opportunity = Opportunity.get(
                Opportunity.id == opportunity_id,
                Opportunity.business_id == business_id,
            )
            if not opportunity:
                raise ValueError("Opportunity not found")
            default_usage_dict: dict = dict(
                {
                    "business_id": business_id,
                    "created_by_id": employee_id,
                    "response_status": "in-progress",
                    **request_data,
                }
            )
            db_model = ApiKeyUsage(**default_usage_dict)
            cv_processor = JobProcessor(db_model=db_model)
            extracted_detail = opportunity.extract_detail()
            response = await cv_processor.process_and_standardize_job(extracted_detail)
            job_detail = JobDetailExtractorHelper(response)
            job_info = job_detail.extract_all()
            opportunity.save_response(job_info)

            questions = await cv_processor.process_and_standardize_questions(
                extracted_detail
            )
            question_helper = ScreeningQuestionHelper(questions)
            questions_array = question_helper.build_questions(
                business_id=business_id, opportunity_id=opportunity.id
            )
            # mark deleted prev question based on job description
            ScreeningInterviewQuestion.update(is_deleted=True).where(
                (ScreeningInterviewQuestion.business_id == business_id)
                & (ScreeningInterviewQuestion.opportunity_id == opportunity.id)
            ).execute()
            # save questions in db
            ScreeningInterviewQuestion.insert_many(questions_array).execute()
            JobHelper.extract_opportunity_score_for_candidates(
                opportunity_id=opportunity.id,
                business_id=business_id,
            )
        except Exception as e:
            print(e, "exception")

    @staticmethod
    def extract_opportunity_score_for_candidates(
        opportunity_id: int,
        business_id: int,
    ):
        """
        Extracts opportunity score with candidates.
        :param opportunity_id: Opportunity ID
        :param business_id: Business ID
        """
        try:
            from app.helper.resume_builder_helper import ResumeBuilderHelper
            from app.models.opportunity import Opportunity
            from app.models.candidate import Candidate

            opportunity = Opportunity.get(
                Opportunity.id == opportunity_id,
                Opportunity.business_id == business_id,
            )
            if not opportunity:
                raise ValueError("Opportunity not found")
            score_matcher = ScoreMatcher()

            candidates = Candidate.select().where(Candidate.business_id == business_id)
            extract_ai_response = opportunity.extract_ai_response
            if not extract_ai_response:
                raise ValueError("Opportunity AI response not found")

            score_dicts = []
            for candidate in candidates:
                resume_obj = ResumeBuilderHelper(candidate.id)
                resume = resume_obj.build_detail()
                score = score_matcher.calculate_score(
                    resume, opportunity.extract_ai_response
                )

                score_dict = {
                    "opportunity_id": opportunity_id,
                    "candidate_id": candidate.id,
                    "contact_info": score["contact_info"],
                    "summary": score["summary"],
                    "experience": score["experience"],
                    "skills": score["skills"],
                    "education": score["education"],
                    "certifications": score["certifications"],
                    "projects": score["projects"],
                    "overall_impression": score["Overall Impression"],
                    "total": score["total"],
                }

                score_dicts.append(score_dict)

                print(
                    f"Candidate {candidate.id} score for opportunity {opportunity_id}: {score_dict}"
                )

            if score_dicts:
                from app.models.candidate_opportunity_score import (
                    CandidateOpportunityScore,
                )

                CandidateOpportunityScore.insert_many(score_dicts).on_conflict(
                    update={
                        "contact_info": CandidateOpportunityScore.contact_info,
                        "summary": CandidateOpportunityScore.summary,
                        "experience": CandidateOpportunityScore.experience,
                        "skills": CandidateOpportunityScore.skills,
                        "education": CandidateOpportunityScore.education,
                        "certifications": CandidateOpportunityScore.certifications,
                        "projects": CandidateOpportunityScore.projects,
                        "overall_impression": CandidateOpportunityScore.overall_impression,
                        "total": CandidateOpportunityScore.total,
                    }
                ).execute()
        except Exception as e:
            print(e, "exception")
            return None

    @staticmethod
    def extract_candidate_scores(
        candidate_id: int,
    ):
        """
        Extracts opportunity score with candidates.
        :param candidate_id: Opportunity ID
        """
        try:
            from app.helper.resume_builder_helper import ResumeBuilderHelper
            from app.models.opportunity import Opportunity
            from app.models.candidate import Candidate

            score_matcher = ScoreMatcher()

            candidate = Candidate.get_or_none(Candidate.id == candidate_id)

            resume_obj = ResumeBuilderHelper(candidate.id)
            resume = resume_obj.build_detail()

            business_id = candidate.business_id
            opportunities = Opportunity.select().where(
                Opportunity.business_id == business_id
            )

            score_dicts = []
            for opportunity in opportunities:
                if opportunity.extract_ai_response:
                    score = score_matcher.calculate_score(
                        resume, opportunity.extract_ai_response
                    )

                    score_dict = {
                        "opportunity_id": opportunity.id,
                        "candidate_id": candidate.id,
                        "contact_info": score["contact_info"],
                        "summary": score["summary"],
                        "experience": score["experience"],
                        "skills": score["skills"],
                        "education": score["education"],
                        "certifications": score["certifications"],
                        "projects": score["projects"],
                        "overall_impression": score["Overall Impression"],
                        "total": score["total"],
                    }

                    score_dicts.append(score_dict)

                    print(
                        f"Candidate {candidate.id} score for opportunity {opportunity.id}: {score_dict}"
                    )

            if score_dicts:
                from app.models.candidate_opportunity_score import (
                    CandidateOpportunityScore,
                )

                CandidateOpportunityScore.insert_many(score_dicts).on_conflict(
                    update={
                        "contact_info": CandidateOpportunityScore.contact_info,
                        "summary": CandidateOpportunityScore.summary,
                        "experience": CandidateOpportunityScore.experience,
                        "skills": CandidateOpportunityScore.skills,
                        "education": CandidateOpportunityScore.education,
                        "certifications": CandidateOpportunityScore.certifications,
                        "projects": CandidateOpportunityScore.projects,
                        "overall_impression": CandidateOpportunityScore.overall_impression,
                        "total": CandidateOpportunityScore.total,
                    }
                ).execute()
        except Exception as e:
            print(e, "exception")
            return None
