from datetime import datetime, timedelta
import calendar


class FilterHelper:
    @staticmethod
    def get_date_range(filter_value: str):
        now = datetime.now()
        today = datetime(now.year, now.month, now.day)
        if filter_value == "today":
            start = today
            end = start + timedelta(days=1)
        elif filter_value == "tomorrow":
            start = today + timedelta(days=1)
            end = start + timedelta(days=1)
        elif filter_value == "this_week":
            start = today - timedelta(days=today.weekday())  # Monday of this week
            end = start + timedelta(days=7)
        elif filter_value == "next_week":
            start = today - timedelta(days=today.weekday()) + timedelta(days=7)
            end = start + timedelta(days=7)
        elif filter_value == "this_month":
            start = today.replace(day=1)
            _, last_day = calendar.monthrange(today.year, today.month)
            end = start.replace(day=last_day) + timedelta(days=1)
        elif filter_value == "next_month":
            year = today.year + (1 if today.month == 12 else 0)
            month = 1 if today.month == 12 else today.month + 1
            start = datetime(year, month, 1)
            _, last_day = calendar.monthrange(year, month)
            end = start.replace(day=last_day) + timedelta(days=1)
        elif filter_value == "past":
            start = None
            end = today
        elif filter_value == "upcoming":
            start = today
            end = None
        else:
            start = None
            end = None
        return start, end
