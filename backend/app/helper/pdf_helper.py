from starlette.datastructures import UploadFile, Headers as UploadFileHeaders
import pdfkit
import io


class PdfHelper:
    @staticmethod
    def generate_pdf_uploadfile(
        context: str, filename: str = "sample.pdf"
    ) -> UploadFile:
        # Render to bytes
        pdf_bytes = pdfkit.from_string(context, False)

        # Wrap in BytesIO
        pdf_buffer = io.BytesIO(pdf_bytes)

        headers = UploadFileHeaders(
            {
                "content-type": "application/pdf",
                "content-disposition": 'form-data; name="file"; filename="example.pdf"',
            }
        )

        return UploadFile(filename=filename, file=pdf_buffer, headers=headers)
