from fastapi import Request, Body, Response
from app.exceptions import UnauthorizedException
from app.helper.concern.auth_helper_concern import AuthHelperConcern
from app.context import set_whodoneit, set_request_headers
from app.constants import ConstantMessages


class AdminAuthHelper(AuthHelperConcern):
    role = "admin"
    from app.models.user import User

    @staticmethod
    async def authenticate_user(email: str, password: str) -> User:
        from app.models.user import User

        user = User.authenticate(email=email.lower(), password=password)
        return user

    @staticmethod
    async def get_email_otp_admin(request: Request, body: dict = Body(...)) -> User:
        email = AdminAuthHelper.get_otp_email(request=request, body=body)
        from app.models.user import User

        user = User.get_or_none(email=email.lower(), status=1)
        if user is None:
            raise UnauthorizedException(error=ConstantMessages.USER_DOES_NOT_EXIST)
        return user

    @staticmethod
    async def get_admin_user(request: Request, response: Response) -> User:
        try:
            if hasattr(request.state, "current_user"):
                return request.state.current_user
            auth_token = await AdminAuthHelper.get_current_auth_token(
                request=request,
                response=response,
                api_key_token=request.headers.get("X-API-KEY"),
            )
            if not auth_token or auth_token.object_type != "User":
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            if auth_token.is_expired():
                raise UnauthorizedException(error=ConstantMessages.SESSION_EXPIRED)
            from app.models import User

            user = User.get_or_none(id=auth_token.object_id, status=1)
            if not user:
                raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
            request.state.current_user = user

            # save in thread request headers and current logged in user
            from app.helper.request_header_helper import RequestHeaderHelper

            request_data = RequestHeaderHelper.get_request_ip_basic(request)
            set_whodoneit(user)
            set_request_headers(request_data)

            return user
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error=ConstantMessages.AUTH_FAILED)
