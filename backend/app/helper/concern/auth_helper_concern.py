from fastapi import Request, Response, Body, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>eader
from app.config import PYTHON_ENV, BASE_DOMAIN
from app.exceptions import UnauthorizedException
from typing import Optional

api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)


class AuthHelperConcern:
    role = "admin"

    @staticmethod
    def get_business(current_subdomain):
        from app.models.business import Business

        business = Business.get_or_none(subdomain=current_subdomain, status=1)
        if not business:
            raise UnauthorizedException(error="Business does not exist or is inactive.")
        if business.payment_status == 0:
            raise UnauthorizedException(
                error="Please make a payment before performing any actions."
            )
        if not business.email_verified:
            raise UnauthorizedException(error="Please Verify your business first.")
        return business

    @classmethod
    async def set_otp_cookie(cls, request: Request, response: Response, email: str):
        subdomain = request.state.subdomain
        response.set_cookie(
            key=f"_{subdomain or 'auth'}_{cls.role}_otp_email",
            value=email,
            domain=f".{BASE_DOMAIN}",
            max_age=60 * 15,  # Set expiry time to 15 minutes in seconds
            expires=False,
            secure=PYTHON_ENV == "production",
            httponly=True,
        )

    # private helper method
    @classmethod
    def get_otp_email(cls, request: Request, body: dict = Body(...)):
        subdomain = request.state.subdomain
        email = body.get("email") or request.cookies.get(
            f"_{subdomain or 'auth'}_{cls.role}_otp_email"
        )
        if email is None:
            raise UnauthorizedException(error="OTP has expired")
        return email

    @classmethod
    async def clear_otp_cookie(cls, request: Request, response: Response):
        subdomain = request.state.subdomain
        response.delete_cookie(
            f"_{subdomain or 'auth'}_{cls.role}_otp_email", domain=f".{BASE_DOMAIN}"
        )

    @classmethod
    async def set_auth_token_cookie(
        cls,
        request: Request,
        response: Response,
        access_token: str,
        custom_subdomain: Optional[str] = None,
    ) -> str:
        token_key = (
            f"_{custom_subdomain or request.state.subdomain or 'auth'}_{cls.role}_token"
        )
        response.set_cookie(
            key=token_key,
            value=access_token,
            domain=f".{BASE_DOMAIN}",
            max_age=60 * 60 * 1,  # Set expiry time to 1 hours in seconds
            expires=True,
            secure=PYTHON_ENV == "production",
            samesite="lax",
            httponly=True,
        )
        return access_token

    @classmethod
    async def get_cookie_value(
        cls, request: Request, custom_subdomain: Optional[str] = None
    ) -> Optional[str]:
        token = request.cookies.get(
            f"_{(custom_subdomain or request.state.subdomain) or 'auth'}_{cls.role}_token"
        )
        return token

    @classmethod
    async def clear_auth_cookie(cls, request: Request, response: Response) -> str:
        subdomain = request.state.subdomain
        response.delete_cookie(
            f"_{subdomain or 'auth'}_{cls.role}_token", domain=f".{BASE_DOMAIN}"
        )

    @classmethod
    async def get_current_auth_token(
        cls,
        request: Request,
        response: Response,
        api_key_token: Optional[str] = Depends(api_key_header),
    ):
        try:
            subdomain = request.state.subdomain
            if hasattr(request.state, "auth_token"):
                await cls.set_auth_token_cookie(
                    request=request,
                    response=response,
                    access_token=request.state.auth_token.id,
                )
                return request.state.auth_token
            token = api_key_token or request.cookies.get(
                f"_{subdomain or 'auth'}_{cls.role}_token"
            )
            if not token:
                raise UnauthorizedException(error="Authorization Failed.")
            from app.models import AuthToken

            auth_token = AuthToken.get(id=token)
            if auth_token.is_expired():
                raise UnauthorizedException(error="Session Expired.")
            auth_token.update_expiration()
            await cls.set_auth_token_cookie(
                request=request, response=response, access_token=auth_token.id
            )
            request.state.auth_token = auth_token
            return auth_token
        except UnauthorizedException as e:
            raise e
        except Exception:
            raise UnauthorizedException(error="Authorization Failed.")

    @classmethod
    async def get_current_auth_token_only(
        cls,
        request: Request,
        api_key_token: Optional[str] = Depends(api_key_header),
    ):
        try:
            subdomain = request.state.subdomain
            if hasattr(request.state, "auth_token"):
                return request.state.auth_token
            token = api_key_token or request.cookies.get(
                f"_{subdomain or 'auth'}_{cls.role}_token"
            )
            if not token:
                raise UnauthorizedException(error="Authorization Failed.")
            from app.models import AuthToken

            auth_token = AuthToken.get(AuthToken.id == token)
            if auth_token.is_expired():
                raise UnauthorizedException(error="Session Expired.")
            request.state.auth_token = auth_token
            return auth_token
        except UnauthorizedException as e:
            raise e
        except Exception as e:
            raise UnauthorizedException(error="Authorization Failed.")
