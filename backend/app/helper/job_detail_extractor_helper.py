from typing import Dict, Any, List, Union


class JobDetailExtractorHelper:
    def __init__(self, json_data: Dict[str, Any]):
        """
        Initialize the JobDetailExtractorHelper with JSON data.

        Args:
        - json_data (dict): JSON data containing job information.
        """
        if not isinstance(json_data, dict):
            raise ValueError("Expected json_data to be a dictionary.")
        self.json_data = json_data

    def _filter_none_values(
        self, data: Union[List[Any], Dict[str, Any]]
    ) -> Union[List[Any], Dict[str, Any]]:
        if isinstance(data, list):
            return [item for item in data if item not in (None, "")]
        elif isinstance(data, dict):
            return {k: v for k, v in data.items() if v not in (None, "")}
        return data

    def _get_dict(self, key: str) -> Dict[str, Any]:
        value = self.json_data.get(key, {})
        if not isinstance(value, dict):
            raise ValueError(f"Expected {key} to be a dictionary.")
        return value

    def _parse_list(self, value: str) -> List[str]:
        # Splitting string into list by commas and trimming extra spaces
        if not value or value.lower() == "none":
            return []
        return [item.strip() for item in value.split(",")]

    def _parse_string(self, value: Any) -> str:
        """
        Cleans up and formats the string value.

        Args:
            value (Any): The value to be parsed, expected to be a string.

        Returns:
            str: The cleaned and formatted string if `value` is a string; otherwise, an empty string.
        """
        if isinstance(value, str):
            # Clean up string values
            return value.strip() if value.strip().lower() != "none" else ""
        return ""

    def extract_job_requirements(self) -> Dict[str, Any]:
        """
        Extracts and returns job requirements from the JSON data.
        """
        # requirements = self.json_data.get('job_requirements', '')
        return {
            "skills": self._parse_list(self.json_data.get("skills", "")),
            "qualifications": self._parse_list(
                self.json_data.get("qualifications", "")
            ),
            # 'experience': self._parse_list(requirements)  # Assuming 'experience' might be in a more complex format
        }

    def extract_job_responsibilities(self) -> Dict[str, Any]:
        """
        Extracts and returns job responsibilities from the JSON data.
        """
        responsibilities = self.json_data.get("job_responsibilities", "")
        return self._parse_list(responsibilities)

    def extract_job_benefits(self) -> Dict[str, Any]:
        """
        Extracts and returns job benefits from the JSON data.
        """
        benefits = self.json_data.get("job_benefits", "")
        return self._parse_list(benefits)

    def extract_skills(self) -> List[str]:
        """
        Extracts and returns a list of technical skills from the JSON data.
        """
        skills = self.json_data.get("skills", "")
        return self._parse_list(skills)

    def extract_qualifications(self) -> List[str]:
        """
        Extracts and returns a list of qualifications from the JSON data.
        """
        qualifications = self.json_data.get("qualifications", "")
        return self._parse_list(qualifications)

    def extract_other_details(self) -> Dict[str, Any]:
        """
        Dynamically extracts other details from the JSON data, assuming they are strings.
        """
        other_details = {
            k: self._parse_string(v)
            for k, v in self.json_data.items()
            if k
            not in {
                "job_requirements",
                "job_responsibilities",
                "job_benefits",
                "skills",
                "qualifications",
            }
        }
        return other_details

    def extract_all(self) -> Dict[str, Any]:
        """
        Extracts all information from the JSON data and returns it as a dictionary.
        """
        return {
            "job_requirements": self.extract_job_requirements(),
            "job_responsibilities": self.extract_job_responsibilities(),
            "job_benefits": self.extract_job_benefits(),
            "skills": self.extract_skills(),
            "qualifications": self.extract_qualifications(),
            "other_details": self.extract_other_details(),
        }
