from typing import List, Any
import json


class ScreeningQuestionHelper:
    def __init__(self, questions: List[Any]):
        """
        Initialize the ScreeningQuestionHelper with a list of ScreeningQuestionType objects.

        Args:
        - questions (List[ScreeningQuestionType]): List of question objects.
        """
        if not isinstance(questions, list):
            raise ValueError(
                "Expected questions to be a list of ScreeningQuestionType objects."
            )
        self.questions = questions

    # def build_questions(self, business_id: int, opportunity_id: int):
    #     """
    #     Build questions by adding business and opportunity IDs and setting the question type.

    #     Args:
    #     - business_id (int): Business identifier.
    #     - opportunity_id (int): Opportunity identifier.

    #     Returns:
    #     - List[dict]: A list of questions with additional metadata.
    #     """
    #     new_questions = []

    #     for question in self.questions:
    #         # Check if options are present to determine the question type

    #         # Append the new question with additional fields
    #         try:
    #             has_options = "options" in question.keys()
    #             question_type = 0 if has_options else 1
    #             new_questions.append(
    #                 {
    #                     "question": question["question"],
    #                     "answer": question["answer"],
    #                     "question_type": question_type,
    #                     "options": (
    #                         json.dumps(question["options"]) if has_options else "[]"
    #                     ),
    #                     "business_id": business_id,
    #                     "opportunity_id": opportunity_id,
    #                 }
    #             )
    #         except Exception as e:
    #             print(e, "e")

    #     return new_questions
    def build_questions(self, business_id: int, opportunity_id: int):
        """
        Build questions by adding business and opportunity IDs and setting the question type.

        Args:
        - business_id (int): Business identifier.
        - opportunity_id (int): Opportunity identifier.

        Returns:
        - List[dict]: A list of questions with additional metadata.
        """
        new_questions = []

        for question in self.questions:
            try:
                # Determine question type
                if "language" in question:
                    question_type = 2  # Coding
                elif "options" in question:
                    question_type = 0  # Objective
                else:
                    question_type = 1  # Subjective

                # Handle options field (default to empty list if not present)
                options = json.dumps(question.get("options", []))
                test_cases = json.dumps(question.get("test_cases", {}))

                # Build the question dict
                new_question = {
                    "question": question["question"],
                    "answer": question.get("answer", ""),
                    "starter_code": question.get("starter_code", ""),
                    "language": question.get("language", ""),
                    "test_cases": test_cases,
                    "question_type": question_type,
                    "options": options,
                    "business_id": business_id,
                    "opportunity_id": opportunity_id,
                }

                # Optionally include language if it's a coding question
                if question_type == 2:
                    new_question["language"] = question["language"]

                new_questions.append(new_question)

            except Exception as e:
                print(e, "e")

        return new_questions
