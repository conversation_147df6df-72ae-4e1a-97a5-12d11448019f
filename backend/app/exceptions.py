from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint
from starlette.types import ASGIApp


class RecordNotFoundException(Exception):
    """Exception raised when a record is not found."""

    def __init__(self, message="Record not found"):
        self.message = message
        super().__init__(self.message)


class CustomValidationError(Exception):
    """Exception raised for custom validation errors."""

    def __init__(self, error="Record not found", status_code=404):
        self.error = error
        self.status_code = status_code
        super().__init__(self.error)


class UnauthorizedException(Exception):
    """Exception raised for unauthorized access attempts."""

    def __init__(self, error="Unauthorized"):
        self.error = error
        super().__init__(self.error)


class MethodNotAllowedException(Exception):
    """Exception raised for method not allowed access attempts."""

    def __init__(self, error="Method Not Allowed"):
        self.error = error
        super().__init__(self.error)
