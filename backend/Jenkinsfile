pipeline {
    agent any

    environment {
        DOCKER_CREDENTIALS_ID = 'tlgt-git' // <PERSON> credentials ID
        IMAGE_NAME = 'ghcr.io/talentelgia-technologies-pvt-ltd/ats-backend'
        K8S_NAMESPACE = 'ats' // Namespace in Kubernetes
        SONARQUBE_SERVER = 'sonarqube-server'
        SONARSCANNER = 'sonarqube-scanner'
    }

    stages {
        stage('Checkout Code') {
            steps {
                // Checkout the code from your Git repository
                git url: 'https://github.com/Talentelgia-Technologies-Pvt-Ltd/ATS.git', credentialsId: 'tlgt-git', branch: 'master'
            }
        }

        stage('SonarQube Analysis') {
            environment {
                scannerHome = tool name: "${SONARSCANNER}"
            }
            steps {
                script {
                    def executable = "${scannerHome}/bin/sonar-scanner"
                    
                    withCredentials([string(credentialsId: 'jenkins-sonar-token', variable: 'SONAR_TOKEN')]) {
                        withSonarQubeEnv("${SONARQUBE_SERVER}") {
                            sh """
                                #!/bin/bash
                                ${executable} \\
                                -Dsonar.projectKey=ats-backend \\
                                -Dsonar.projectName=ATS_Backend \\
                                -Dsonar.projectVersion=1.0 \\
                                -Dsonar.sources=backend \\
                                -Dsonar.exclusions='**/node_modules/**/*,**/__pycache__/**,**/*.pyc' \\
                                -Dsonar.sourceEncoding=UTF-8 \\
                                -Dsonar.host.url=https://sonar.teamtalentelgia.com/ \\
                                -Dsonar.login=${SONAR_TOKEN} \\
                                -Dsonar.language=py \\
                                -Dsonar.python.coverage.reportPaths=backend/coverage.xml \\
                                -Dsonar.exclusions=**/*.js,**/*.ts \\
                                -Dsonar.ce.javaOpts="-Dhttp.maxContentLength=10000000"
                            """
                        }
                    }
                }
            }
        }

        stage('Quality Gate') {
            steps {
                script {
                    timeout(time: 1, unit: 'HOURS') {
                        def qualityGate = waitForQualityGate()
                        if (qualityGate.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qualityGate.status}"
                        }
                    }
                }
            }
        }

        stage('Build Image') {
            steps {
                script {
                    // Build Docker image from the backend folder
                    dockerImage = docker.build IMAGE_NAME, 'backend'
                }
            }
        }

        stage('Push Image') {
            steps {
                script {
                    // Push the Docker image with the build number tag
                    docker.withRegistry('https://ghcr.io', DOCKER_CREDENTIALS_ID) {
                        dockerImage.push("${env.BUILD_NUMBER}")
                    }
                }
            }
        }

        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'ATS-Manifest-Backend', parameters: [string(name: 'DOCKERTAG', value: env.BUILD_NUMBER)]
            }
        }
    }

    // Optionally, you can add post actions here if needed
}
