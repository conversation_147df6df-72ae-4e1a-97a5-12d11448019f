"""
Peewee migrations -- 002_create_users.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE users (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            first_name VARCHAR(100) DEFAULT NULL,
            last_name VARCHAR(100) DEFAULT NULL,
            email VARCHAR(255) NOT NULL,
            password VARCHAR(255) DEFAULT NULL,
            gender VARCHAR(255) DEFAULT NULL,
            dob DATE DEFAULT NULL,
            user_type_id INT,
            email_verified BOOLEAN DEFAULT false,
            otp VARCHAR(6) DEFAULT NULL,
            otp_expire_at DATETIME NULL DEFAULT NULL,
            status TINYINT DEFAULT '1',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_type_id) REFERENCES user_types(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql("CREATE UNIQUE INDEX idx_users_email ON users (email);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_users_email ON users;")

    # drop table
    migrator.sql("DROP TABLE `users`")
