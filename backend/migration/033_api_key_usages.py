"""
Peewee migrations -- 033_api_key_usages.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    # Create Table
    sql_query = """
    CREATE TABLE api_key_usages (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        api_key VARCHAR(255) NOT NULL,
        business_id BIGINT NOT NULL,
        created_by_id BIGINT,
        ip VARCHAR(255),
        host VARCHA<PERSON>(255),
        origin VARCHAR(255),
        user_agent VARCHAR(255),
        response_status VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (business_id) REFERENCES businesses(id),
        FOREIGN KEY (created_by_id) REFERENCES employees(id)
    );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # Drop Table
    migrator.sql("DROP TABLE `api_key_usages`;")
