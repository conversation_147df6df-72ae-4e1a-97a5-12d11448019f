"""
Peewee migrations -- 059_add_timezone_in_businesses.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE businesses ADD COLUMN timezone VARCHAR(100) DEFAULT 'UTC';
    """
    # create table candidate_interview_suggestion
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    rollback_query = """
        ALTER TABLE businesses
        DROP COLUMN timezone;
    """

    # drop columns
    migrator.sql(rollback_query)
