"""Peewee migrations -- 022_create_schedule_interviews.py."""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE schedule_interviews (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            interview_at DATETIME NOT NULL,
            meeting_link VARCHAR(150) NOT NULL,
            comment TEXT,
            business_id BIGINT,
            interviewer_id BIGINT,
            opportunity_id BIGINT,
            candidate_id BIGINT,
            created_by_id BIGINT,
            interview_round TINYINT DEFAULT 1,
            status TINYINT DEFAULT 0, # means interview schedule
            feedback TEXT,
            rating INT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OREIG<PERSON> KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (candidate_id) REFERENCES candidates(id),
            FOREIGN KEY (interviewer_id) REFERENCES employees(id),
            FOREIGN KEY (opportunity_id) REFERENCES opportunities(id),
            FOREIGN KEY (created_by_id) REFERENCES employees(id)
        );
    """

    # create table schedule_interview
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_schedule_interviews_id ON schedule_interviews (id);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_schedule_interviews_id ON schedule_interviews;")

    # drop table
    migrator.sql("DROP TABLE schedule_interviews;")
