"""
Peewee migrations -- 037_create_candidate_interviews.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_interviews (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            created_by_id BIGINT,
            business_id BIGINT NOT NULL,
            opportunity_id BIGINT NOT NULL,
            candidate_id BIGINT NOT NULL,
            status TINYINT DEFAULT '1',
            offer_sent BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREI<PERSON>N KEY (business_id) REFERENCES businesses(id),
            FOREI<PERSON><PERSON> KEY (opportunity_id) REFERENCES opportunities(id),
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (candidate_id) REFERENCES candidates(id),
            FOREI<PERSON><PERSON> KEY (created_by_id) REFERENCES employees(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `candidate_interviews`")
