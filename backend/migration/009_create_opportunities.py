"""
Peewee migrations -- 009_create_opportunities.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        CREATE TABLE opportunities (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            title varchar(255) NOT NULL,
            designation varchar(100) NOT NULL,
            location TEXT,
            number_of_vacancies int NOT NULL,
            description text NOT NULL,
            salary BIGINT NOT NULL,
            experience int NOT NULL,
            questions text,
            status TINYINT DEFAULT 1,
            department_id BIGINT NOT NULL,
            contact_person_id BIGINT NOT NULL,
            business_id BIGINT,
            created_by_id BIGINT,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES employees(id),
            FOREIGN KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (department_id) REFERENCES departments(id),
            FOREIGN KEY (contact_person_id) REFERENCES employees(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql("CREATE INDEX idx_opportunities_id ON opportunities (id);")
    migrator.sql(
        "CREATE UNIQUE INDEX idx_opportunities_title_business_id ON opportunities (title, business_id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_opportunities_id ON opportunities;")
    migrator.sql("DROP INDEX idx_opportunities_title_business_id ON opportunities;")

    # drop table
    migrator.sql("DROP TABLE `opportunities`;")
