"""
Peewee migrations -- 057_candidate_opportunity_scores.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_opportunity_scores (
            id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            candidate_id BIGINT NOT NULL,
            opportunity_id BIGINT NOT NULL,
            contact_info FLOAT DEFAULT 0.0,
            summary FLOAT DEFAULT 0.0,
            experience FLOAT DEFAULT 0.0,
            skills FLOAT DEFAULT 0.0,
            education FLOAT DEFAULT 0.0,
            certifications FLOAT DEFAULT 0.0,
            projects FLOAT DEFAULT 0.0,
            overall_impression FLOAT DEFAULT 0.0,
            total FLOAT DEFAULT 0.0,
            UNIQUE (candidate_id, opportunity_id)
        );
    """

    # alter table query
    migrator.sql(sql_query)

    sql_query = """
    CREATE INDEX idx_candidate_opportunity ON candidate_opportunity_scores (candidate_id, opportunity_id);
    """


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop table
    migrator.sql("DROP TABLE IF EXISTS candidate_opportunity_scores;")
