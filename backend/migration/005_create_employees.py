"""
Peewee migrations -- 005_create_employees.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE employees (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            first_name VA<PERSON><PERSON><PERSON>(100),
            last_name <PERSON><PERSON><PERSON><PERSON>(100),
            email VARCHAR(255) NOT NULL,
            password VARCHAR(255),
            status TINYINT DEFAULT 1,
            employee_role_id INT,
            business_id BIGINT,
            created_by_id BIGINT,
            contact_number VARCHAR(100),
            otp VARCHAR(6) DEFAULT NULL,
            otp_expire_at DATETIME NULL DEFAULT NULL,
            is_deleted B<PERSON><PERSON>EAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (employee_role_id) REFERENCES employee_roles(id),
            FOREIGN KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (created_by_id) REFERENCES employees(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql(
        "CREATE UNIQUE INDEX idx_employees_email_business_id ON employees (email, business_id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_employees_email_business_id ON employees;")

    # drop table
    migrator.sql("DROP TABLE `employees`")
