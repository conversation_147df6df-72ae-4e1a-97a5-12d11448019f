"""
Peewee migrations -- 043_screening_interview_questions.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        CREATE TABLE screening_interview_questions(
            id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            question_type TINYINT NOT NULL,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            options TEXT NULL,
            opportunity_id BIGINT,
            business_id BIGINT,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (opportunity_id) REFERENCES opportunities(id),
            FOREIGN KEY (business_id) REFERENCES businesses(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `screening_interview_questions`;")
