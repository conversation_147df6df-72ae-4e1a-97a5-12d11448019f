"""
Peewee migrations -- 010_create_opportunities_qualifications.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE opportunities_qualifications (
            opportunity_id BIGINT,
            qualification_id BIGINT,
            PRIMARY KEY (opportunity_id, qualification_id),
            FOREIGN KEY (opportunity_id) REFERENCES opportunities(id),
            FOREIGN KEY (qualification_id) REFERENCES qualifications(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql(
        "CREATE INDEX idx_opportunity_qualification_id ON opportunities_qualifications (opportunity_id, qualification_id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql(
        "DROP INDEX idx_opportunity_qualification_id ON opportunities_qualifications;"
    )

    # drop table
    migrator.sql("DROP TABLE `opportunities_qualifications`;")
