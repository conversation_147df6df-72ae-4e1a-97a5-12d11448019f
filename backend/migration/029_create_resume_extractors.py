"""
Peewee migrations -- 029_create_resume_extractors.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE resume_extractors (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL,
            resume TEXT,
            data TEXT,
            business_id BIGINT,
            created_by_id BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (created_by_id) REFERENCES employees(id),
            FOREIGN KEY (business_id) REFERENCES businesses(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE resume_extractors;")
