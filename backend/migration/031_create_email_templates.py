"""
Peewee migrations -- 031_create_email_templates.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    # migrator.sql("DROP TABLE `email_templates`;")
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE email_templates (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            template_name VARCHAR(255) NOT NULL, 
            business_id BIGINT,
            email_body TEXT NOT NULL, 
            is_default TINYINT DEFAULT 0,
            status TINYINT DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (business_id) REFERENCES businesses(id),
            UNIQUE (template_name, business_id)
        );
    """
    migrator.sql(sql_query)

    # Creating the unique index separately
    migrator.sql(
        "CREATE UNIQUE INDEX idx_template_name_business_id ON email_templates (template_name, business_id);"
    )

    # insert queries
    template_name = "Default Job Template"
    email_body = """<body style="margin: 0; padding: 0; font-family: \'Fira Sans\', sans-serif; font-weight: 400; color: #4a5b67; line-height: 1.5; font-size: 16px;"><center><table style="max-width: 600px; width: 100%; border: none;"><tbody><tr><td style="height: 30px;"></td></tr></tbody></table><table style="max-width: 600px; width: 100%; border: none;"><tbody><tr><td><table style="width: 100%; background-color: #ebf7ff; max-width: 600px;"><tbody><tr><td style="height: 30px;"></td></tr><tr><td align="center"><a href="#" style="display: inline-block;"><img src="https://api.recruiteasepro.com/images/logo.png" alt="Logo" style="max-width: 150px;" /></a></td></tr><tr><td style="height: 30px;"></td></tr><tr><td style="background-color: #ffffff; padding: 20px;"><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td style="width: 25px;"></td><td><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td style="height: 30px;"></td></tr><tr><td>Dear <span style="color: #000;">%candidate_name%</span>,</td></tr><tr><td style="height: 20px;"></td></tr><tr><td><p><strong>Job Title:</strong> %job_title%</p><p><strong>Job Description:</strong> %job_description%</p><p><strong>Roles and Responsibilities:</strong> %job_responsibilities%</p><p><strong>Salary:</strong> %job_salary%</p><p><strong>Experience Range:</strong> %job_experience% years</p><p><strong>Questions:</strong></p><ol><li>%job_questions%</li></ol></td></tr><tr><td style="height: 20px;"></td></tr><tr><td>Best regards,<br />The Recruitease Pro Team</td></tr></tbody></table></td></tr></tbody></table></td><td style="width: 25px;"></td></tr><tr><td style="height: 20px;"></td></tr></tbody></table></td></tr><tr><td style="background-color: #ffffff;"></td></tr><tr><td align="center" style="background-color: #ebf7ff;"><table style="width: 100%;"><tbody><tr><td style="height: 20px;"></td></tr><tr><td align="center" style="color: #5c5c5c; font-size: 12px;">© Copyright | All Rights Reserved by <a href="#" style="color: #0d6b40; text-decoration: none;">recruiteasepro.com</a></td></tr><tr><td style="height: 20px;"></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></center></body>"""
    is_default = 1
    status = 1

    # Use parameterized queries to safely insert data
    sql_query = """
        INSERT INTO email_templates (
            business_id,
            template_name,
            email_body,
            is_default,
            status
        ) VALUES (
            %s, 
            %s, 
            %s, 
            %s, 
            %s
        )
    """

    params = (None, template_name, email_body, is_default, status)

    # Execute the SQL query with parameters
    migrator.sql(sql_query, params)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # Drop index
    migrator.sql("DROP INDEX idx_template_name_business_id ON email_templates;")

    # Drop Table
    migrator.sql("DROP TABLE `email_templates`;")
