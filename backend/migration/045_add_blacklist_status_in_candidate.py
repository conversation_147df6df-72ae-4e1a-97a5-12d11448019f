"""
Peewee migrations -- 045_add_blacklist_status_in_candidate.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE candidates 
        ADD COLUMN blacklist TINYINT DEFAULT 0,
        ADD COLUMN blacklist_reason TEXT;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE candidates 
        DROP COLUMN blacklist,
        DROP COLUMN blacklist_reason;
    """
    # alter table query
    migrator.sql(sql_query)
