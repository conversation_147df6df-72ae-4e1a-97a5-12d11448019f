"""
Peewee migrations -- 008_create_qualifications.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        CREATE TABLE qualifications (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description varchar(255) DEFAULT NULL,
            status TINYINT DEFAULT 1,
            business_id BIGINT,
            created_by_id BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES employees(id),
            <PERSON>OR<PERSON><PERSON><PERSON> (business_id) REFERENCES businesses(id),
            UNIQUE (name, business_id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql("CREATE INDEX idx_qualifications_id ON qualifications (id);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_qualifications_id ON qualifications;")

    # drop table
    migrator.sql("DROP TABLE `qualifications`;")
