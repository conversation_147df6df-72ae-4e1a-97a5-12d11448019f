"""
Peewee migrations -- 023_create_versions.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE versions (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            whodoneit VARCHAR(255),
            record TEXT,
            model_name VARCHAR(255),
            model_id VARCHAR(255),
            action VARCHAR(255),
            request_headers TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    """

    # create table schedule_interview
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE versions;")
