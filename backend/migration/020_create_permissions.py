"""
Peewee migrations -- 020_create_permissions.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
    CREATE TABLE permissions (
        id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    """

    # create table candidate_skills
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_permissions_name ON permissions (name);")

    # insert queries
    sql_query = """
        INSERT INTO permissions (name) VALUES
            ('read'),
            ('write'),
            ('edit'),
            ('delete');
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_permissions_name ON permissions;")

    # drop table
    migrator.sql("DROP TABLE `permissions`")
