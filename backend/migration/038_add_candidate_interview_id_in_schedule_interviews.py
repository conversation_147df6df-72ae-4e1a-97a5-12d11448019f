"""
Peewee migrations -- 038_add_candidate_interview_id_in_schedule_interviews.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE schedule_interviews 
        ADD COLUMN candidate_interview_id BIGINT,
        ADD CONSTRAINT fk_candidate_interview_id FOREIGN KEY (candidate_interview_id) REFERENCES candidate_interviews(id);
    """

    # alter table query
    migrator.sql(sql_query)

    # to TRUNCATE table clear old records
    migrator.sql("SET FOREIGN_KEY_CHECKS = 0;")
    migrator.sql("TRUNCATE table candidate_interview_feedbacks;")
    migrator.sql("TRUNCATE table schedule_interviews;")
    migrator.sql("SET FOREIGN_KEY_CHECKS = 1;")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE schedule_interviews 
        DROP FOREIGN KEY fk_candidate_interview_id,
        DROP COLUMN candidate_interview_id;
    """
    # alter table query
    migrator.sql(sql_query)

    # to TRUNCATE table clear old records
    migrator.sql("SET FOREIGN_KEY_CHECKS = 0;")
    migrator.sql("TRUNCATE table candidate_interview_feedbacks;")
    migrator.sql("TRUNCATE table schedule_interviews;")
    migrator.sql("SET FOREIGN_KEY_CHECKS = 1;")
