"""
Peewee migrations -- 017_create_document_upload_link_requests.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE document_upload_link_requests (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT NOT NULL,
            employee_id BIGINT NOT NULL,
            unique_key VARCHAR(255) UNIQUE NOT NULL,
            expired BOOLEAN DEFAULT FALSE,
            submitted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (candidate_id) REFERENCES candidates(id),
            FOREI<PERSON><PERSON> KEY (employee_id) REFERENCES employees(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql(
        "CREATE INDEX idx_document_upload_link_requests_unique_key ON document_upload_link_requests (unique_key);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql(
        "DROP INDEX idx_document_upload_link_requests_unique_key ON document_upload_link_requests;"
    )

    # drop table
    migrator.sql("DROP TABLE `document_upload_link_requests`")
