"""
Peewee migrations -- 006_create_auth_tokens.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE auth_tokens (
            id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
            object_id BIGINT NOT NULL,
            object_type VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME
        );
    """
    # create table
    migrator.sql(sql_query)

    delimiter_query = """
        CREATE TRIGGER set_expires_at_before_insert_auth_tokens
        BEFORE INSERT ON auth_tokens
        FOR EACH ROW
        BEGIN
            IF NEW.expires_at IS NULL THEN
                SET NEW.expires_at = DATE_ADD(CURRENT_TIMESTAMP, INTERVAL 60 MINUTE);
            END IF;
        END;
    """
    # create delimiter
    migrator.sql(delimiter_query)

    # create index
    migrator.sql(
        "CREATE INDEX idx_auth_tokens_object_id_object_type ON auth_tokens (object_id, object_type);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop delimiter
    delimiter_query = "DROP TRIGGER IF EXISTS set_expires_at_before_insert_auth_tokens;"
    migrator.sql(delimiter_query)
    # drop indexes
    migrator.sql("DROP INDEX idx_auth_tokens_object_id_object_type ON auth_tokens;")

    # drop table
    migrator.sql("DROP TABLE `auth_tokens`")
