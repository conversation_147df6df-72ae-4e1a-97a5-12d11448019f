"""
Peewee migrations -- 039_add_template_type_field_in_email_templates.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        ALTER TABLE email_templates ADD COLUMN template_type TINYINT DEFAULT 0;
    """

    # alter table add template type
    migrator.sql(sql_query)

    # insert queries
    template_name = "Default Offer Template"
    email_body = """<body style="margin: 0; padding: 0; font-family: 'Fira Sans', sans-serif; font-weight: 400; color: #4a5b67; line-height: 1.5; font-size: 16px;"><center><table style="max-width: 600px; width: 100%; border: none;"><tbody><tr><td style="height: 30px;"></td></tr></tbody></table><table style="max-width: 600px; width: 100%; border: none;"><tbody><tr><td><table style="width: 100%; background-color: #ebf7ff; max-width: 600px;"><tbody><tr><td style="height: 30px;"></td></tr><tr><td align="center"><a href="#" style="display: inline-block;"><img src="https://api.recruiteasepro.com/images/logo.png" alt="Logo" style="max-width: 150px;" /></a></td></tr><tr><td style="height: 30px;"></td></tr><tr><td style="background-color: #ffffff; padding: 20px;"><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td style="width: 25px;"></td><td><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td><table style="width: 100%;" cellspacing="0" cellpadding="0"><tbody><tr><td style="height: 30px;"></td></tr><tr><td>Dear <span style="color: #000;">%candidate_name%</span>,</td></tr><tr><td style="height: 20px;"></td></tr><tr><td><p>I hope this message finds you well. I am reaching out to share an exciting opportunity to join our dynamic team at <strong>%business_name%</strong> as a <strong>%job_title%</strong>. Given your impressive background and innovative mindset, we believe you would be a fantastic fit for our mission-driven culture.</p><p>Please confirm your interest, and we can arrange a time to discuss this opportunity further and explore how your skills can help drive our vision forward.</p><p><strong>Job Title:</strong> %job_title%</p><p><strong>Job Description:</strong> %job_description%</p><p><strong>Roles and Responsibilities:</strong> %job_responsibilities%</p><p><strong>Salary Range:</strong> %job_salary%</p></td></tr><tr><td style="height: 20px;"></td></tr><tr><td>Best regards,<br />The Recruitease Pro Team</td></tr></tbody></table></td></tr></tbody></table></td><td style="width: 25px;"></td></tr><tr><td style="height: 20px;"></td></tr></tbody></table></td></tr><tr><td style="background-color: #ffffff;"></td></tr><tr><td align="center" style="background-color: #ebf7ff;"><table style="width: 100%;"><tbody><tr><td style="height: 20px;"></td></tr><tr><td align="center" style="color: #5c5c5c; font-size: 12px;">© Copyright | All Rights Reserved by <a href="#" style="color: #0d6b40; text-decoration: none;">recruiteasepro.com</a></td></tr><tr><td style="height: 20px;"></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></center></body>"""
    is_default = 1
    status = 1
    template_type = 1

    # Use parameterized queries to safely insert data
    sql_query = """
        INSERT INTO email_templates (
            business_id,
            template_name,
            email_body,
            is_default,
            status,
            template_type
        ) VALUES (
            %s, 
            %s, 
            %s, 
            %s, 
            %s,
            %s
        )
    """

    params = (None, template_name, email_body, is_default, status, template_type)

    # Execute the SQL query with parameters
    migrator.sql(sql_query, params)

    sql_query = """
    UPDATE email_templates
    SET status = 1
    WHERE template_name = 'Default Job Template';
    """

    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE email_templates DROP COLUMN template_type;
    """

    # alter table remove sub skill type
    migrator.sql(sql_query)
