"""
Peewee migrations -- 055_add_test_cases_and_language_in_screening_questions.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE screening_interview_questions
        ADD COLUMN starter_code TEXT,
        ADD COLUMN language TEXT,
        ADD COLUMN test_cases TEXT;
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):

    # drop table
    migrator.sql(
        """
            ALTER TABLE screening_interview_questions
            DROP COLUMN starter_code,
            DROP COLUMN language,
            DROP COLUMN test_cases;
        """
    )
