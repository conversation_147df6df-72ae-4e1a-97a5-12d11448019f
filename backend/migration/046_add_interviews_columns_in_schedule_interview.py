"""
Peewee migrations -- 046_add_interviews_columns_in_schedule_interview.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE schedule_interviews 
        ADD COLUMN phone_number VARCHAR(255) NULL,
        ADD COLUMN passing_percentage INT NULL,
        ADD COLUMN time_duration INT NULL,
        ADD COLUMN show_marks BOOLEAN DEFAULT FALSE;
    """

    # alter table query
    migrator.sql(sql_query)

    sql_query = """
        ALTER TABLE candidate_interviews 
        ADD COLUMN location_id INT NULL;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE schedule_interviews 
        DROP COLUMN phone_number,
        DROP COLUMN passing_percentage,
        DROP COLUMN time_duration,
        DROP COLUMN show_marks;
    """
    # alter table query
    migrator.sql(sql_query)

    sql_query = """
        ALTER TABLE candidate_interviews 
        DROP COLUMN location_id;
    """
    # alter table query
    migrator.sql(sql_query)
