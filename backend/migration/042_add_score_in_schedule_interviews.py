"""
Peewee migrations -- 042_add_score_in_schedule_interviews.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE schedule_interviews 
        ADD COLUMN score FLOAT;
    """

    # alter table query
    migrator.sql(sql_query)

    sql_query = """
        ALTER TABLE schedule_interviews 
        ADD COLUMN screening_video TEXT;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE schedule_interviews 
        DROP COLUMN score;
    """
    # alter table query
    migrator.sql(sql_query)

    sql_query = """
        ALTER TABLE schedule_interviews 
        DROP COLUMN screening_video;
    """
    # alter table query
    migrator.sql(sql_query)
