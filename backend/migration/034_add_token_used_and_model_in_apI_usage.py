"""
Peewee migrations -- 034_add_token_used_and_model_in_apI_usage.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        ALTER TABLE api_key_usages 
        ADD COLUMN model_name VARCHAR(255) DEFAULT 'gpt-4o-mini' NOT NULL,
        ADD COLUMN token_used BIGINT DEFAULT 0;
    """

    # alter table add sub skill type
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE api_key_usages 
        DROP COLUMN model_name,
        DROP COLUMN token_used;
    """

    # alter table remove sub skill type
    migrator.sql(sql_query)
