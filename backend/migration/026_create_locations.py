"""Peewee migrations -- 026_create_locations.py."""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE locations (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            address VARCHAR(150) NOT NULL,
            city VARCHAR(150) NOT NULL,
            state VARCHAR(150),
            country VARCHAR(150),
            pincode BIGINT,
            is_deleted BOOLEAN DEFAULT FALSE,
            business_id BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            deleted_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREI<PERSON><PERSON> KEY (business_id) REFERENCES businesses(id)
        );
    """

    # create table schedule_interview
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_locations_id ON locations (id);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_locations_id ON locations;")

    # drop table
    migrator.sql("DROP TABLE locations;")
