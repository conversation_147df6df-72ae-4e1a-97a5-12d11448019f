"""
Peewee migrations -- 048_add_resume_source_in_candidates.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE candidates 
        ADD COLUMN resume_source VARCHAR(255) NULL;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE candidates 
        DROP COLUMN resume_source;
    """
    # alter table query
    migrator.sql(sql_query)
