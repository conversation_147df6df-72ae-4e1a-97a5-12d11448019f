"""
Peewee migrations -- 028_change_location_pincode_column.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE locations MODIFY COLUMN pincode VARCHAR(255);
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE locations MODIFY COLUMN pincode BIGINT;
    """

    # alter table query
    migrator.sql(sql_query)
