"""
Peewee migrations -- 021_create_business_employee_role_permissions.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
    CREATE TABLE business_employee_role_permissions (
        id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        business_id BIGINT,
        node_id BIGINT,
        permission_id INT,
        employee_role_id INT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREI<PERSON><PERSON> KEY (business_id) REFERENCES businesses(id),
        FOREIGN KEY (node_id) REFERENCES nodes(id),
        <PERSON>OREI<PERSON><PERSON> KEY (permission_id) REFERENCES permissions(id),
        FOREI<PERSON><PERSON> KEY (employee_role_id) REFERENCES employee_roles(id),
        UNIQUE INDEX idx_business_employee_role_permissions_unique (employee_role_id, permission_id, node_id, business_id)
    );
    """

    # default insert
    migrator.sql(sql_query)

    sql_query = """
        INSERT INTO business_employee_role_permissions (node_id, permission_id, employee_role_id, business_id)
        SELECT 
            inserted_node.node_id,
            perms.perm_id,
            roles.role_id,
            businesses.business_id
        FROM 
            (SELECT id AS node_id 
            FROM nodes 
            WHERE name = 'Dashboard' AND type = 'EmployeeNode') AS inserted_node
        CROSS JOIN 
            (SELECT id AS perm_id 
            FROM permissions) AS perms
        CROSS JOIN 
            (SELECT id AS role_id 
            FROM employee_roles) AS roles
        CROSS JOIN 
            (SELECT id AS business_id 
            FROM businesses) AS businesses;
        """

    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `business_employee_role_permissions`")
