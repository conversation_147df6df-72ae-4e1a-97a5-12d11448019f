"""
Peewee migrations -- 044_candidate_screening_answers.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_screening_answers(
            id BIGINT PRIMARY KEY NOT NULL AUTO_INCREMENT,
            screening_interview_question_id BIGINT NOT NULL,
            business_id BIGINT NOT NULL,
            candidate_id BIGINT NOT NULL,
            opportunity_id BIGINT NOT NULL,
            schedule_interview_id BIGINT NOT NULL,
            answer TEXT NOT NULL,
            question_type TINYINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREI<PERSON><PERSON>EY(screening_interview_question_id) REFERENCES screening_interview_questions(id),
            FOR<PERSON><PERSON><PERSON> KEY(business_id) REFERENCES businesses(id),
            FOREIGN KEY(candidate_id) REFERENCES candidates(id),
            FOREIGN KEY(opportunity_id) REFERENCES opportunities(id),
            FOREIGN KEY(schedule_interview_id) REFERENCES schedule_interviews(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop table
    migrator.sql("DROP TABLE `candidate_screening_answers`;")
