"""
Peewee migrations -- 040_create_payment_plans.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE payment_plans (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            description TEXT,
            amount DECIMAL(10, 2) NOT NULL,
            currency VARCHAR(3) NOT NULL,
            status TINYINT DEFAULT 1,
            credit BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    """
    migrator.sql(sql_query)

    sql_insert_query = """
    INSERT INTO payment_plans (name, description, amount, currency, status, credit)
    VALUES
        ('basic', 'Basic plan with limited features, includes 100 credits', 9.99, 'USD', 1, 100),
        ('premium', 'Premium plan with full features, includes 500 credits', 19.99, 'USD', 1, 500);
    """

    migrator.sql(sql_insert_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `payment_plans`")
