"""
Peewee migrations -- 011_create_candidates.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE IF NOT EXISTS `candidates` (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            contact VARCHAR(255),
            linkedin VARCHAR(255),
            github VARCHAR(255),
            website VARCHAR(255),
            designation VARCHAR(255),
            is_fresher BOOLEAN DEFAULT FALSE,
            total_experience FLOAT,
            achievements TEXT,
            resume TEXT,
            is_deleted BOOLEAN DEFAULT FALSE,
            business_id BIGINT,
            professional_title VARCHAR(255),
            summary TEXT,
            dob <PERSON>,
            gender VARCHAR(25),
            status TINYINT DEFAULT 1,
            created_by_id BIGINT,
            opportunity_id BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by_id) REFERENCES employees(id),
            FOREIGN KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (opportunity_id) REFERENCES opportunities(id),
            UNIQUE (email, business_id)
        );
    """

    # create table candidates
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_candidates_id ON candidates (id);")
    migrator.sql("CREATE INDEX idx_candidates_email ON candidates (email);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_candidates_id ON candidates;")
    migrator.sql("DROP INDEX idx_candidates_email ON candidates;")

    # drop table
    migrator.sql("DROP TABLE `candidates`")
