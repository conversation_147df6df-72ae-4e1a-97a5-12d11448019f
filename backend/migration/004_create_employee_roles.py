"""
Peewee migrations -- 004_create_employee_roles.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE employee_roles (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            status TINYINT DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

    """
    migrator.sql(sql_query)
    migrator.sql(
        "CREATE UNIQUE INDEX idx_employee_roles_name ON employee_roles (name);"
    )
    sql_query = """
        INSERT INTO employee_roles (name, status) VALUES
            ('Super Admin', 1),
            ('Admin', 1),
            ('HR', 1),
            ('Interviewer', 1);
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_employee_roles_name ON employee_roles;")

    # drop table
    migrator.sql("DROP TABLE `employee_roles`")
