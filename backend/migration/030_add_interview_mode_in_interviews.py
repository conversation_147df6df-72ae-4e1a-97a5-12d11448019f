"""
Peewee migrations -- 030_add_interview_mode_in_interviews.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE schedule_interviews MODIFY COLUMN meeting_link VARCHAR(255) NULL;
    """
    # alter table modify meeting link
    migrator.sql(sql_query)

    sql_query = """
        ALTER TABLE schedule_interviews ADD COLUMN interview_mode INT;
    """

    # alter table add interview modes
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE schedule_interviews DROP COLUMN interview_mode;
    """

    # alter table remove interview modes
    migrator.sql(sql_query)
