"""
Peewee migrations -- 041_create_payments.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE payments (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            stripe_payment_id VARCHAR(255) NOT NULL UNIQUE,
            amount_received DECIMAL(10, 2) NOT NULL,
            currency VARCHAR(3) NOT NULL,
            event_status VARCHAR(50) NOT NULL,
            business_id BIGINT,
            payment_plan_id BIGINT,
            credit BIGINT,
            payment_intent_id VARCHAR(255),
            payment_method VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (payment_plan_id) REFERENCES payment_plans(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `payments`")
