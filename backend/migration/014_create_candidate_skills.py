"""
Peewee migrations -- 014_create_candidate_skills.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_skills (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT,
            name VARCHAR(255) NOT NULL,
            skill_type ENUM('Technical', 'Soft') NOT NULL,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (candidate_id) REFERENCES candidates(id)
        );
    """

    # create table candidate_skills
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_candidate_skills_id ON candidate_skills (id);")
    migrator.sql("CREATE INDEX idx_candidate_skills_name ON candidate_skills (name);")
    migrator.sql(
        "CREATE INDEX idx_candidate_skills_type ON candidate_skills (skill_type);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_candidate_skills_id ON candidate_skills;")
    migrator.sql("DROP INDEX idx_candidate_skills_name ON candidate_skills;")
    migrator.sql("DROP INDEX idx_candidate_skills_type ON candidate_skills;")

    # drop table
    migrator.sql("DROP TABLE `candidate_skills`")
