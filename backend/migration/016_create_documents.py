"""
Peewee migrations -- 016_create_documents.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_documents (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT NOT NULL,
            content_type VARCHAR(255),  
            document varchar(255),
            name varchar(255),
            status TINYINT DEFAULT 1,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (candidate_id) REFERENCES candidates(id)
        );
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop table
    migrator.sql("DROP TABLE `candidate_documents`")
