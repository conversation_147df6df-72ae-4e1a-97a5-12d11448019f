"""
Peewee migrations -- 054_add_extra_fields_column_in_opportunities.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE opportunities ADD COLUMN extra_fields JSON;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE opportunities DROP COLUMN extra_fields;
    """

    # alter table query
    migrator.sql(sql_query)
