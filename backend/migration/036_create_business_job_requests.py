"""
Peewee migrations -- 036_create_business_job_requests.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE business_job_requests (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            resume TEXT,
            status TINYINT DEFAULT 1,
            business_id BIGINT NOT NULL,
            opportunity_id BIGINT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (business_id) REFERENCES businesses(id),
            FOREIGN KEY (opportunity_id) REFERENCES opportunities(id)
        );
    """
    migrator.sql(sql_query)

    # insert queries
    sql_query = """
        INSERT INTO nodes (name, singular_name, unique_key, path, type, order_num, icon, description) VALUES
            ('Job Requests', 'Job Request', 'employee_job_requests', '/admin/job_requests', 'EmployeeNode', 7, 'job_request', 'Explore our comprehensive list of job requests.')
        """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE `business_job_requests`")
