"""
Peewee migrations -- 018_search_filters.py.
"""

from contextlib import suppress
import peewee as pw
from peewee_migrate import Migrator

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE search_filters (
            id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            opportunity_id BIGINT NULL,
            qualification_id BIGINT NULL,
            experience INT NULL,
            status TINYINT DEFAULT 1,
            resume_uploaded_after DATE,
            created_by_id BIGINT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            business_id BIGINT NOT NULL,
            FOREIG<PERSON> KEY (opportunity_id) REFERENCES opportunities(id),
            FOREIG<PERSON> KEY (qualification_id) REFERENCES qualifications(id),
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (created_by_id) REFERENCES employees(id),
            <PERSON>OR<PERSON><PERSON>N <PERSON>EY (business_id) REFERENCES businesses(id)
        );
    """

    # create table search_filters
    migrator.sql(sql_query)

    # create indexes
    migrator.sql("CREATE INDEX idx_search_filters_id ON search_filters (id);")


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_search_filters_id ON search_filters;")

    # drop table
    migrator.sql("DROP TABLE search_filters;")
