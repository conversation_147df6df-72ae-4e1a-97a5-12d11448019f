"""
Peewee migrations -- 024_create_email_tracks.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE email_tracks (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            body TEXT,
            subject VARCHAR(255),
            to_email TEXT,
            cc_email TEXT,
            bcc_email TEXT,
            model_name VARCHAR(255),
            model_id VARCHAR(255),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
    """

    # create table schedule_interview
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE email_tracks;")
