"""
Peewee migrations -- 032_add_sub_skill_type_in_candidate_skills.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""

    sql_query = """
        ALTER TABLE candidate_skills ADD COLUMN sub_skill_type VARCHAR(150) NOT NULL;
    """

    # alter table add sub skill type
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE candidate_skills DROP COLUMN sub_skill_type;
    """

    # alter table remove sub skill type
    migrator.sql(sql_query)
