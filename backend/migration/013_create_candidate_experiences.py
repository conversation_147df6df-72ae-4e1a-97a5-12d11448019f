"""
Peewee migrations -- 013_create_candidate_experiences.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_experiences (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT,
            is_current_experience BOOLEAN DEFAULT false,
            employer VARCHAR(255) NOT NULL,
            position VARCHAR(255) NOT NULL,
            start_date DATE,
            end_date DATE,
            responsibilities TEXT,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIG<PERSON> KEY (candidate_id) REFERENCES candidates(id)
        );
    """

    # create table candidate_experiences
    migrator.sql(sql_query)

    # create indexes
    migrator.sql(
        "CREATE INDEX idx_candidate_experiences_id ON candidate_experiences (id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_candidate_experiences_id ON candidate_experiences;")

    # drop table
    migrator.sql("DROP TABLE `candidate_experiences`")
