"""
Peewee migrations -- 050_create_candidate_profile_comments.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
    CREATE TABLE candidate_profile_comments (
        id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        candidate_id BIGINT,
        business_id BIGINT,
        employee_id BIGINT,
        comment TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (candidate_id) REFERENCES candidates(id),
        FOREIGN KEY (business_id) REFERENCES businesses(id),
        FOREIG<PERSON> KEY (employee_id) REFERENCES employees(id)
    );
    """
    # create table candidate_profile_comments
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    migrator.sql("DROP TABLE  `candidate_profile_comments`;")
