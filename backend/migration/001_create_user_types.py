"""
Peewee migrations -- 001_create_user_types.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE user_types (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            status TINYINT DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    """
    migrator.sql(sql_query)
    migrator.sql("CREATE UNIQUE INDEX idx_user_types_name ON user_types (name);")

    sql_query = """
        INSERT INTO user_types (name, status) VALUES
            ('Super Admin', 1),
            ('Business Admin', 1),
            ('Free User', 1);
    """
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_user_types_name ON user_types;")

    # drop table
    migrator.sql("DROP TABLE `user_types`")
