"""
Peewee migrations -- 058_add_custom_fields_in_candidate_feedbacks.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE candidate_interview_feedbacks
        ADD COLUMN rating_fields TEXT,
        ADD COLUMN approved_status TINYINT DEFAULT 0;
    """
    # create table candidate_interview_suggestion
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    rollback_query = """
        ALTER TABLE candidate_interview_feedbacks
        DROP COLUMN rating_fields,
        DROP COLUMN approved_status;
    """

    # drop columns
    migrator.sql(rollback_query)
