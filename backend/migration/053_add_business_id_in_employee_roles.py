"""
Peewee migrations -- 053_add_business_id_in_employee_roles.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE employee_roles
        ADD COLUMN business_id BIGINT NULL,
        ADD CONSTRAINT fk_employee_roles_business FOREIGN KEY (business_id) REFERENCES businesses(id);
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE employee_roles
        DROP CONSTRAINT fk_employee_roles_business,
        DROP COLUMN business_id;
    """

    # alter table query
    migrator.sql(sql_query)
