"""
Peewee migrations -- 051_add_fileds_in_document_upload_link_requests.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE document_upload_link_requests 
        ADD COLUMN adhar_certificate BOOLEAN DEFAULT true,
        ADD COLUMN pan_certificate BOOLEAN DEFAULT true,
        ADD COLUMN degree_certificate BOOLEAN DEFAULT true,
        ADD COLUMN other_certificate BOOLEAN DEFAULT true;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE document_upload_link_requests 
        DROP COLUMN adhar_certificate,
        DROP COLUMN pan_certificate,
        DROP COLUMN degree_certificate,
        DROP COLUMN other_certificate;
    """
    # alter table query
    migrator.sql(sql_query)
