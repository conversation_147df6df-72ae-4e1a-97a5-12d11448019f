"""
Peewee migrations -- 025_add_response_and_responsibilities_in_opportunities.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
    ALTER TABLE opportunities
    ADD COLUMN ai_response TEXT,
    ADD COLUMN responsibilities TEXT;
    """

    # alter table
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
    ALTER TABLE opportunities
    DROP COLUMN ai_response,
    DROP COLUMN responsibilities;
    """

    # alter table rollback
    migrator.sql(sql_query)
