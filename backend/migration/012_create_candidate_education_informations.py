"""
Peewee migrations -- 012_create_candidate_education_informations.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_education_informations (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT,
            qualification_id BIGINT,
            degree VARCHAR(255) NOT NULL,
            university VARCHAR(255) NOT NULL,
            grade VARCHAR(255),
            start_date DATE,
            end_date DATE,
            is_deleted BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (candidate_id) REFERENCES candidates(id),
            FOREIGN KEY (qualification_id) REFERENCES qualifications(id)
        );
    """

    # create table candidate_education_informations
    migrator.sql(sql_query)

    # create indexes
    migrator.sql(
        "CREATE INDEX idx_candidate_education_informations_id ON candidate_education_informations (id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql(
        "DROP INDEX idx_candidate_education_informations_id ON candidate_education_informations;"
    )

    # drop table
    migrator.sql("DROP TABLE `candidate_education_informations`;")
