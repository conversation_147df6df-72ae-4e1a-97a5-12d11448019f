"""
Peewee migrations -- 003_create_businesses.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE businesses (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            website VARCHAR(255) NOT NULL,
            subdomain VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            email_verified BOOLEAN DEFAULT false,
            contact_number VARCHAR(100),
            location TEXT,
            status TINYINT DEFAULT '1',
            otp VARCHAR(6) DEFAULT NULL,
            otp_expire_at DATETIME NULL DEFAULT NULL,
            is_deleted BOOLEAN DEFAULT FALSE,
            payment_status TINYINT DEFAULT 0,
            user_id BIGINT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql("CREATE UNIQUE INDEX idx_businesses_website ON businesses (website);")
    migrator.sql("CREATE UNIQUE INDEX idx_businesses_email ON businesses (email);")
    migrator.sql(
        "CREATE UNIQUE INDEX idx_businesses_subdomain ON businesses (subdomain);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql("DROP INDEX idx_businesses_website ON businesses;")
    migrator.sql("DROP INDEX idx_businesses_email ON businesses;")
    migrator.sql("DROP INDEX idx_businesses_subdomain ON businesses;")

    # drop table
    migrator.sql("DROP TABLE `businesses`;")
