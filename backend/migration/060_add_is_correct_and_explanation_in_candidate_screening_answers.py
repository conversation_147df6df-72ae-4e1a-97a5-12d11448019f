"""
Peewee migrations -- 060_add_is_correct_and_explanation_in_candidate_screening_answers.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE candidate_screening_answers
        ADD COLUMN is_correct BOOLEAN DEFAULT false,
        ADD COLUMN answer_explanation TEXT;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE candidate_screening_answers 
        DROP COLUMN is_correct,
        DROP COLUMN answer_explanation;
    """

    # alter table query
    migrator.sql(sql_query)
