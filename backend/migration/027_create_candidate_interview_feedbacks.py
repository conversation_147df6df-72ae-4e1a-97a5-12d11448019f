"""
Peewee migrations -- 027_create_candidate_interview_feedbacks.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migra<PERSON>

with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidate_interview_feedbacks (
            id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            candidate_id BIGINT,
            created_by_id BIGINT,
            schedule_interview_id BIGINT,
            interview_round TINYINT DEFAULT 1,
            feedback TEXT,
            rating INT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (candidate_id) REFERENCES candidates(id),
            <PERSON>OR<PERSON><PERSON><PERSON> (schedule_interview_id) REFERENCES schedule_interviews(id),
            FOR<PERSON><PERSON><PERSON> (created_by_id) REFERENCES employees(id)
        );
    """

    # create table schedule_interview
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    # drop table
    migrator.sql("DROP TABLE candidate_interview_feedbacks;")
