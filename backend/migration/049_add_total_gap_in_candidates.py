"""
Peewee migrations -- 049_add_total_gap_in_candidates.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        ALTER TABLE candidates 
        ADD COLUMN total_gap FLOAT DEFAULT 0;
    """

    # alter table query
    migrator.sql(sql_query)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""

    sql_query = """
        ALTER TABLE candidates 
        DROP COLUMN total_gap;
    """
    # alter table query
    migrator.sql(sql_query)
