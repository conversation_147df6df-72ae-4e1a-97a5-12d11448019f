"""
Peewee migrations -- 056_create_candidate_offer_letters.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    sql = """
    CREATE TABLE candidate_offer_letters (
        id BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
        candidate_id BIGINT NOT NULL,
        letter TEXT NOT NULL,
        created_by_id BIGINT,
        opportunity_id BIGINT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (candidate_id) REFERENCES candidates(id),
        FOREIGN KEY (created_by_id) REFERENCES employees(id),
        FOREI<PERSON><PERSON> KEY (opportunity_id) REFERENCES opportunities(id)
    );
    """
    migrator.sql(sql)


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    migrator.sql("DROP TABLE candidate_offer_letters;")
