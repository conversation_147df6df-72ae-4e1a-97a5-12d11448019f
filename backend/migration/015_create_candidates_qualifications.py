"""
Peewee migrations -- 015_create_candidates_qualifications.py.
"""

from contextlib import suppress

import peewee as pw
from peewee_migrate import Migrator


with suppress(ImportError):
    import playhouse.postgres_ext as pw_pext


def migrate(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your migrations here."""
    sql_query = """
        CREATE TABLE candidates_qualifications (
            candidate_id BIGINT,
            qualification_id BIGINT,
            PRIMARY KEY (candidate_id, qualification_id),
            FOREIGN KEY (candidate_id) REFERENCES candidates(id),
            FOREIGN KEY (qualification_id) REFERENCES qualifications(id)
        );
    """
    migrator.sql(sql_query)
    migrator.sql(
        "CREATE INDEX idx_candidate_qualification_id ON candidates_qualifications (candidate_id, qualification_id);"
    )


def rollback(migrator: Migrator, database: pw.Database, *, fake=False):
    """Write your rollback migrations here."""
    # drop indexes
    migrator.sql(
        "DROP INDEX idx_candidate_qualification_id ON candidates_qualifications;"
    )

    # drop table
    migrator.sql("DROP TABLE `candidates_qualifications`;")
