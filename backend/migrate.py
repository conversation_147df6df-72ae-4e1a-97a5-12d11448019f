import click
from subprocess import run
from pathlib import Path
from app.config import DATABASE_URL
from typing import Optional
import datetime

MIGRATION_DIR = Path("migration")


@click.group()
def cli():
    """
    Database Migration Tool.
    """
    pass


@cli.command()
@click.option(
    "--name",
    required=False,
    default=None,
    type=str,
    help="Name of the specific migration to apply.",
)
def migrate(name: Optional[str] = None):
    """
    Perform database migrations.
    """
    # Validate directory existence
    if not MIGRATION_DIR.exists():
        click.echo(f"Error: Migration directory '{MIGRATION_DIR}' not found.")
        return

    # Execute migrations command using subprocess
    command = (
        f"pw_migrate migrate --database {DATABASE_URL} --directory {MIGRATION_DIR}"
    )

    if name:
        command += f" --name {name}"

    run(command, shell=True)


@cli.command()
@click.option(
    "--count",
    required=False,
    default=1,
    type=int,
    help="Number of last migrations to be rolled back.Ignored in case of non-empty name",
)
def rollback(count: int = 1):
    """
    Rollback database migrations.
    """
    # Validate directory existence
    if not MIGRATION_DIR.exists():
        click.echo(f"Error: Migration directory '{MIGRATION_DIR}' not found.")
        return

    # Construct command string
    command = f"pw_migrate rollback --database {DATABASE_URL} --directory {MIGRATION_DIR} --count {count}"

    # Execute rollback command using subprocess
    run(command, shell=True)


@cli.command()
@click.argument("name")
@click.option(
    "--auto",
    is_flag=True,
    help="Scan sources and create db migrations automatically. Supports autodiscovery.",
)
@click.option(
    "--auto-source",
    type=str,
    help="Set to python module path for changes autoscan (e.g. 'package.models'). Current directory will be recursively scanned by default.",
)
def create(  # noqa: PLR0913
    name: Optional[str] = None,
    auto: bool = False,
    auto_source: str = None,
):
    """
    Create a migration.
    """

    # Validate directory existence
    if not MIGRATION_DIR.exists():
        click.echo(f"Error: Migration directory '{MIGRATION_DIR}' not found.")
        return

    # Construct command string
    command = f"pw_migrate create --database {DATABASE_URL} --directory {MIGRATION_DIR} {name}"

    if auto:
        command += " --auto"
    if auto_source:
        command += f" --auto-source {auto_source}"

    command += " -v"

    # Execute rollback command using subprocess
    run(command, shell=True)


@cli.command()
def status():
    """
    status of migrations.
    """

    # Validate directory existence
    if not MIGRATION_DIR.exists():
        click.echo(f"Error: Migration directory '{MIGRATION_DIR}' not found.")
        return

    # Construct command string
    command = f"pw_migrate list --database {DATABASE_URL} --directory {MIGRATION_DIR}"

    # Execute rollback command using subprocess
    run(command, shell=True)


if __name__ == "__main__":
    cli()
