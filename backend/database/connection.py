from app.config import <PERSON><PERSON><PERSON>ASE_URL
from dotenv import load_dotenv
from urllib.parse import urlparse, unquote
from playhouse.pool import PooledMySQLDatabase
from playhouse.shortcuts import ReconnectMixin

load_dotenv()


class CustomPooledMySQLDatabase(ReconnectMixin, PooledMySQLDatabase):
    pass


def parse_database_url(url):
    parsed_url = urlparse(url)
    return {
        "database": parsed_url.path[1:],
        "user": unquote(parsed_url.username),
        "password": unquote(parsed_url.password),
        "host": parsed_url.hostname,
        "port": parsed_url.port,
    }


db_params = parse_database_url(DATABASE_URL)

# Define your database connection
db = CustomPooledMySQLDatabase(
    db_params["database"],
    user=db_params["user"],
    password=db_params["password"],
    host=db_params["host"],
    port=db_params["port"],
    max_connections=64,
    stale_timeout=300,
)


def is_db_connected():
    """
    Checks if the database connection is usable.

    Args:
    - db (peewee.Database): The Peewee database instance.

    Returns:
    - bool: True if the connection is usable, False otherwise.
    """
    try:
        # Use database connection context to ensure connection is properly handled
        with db.connection_context():
            return db.is_connection_usable()
    except Exception as e:
        print(f"Error checking database connection: {e}")
        return False


# Define your database connection
db.execute_sql("SET GLOBAL time_zone = '+00:00'")
db.execute_sql("SET GLOBAL interactive_timeout = 28800;")
db.execute_sql("SET GLOBAL wait_timeout = 28800;")
