import pptx
import logging
import requests
from io import BytesIO
from extraction_modules.text_convertor.base_text_convertor import BaseTextConvertor


class PPTToText(BaseTextConvertor):
    SUPPORTED_FORMATS = [".ppt", ".pptx"]

    def _extract_text(self, file_path) -> str:
        try:
            if not self.valid_format(file_path):
                return ""

            text = ""
            presentation = PPTToText._get_file(file_path)
            for slide in presentation.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
            return text
        except Exception as e:
            logging.error(f"Error in extracting text from PPT: {e}")
            return ""

    @staticmethod
    def _get_file(path: str):
        if path.startswith("http://") or path.startswith("https://"):
            response = requests.get(path)
            if response.status_code == 200:
                return pptx.Presentation(stream=BytesIO(response.content))
            else:
                logging(
                    f"Failed to fetch presentation from URL: {path}. Status code: {response.status_code}"
                )
                return None
        else:
            return pptx.Presentation(path)
