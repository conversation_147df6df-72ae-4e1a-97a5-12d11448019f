import logging
from odf import text, teletype
from odf.opendocument import load
import requests
from io import BytesIO
from extraction_modules.text_convertor.base_text_convertor import BaseTextConvertor


class ODFToText(BaseTextConvertor):
    SUPPORTED_FORMATS = [".odt", ".ods", ".odp", ".odg"]

    def _extract_text(self, file_path) -> str:
        try:
            if not self.valid_format(file_path):
                return ""

            extracted_text = ""
            odf_file = ODFToText._get_file(file_path)
            all_paragraphs = odf_file.getElementsByType(text.P)
            for paragraph in all_paragraphs:
                extracted_text += teletype.extractText(paragraph)
            return extracted_text
        except Exception as e:
            logging.error(f"Error in extracting text from ODF: {e}")
            return ""

    @staticmethod
    def _get_file(path: str):
        if path.startswith("http://") or path.startswith("https://"):
            response = requests.get(path)
            if response.status_code == 200:
                return load(BytesIO(response.content))
            else:
                logging(
                    f"Failed to fetch ODF document from URL: {path}. Status code: {response.status_code}"
                )
                return None
        else:
            return load(path)
