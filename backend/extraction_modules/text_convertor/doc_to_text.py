import docx
import logging
import requests
from io import By<PERSON><PERSON>
from typing import List, Dict, Optional
from extraction_modules.text_convertor.base_text_convertor import BaseTextConvertor


class DocToText(BaseTextConvertor):
    SUPPORTED_FORMATS = [".doc", ".docx"]

    def _extract_text(self, file_path: str) -> str:
        try:
            if not self.valid_format(file_path):
                logging.warning(f"Unsupported file format: {file_path}")
                return ""

            doc = self._get_file(file_path)
            if doc is None:
                logging.error(f"Failed to load document: {file_path}")
                return ""

            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            logging.error(f"Error extracting text from DOCX: {e}")
            return ""

    def _extract_text_blocks(self, file_path: str) -> List[Dict[str, str]]:
        try:
            doc = self._get_file(file_path)
            if doc is None:
                logging.error(f"Failed to load document: {file_path}")
                return []

            text_blocks = [{"text": paragraph.text} for paragraph in doc.paragraphs]
            return text_blocks
        except Exception as e:
            logging.error(f"Error extracting text blocks from DOCX: {e}")
            return []

    @staticmethod
    def _get_file(path: str) -> Optional[docx.Document]:
        try:
            if path.startswith("http://") or path.startswith("https://"):
                response = requests.get(path)
                if response.status_code == 200:
                    return docx.Document(BytesIO(response.content))
                else:
                    logging.error(
                        f"Failed to fetch document from URL: {path}. Status code: {response.status_code}"
                    )
                    return None
            else:
                return docx.Document(path)
        except Exception as e:
            logging.error(f"Error loading document from path: {path}. Exception: {e}")
            return None
