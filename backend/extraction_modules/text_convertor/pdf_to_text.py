from typing import List, Dict
import fitz
import logging
import requests
from io import BytesIO
from extraction_modules.text_convertor.base_text_convertor import BaseTextConvertor


class PDFToText(BaseTextConvertor):
    SUPPORTED_FORMATS = [".pdf"]

    def _extract_text(self, file_path) -> str:
        try:
            if not self.valid_format(file_path):
                return ""

            doc = PDFToText._get_file(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            return text
        except Exception as e:
            logging.error(f"Error in extracting text from PDF: {e}")
            return ""

    def _extract_text_blocks(self, file_path) -> List[Dict]:
        try:
            file_extension = self.get_file_extension(file_path)
            if file_extension not in self.SUPPORTED_FORMATS:
                logging.error(f"Unsupported file format: {file_extension}")
                return []

            doc = PDFToText._get_file(file_path)
            print(doc, "docs")
            text_blocks = []
            for page in doc:
                blocks = page.get_text("dict")["blocks"]
                for block in blocks:
                    text_blocks.append(block)
            return text_blocks
        except Exception as e:
            logging.error(f"Error in extracting text blocks from PDF: {e}")
            return []

    @staticmethod
    def _get_file(path: str):
        if path.startswith("http://") or path.startswith("https://"):
            response = requests.get(path)
            if response.status_code == 200:
                return fitz.open(stream=BytesIO(response.content))
            else:
                logging(
                    f"Failed to fetch PDF from URL: {path}. Status code: {response.status_code}"
                )
                return None
        else:
            return fitz.open(path)
