from typing import List, Dict
import asyncio
import logging
from extraction_modules.helper.file_helper import FileHelper


class BaseTextConvertor(FileHelper):
    SUPPORTED_FORMATS = []

    def __init__(self) -> None:
        pass

    def valid_format(self, file_path: str):
        file_extension = self.get_file_extension(file_path)
        if file_extension not in self.SUPPORTED_FORMATS:
            logging.error(f"Unsupported file format: {file_extension}")
            return False
        return True

    async def extract_text(self, file_path) -> str:
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_text, file_path
        )

    async def extract_text_blocks(self, file_path) -> List[Dict]:
        return await asyncio.get_event_loop().run_in_executor(
            None, self._extract_text_blocks, file_path
        )
