import os
import async<PERSON>
from typing import List, Optional
import extraction_modules.text_convertor as TextConvertor
from extraction_modules.openai_module import (
    OpenAIAPI,
    OpenAIFunctionCalls,
    OpenAiPrompts,
)
from extraction_modules.helper import <PERSON>sonProcessor<PERSON>el<PERSON>, FileHelper
from app.uploader import ResumeUploader
import logging
import json
from peewee import Model


class CVProcessor(FileHelper):
    def __init__(
        self,
        model: str = "gpt-4o",
        temperature: int = None,
        db_model: Optional[Model] = None,
    ):
        self.pdf_to_text = TextConvertor.PDFToText()
        self.odf_to_text = TextConvertor.ODFToText()
        self.ppt_to_text = TextConvertor.PPTToText()
        self.doc_to_text = TextConvertor.DocToText()
        self.api_client = OpenAIAPI(
            model=model, temperature=temperature, db_model=db_model
        )
        self.json_processor = JsonProcessorHelper()

    async def process_files_in_folder(self, folder_path: str) -> List[dict]:
        """
        Processes all the files in a folder.

        Args:
            folder_path (str): The path to the folder.

        Returns:
            List[dict]: A list of responses.
        """
        file_paths = [
            os.path.join(folder_path, file)
            for file in os.listdir(folder_path)
            if os.path.isfile(os.path.join(folder_path, file))
        ]
        print("Files in folder:", file_paths)

        # Extract text from all files
        extract_text_tasks = []
        for file_path in file_paths:
            file_extension = self.get_file_extension(file_path)
            if file_extension in self.pdf_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.pdf_to_text.extract_text(file_path))
            elif file_extension in self.odf_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.odf_to_text.extract_text(file_path))
            elif file_extension in self.ppt_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.ppt_to_text.extract_text(file_path))
            elif file_extension in self.doc_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.doc_to_text.extract_text(file_path))
            else:
                logging.error(f"Unsupported file format: {file_extension}")
                continue

        texts = await asyncio.gather(*extract_text_tasks)

        # Get responses for all texts
        response_tasks = [
            self.api_client.async_func_response(
                model="gpt-4o",
                system_message=OpenAiPrompts.info_extraction_prompt,
                messages=text,
                func=OpenAIFunctionCalls.candidate_cv_info_extraction,
                function=True,
            )
            for text in texts
        ]
        responses = await asyncio.gather(*response_tasks)

        return responses

    async def process_and_standardize_files_in_folder(
        self, folder_path: str
    ) -> List[dict]:
        """
        Processes all the files in a folder and standardizes the responses.

        Args:
            folder_path (str): The path to the folder.

        Returns:
            List[dict]: A list of standardized responses.
        """
        # Process files in the folder
        responses = await self.process_files_in_folder(folder_path)

        # Standardize all responses
        for response in responses:
            response = self.json_processor.standardize_dates_in_json(response)
            response = self.json_processor.convert_comma_separated_to_list(response)

        return responses

    # function
    async def _process_resume_file(self, file_path: str) -> dict:
        """
        Processes single resume standardizes the responses.

        Args:
            file_path (str): file path / file url.

        Returns:
            List[dict]: A list of standardized responses.
        """
        extract_text_tasks = []
        file_extension = self.get_file_extension(file_path)
        if file_extension in self.pdf_to_text.SUPPORTED_FORMATS:
            extract_text_tasks.append(self.pdf_to_text.extract_text(file_path))
        elif file_extension in self.odf_to_text.SUPPORTED_FORMATS:
            extract_text_tasks.append(self.odf_to_text.extract_text(file_path))
        elif file_extension in self.ppt_to_text.SUPPORTED_FORMATS:
            extract_text_tasks.append(self.ppt_to_text.extract_text(file_path))
        elif file_extension in self.doc_to_text.SUPPORTED_FORMATS:
            extract_text_tasks.append(self.doc_to_text.extract_text(file_path))
        else:
            logging.error(f"Unsupported file format: {file_extension}")

        texts = await asyncio.gather(*extract_text_tasks)

        # Get responses for all texts
        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_extraction_prompt,
                messages=text,
                func=OpenAIFunctionCalls.candidate_cv_info_extraction,
                function=True,
            )
            for text in texts
        ]
        responses = await asyncio.gather(*response_tasks)

        return responses[0]

    async def process_and_standardize_resume(self, file_path: str) -> dict:
        """
        Processes single resume standardizes the responses.

        Args:
            file_path (str): The path to the file.

        Returns:
            dict: A standardized response.
        """
        # Process resume file
        response = await self._process_resume_file(file_path)
        response = self.json_processor.standardize_dates_in_json(response)
        response = self.json_processor.convert_comma_separated_to_list(response)

        return response

    # function
    async def _process_resume_files(self, files: List[ResumeUploader]) -> dict:
        """
        Processes multiple resume standardizes the responses.

        Args:
            files (str): ResumeUploader objects.

        Returns:
            List[dict]: A list of standardized responses.
        """
        extract_text_tasks = []
        resume_paths = []
        for file in files:
            resume_url = file["url"] if file["on_cloud"] else file["file_path"]
            resume_path = file["file_path"]

            file_extension = self.get_file_extension(resume_url)
            if file_extension in self.pdf_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.pdf_to_text.extract_text(resume_url))
                resume_paths.append(resume_path)
            elif file_extension in self.odf_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.odf_to_text.extract_text(resume_url))
                resume_paths.append(resume_path)
            elif file_extension in self.ppt_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.ppt_to_text.extract_text(resume_url))
                resume_paths.append(resume_path)
            elif file_extension in self.doc_to_text.SUPPORTED_FORMATS:
                extract_text_tasks.append(self.doc_to_text.extract_text(resume_url))
                resume_paths.append(resume_path)
            else:
                logging.error(f"Unsupported file format: {file_extension}")

        texts = await asyncio.gather(*extract_text_tasks)

        # Get responses for all texts
        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_extraction_prompt,
                messages=text,
                func=OpenAIFunctionCalls.candidate_cv_info_extraction,
                function=True,
            )
            for text in texts
        ]
        responses = await asyncio.gather(*response_tasks)

        return {
            "responses": responses,
            "resume_paths": resume_paths,
            "total": len(resume_paths),
        }

    async def process_and_standardize_resumes(
        self, files: List[ResumeUploader]
    ) -> dict:
        """
        Processes single resume standardizes the responses.

        Args:
            files (str): ResumeUploader objects.

        Returns:
            dict: A standardized response.
        """
        # Process resume file
        data = await self._process_resume_files(files)
        responses = []
        for i in range(data["total"]):
            response = data["responses"][i]
            resume_path = data["resume_paths"][i]

            response = self.json_processor.standardize_dates_in_json(response)
            response = self.json_processor.convert_comma_separated_to_list(response)
            response["resume_path"] = resume_path
            responses.append(response)

            print(responses)

        return responses

    async def process_and_standardize_job(self, job_info: dict) -> dict:
        """
        Processes single job descriptions standardizes the responses.

        Args:
            file_path (str): The path to the file.

        Returns:
            dict: A standardized response.
        """
        # Process job information
        job_text = json.dumps(str(job_info))
        response_tasks = [
            self.api_client.async_func_response(
                system_message=OpenAiPrompts.info_job_extraction_prompt,
                messages=job_text,
                func=OpenAIFunctionCalls.job_description_info_extraction,
                function=True,
                function_name="JobDescription_information",
            )
        ]
        responses = await asyncio.gather(*response_tasks)
        response = responses[0]
        response = self.json_processor.standardize_dates_in_json(response)
        response = self.json_processor.convert_comma_separated_to_list(response)

        return response
