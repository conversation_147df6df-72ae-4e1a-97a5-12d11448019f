import pandas as pd
import spacy
import numpy as np

# Load spaCy model once globally
nlp = spacy.load("en_core_web_lg")


class ScoreMatcher:
    def __init__(self, threshold=0.7):
        self.nlp = nlp
        self.threshold = threshold

    def calculate_similarity(self, vector1, vector2):
        """Calculate cosine similarity between two vectors."""
        if np.linalg.norm(vector1) == 0 or np.linalg.norm(vector2) == 0:
            return 0.0
        return np.dot(vector1, vector2) / (
            np.linalg.norm(vector1) * np.linalg.norm(vector2)
        )

    def calculate_match_score(self, cv_skills, role_keywords):
        """Calculate match score based on keyword matches and semantic similarity."""
        cv_skills_series = pd.Series(cv_skills)
        cv_skills_vectors = cv_skills_series.apply(lambda skill: self.nlp(skill).vector)
        matching_keywords = [
            keyword
            for keyword in role_keywords
            if any(
                self.calculate_similarity(self.nlp(keyword).vector, vec)
                > self.threshold
                for vec in cv_skills_vectors
            )
        ]
        match_score = (
            (len(matching_keywords) / len(role_keywords)) if role_keywords else 0
        )
        return match_score * 30, matching_keywords  # Scale to 0 to 30

    def recursive_score(self, cv, features):
        """Recursively search for presence of features (keys) in cv dict."""
        score = 0
        for key, value in cv.items():
            if key in features:
                score += 1.25  # Increment per found feature
            if isinstance(value, dict):
                score += self.recursive_score(value, features)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        score += self.recursive_score(item, features)
        return score

    def score_contact_info(self, cv):
        """Score presence of essential contact fields."""
        required_fields = ["name", "email", "contact", "linkedin", "github"]
        return min(self.recursive_score(cv, required_fields), 5)  # max 5 points

    def score_summary(self, cv):
        """Score presence of summary or objective."""
        summary_features = ["summary"]
        return min(self.recursive_score(cv, summary_features), 5)  # max 5 points

    def score_experience(self, cv, job_experience):
        """Score experience relative to job requirement."""
        cv_experience_years = cv.get("total_experience", 0)
        if job_experience == 0:
            return 0
        score = (cv_experience_years / job_experience) * 20
        return min(score, 20)  # max 20 points

    def score_education(self, cv, required_qualifications):
        """Score education by semantic similarity of degrees."""
        cv_degrees = cv.get("processed_degrees", [])
        if not cv_degrees or not required_qualifications:
            # Missing info leads to zero score
            return 0

        qual_vectors = np.array([self.nlp(q).vector for q in cv_degrees])
        req_qual_vectors = np.array(
            [self.nlp(rq).vector for rq in required_qualifications]
        )

        similarity_matrix = np.zeros((len(cv_degrees), len(required_qualifications)))
        for i, qv in enumerate(qual_vectors):
            for j, rqv in enumerate(req_qual_vectors):
                similarity_matrix[i, j] = self.calculate_similarity(qv, rqv)

        max_similarities = np.max(similarity_matrix, axis=1)
        final_score = np.mean(max_similarities) * 10  # Scale max 10
        return min(final_score, 10)

    def score_certifications(self, achievements):
        if achievements is None:
            return 0
        certifications_count = 0
        for achievement in achievements:
            if isinstance(achievement, dict):
                certifications_count += len(achievement)
            else:
                # If achievement is not dict, count it as one
                certifications_count += 1
        return min(certifications_count * 1.5, 10)  # max 10 points

    def score_projects(self, cv):
        """Count responsibilities in current and past experience."""
        responsibilities_count = 0
        current_projects = cv.get("candidate_current_experience", [])
        past_experience = cv.get("candidate_past_experience", [])
        if past_experience is None:
            past_experience = []

        try:
            # Count responsibilities in current projects
            for project in current_projects:
                if isinstance(project, dict):
                    # Assuming 'responsibilities' is the key here instead of ' '
                    responsibilities_count += len(project.get("responsibilities", []))

            # Count responsibilities in past experiences
            for experience in past_experience:
                if isinstance(experience, dict):
                    responsibilities_count += len(
                        experience.get("responsibilities", [])
                    )
                elif isinstance(experience, str):
                    responsibilities_count += 1  # Count string as one responsibility
        except Exception as e:
            print(f"Error while processing projects: {e}")
            return 0
        return min(responsibilities_count, 10)  # max 10 points

    def calculate_score(self, cv, jd):
        """Calculate total score for a CV against a job description."""
        scores = {}
        try:
            total_experience = jd.get("total_experience", None)
            skills = jd.get("skills", [])
            qualifications = jd.get("qualifications", [])

            scores["contact_info"] = self.score_contact_info(cv)  # max 5
            scores["summary"] = self.score_summary(cv)  # max 5

            if total_experience is not None:
                scores["experience"] = self.score_experience(
                    cv, total_experience
                )  # max 20

            if skills:
                cv_skills = cv.get("candidate_skills", [])
                scores["skills"], _ = self.calculate_match_score(
                    cv_skills, skills
                )  # max 30

            if qualifications:
                scores["education"] = self.score_education(cv, qualifications)  # max 10

            scores["certifications"] = self.score_certifications(
                cv.get("candidate_achievements", [])
            )  # max 10
            scores["projects"] = self.score_projects(cv)  # max 10

            scores["Overall Impression"] = 10

            # Round numeric scores
            for key in scores:
                if isinstance(scores[key], (int, float)):
                    scores[key] = round(scores[key], 2)

            scores["total"] = round(
                sum(v for v in scores.values() if isinstance(v, (int, float))), 2
            )

        except Exception as e:
            print(f"Error in calculate_score: {e}")
            scores["total"] = "N/A"

        return scores
