candidate_cv_info_extraction = [
    {
        "name": "Candidate_information",
        "description": "extract all the information of the candidate that is available in the cv",
        "note": "IMPORTANT: All keys in the returned JSON must use lowercase exact names as per the schema (e.g., 'name', NOT 'Name'). Do not add or change key names. Include missing keys with empty string values.",
        "parameters": {
            "type": "object",
            "properties": {
                "candidate_personal_info": {
                    "name": "candidate_personal_info",
                    "description": (
                        "Extract personal info like name, address, contact etc from CV. "
                        "Return a JSON object with the following keys EXACTLY (case-sensitive): "
                        "`name`, `contact`, `linkedin`, `github`, `website`, `email`, `designation`. "
                        "Do NOT use any variations or capitalized keys such as 'Name' or 'Email'. "
                        "If some values are missing, set those keys to an empty string. "
                        "Return ONLY these keys—no extra keys allowed."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Name of the candidate",
                            },
                            "contact": {
                                "type": "string",
                                "description": "Extract the candidate's contact number, ensuring it includes the country code. If the country code is missing, determine and add it based on the information in the resume. Use the format: +1123456789 (avoid formats like +1 123 345 5678).",
                            },
                            "linkedin": {
                                "type": "string",
                                "description": "LinkedIn ID of candidate",
                            },
                            "github": {
                                "type": "string",
                                "description": "GitHub ID of candidate",
                            },
                            "website": {
                                "type": "string",
                                "description": "Portfolio website candidate",
                            },
                            "email": {
                                "type": "string",
                                "description": "Email Id of the candidate",
                            },
                            "designation": {
                                "type": "string",
                                "description": "Latest/Current Designation of the candidate",
                            },
                        },
                        "required": ["name", "contact", "email", "designation"],
                    },
                },
                "candidate_current_experience": {
                    "name": "candidate_current_experience",
                    "description": "Extract current work experience, employer, role, responsibilities,start date, end date etc",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "employers": {"type": "string", "description": "employer"},
                            "positions": {
                                "type": "string",
                                "description": "Current role/position",
                            },
                            "start_date": {
                                "type": "string",
                                "description": "Starting date of employment in dd/mm/yyyy format else none",
                            },
                            "end_date": {
                                "type": "string",
                                "description": "End date of employment in dd/mm/yyyy format else none/present",
                            },
                            "responsibilities": {
                                "type": "string",
                                "description": "Responsibilities in the past roles in a comma separated string",
                            },
                        },
                        "required": [
                            "employers",
                            "positions",
                            "start_date",
                            "end_date",
                            "responsibilities",
                        ],
                    },
                },
                "candidate_past_experience": {
                    "name": "candidate_past_experience",
                    "description": "Extract past work experiences, employers, roles, responsibilities,start date, end date etc",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "employers": {
                                "type": "string",
                                "description": "List of past employers",
                            },
                            "positions": {
                                "type": "string",
                                "description": "Past roles/positions held",
                            },
                            "start_date": {
                                "type": "string",
                                "description": "Starting date of employment in dd/mm/yyyy format else none",
                            },
                            "end_date": {
                                "type": "string",
                                "description": "End date of employment in dd/mm/yyyy format else none/present",
                            },
                            "responsibilities": {
                                "type": "string",
                                "description": "Responsibilities in the past roles in a comma separated string",
                            },
                        },
                        "required": [
                            "employers",
                            "positions",
                            "start_date",
                            "end_date",
                            "responsibilities",
                        ],
                    },
                },
                "candidate_education_info": {
                    "name": "candidate_education_info",
                    "description": "Extract education details, degrees, universities, grades,start date, end date etc from CV in a list",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "degrees": {
                                "type": "string",
                                "description": "List of degrees",
                            },
                            "universities": {
                                "type": "string",
                                "description": "Universities attended",
                            },
                            "grades": {
                                "type": "string",
                                "description": "Grades or CGPA in each degree/course",
                            },
                            "start_date": {
                                "type": "string",
                                "description": "Starting date of education in dd/mm/yyyy only convert it if in different format and if dd is not there then add 01 as default else none example 18th March, 2020 as 18/03/2020",
                            },
                            "end_date": {
                                "type": "string",
                                "description": "End date of education in dd/mm/yyyy only convert it if in different format and if dd is not there then add 01 as default none/present",
                            },
                        },
                        "required": [
                            "degrees",
                            "universities",
                            "grades",
                            "start_date",
                            "end_date",
                        ],
                    },
                },
                "candidate_projects": {
                    "type": "array",
                    "description": "A list of projects undertaken by the candidate, including title, technology, description, responsibilities, start date, and end date.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "Title of the project",
                            },
                            "technology": {
                                "type": "string",
                                "description": "Technologies used in the project",
                            },
                            "description": {
                                "type": "string",
                                "description": "Detailed description of the project",
                            },
                            "responsibilities": {
                                "type": "string",
                                "description": "Candidate's responsibilities in the project",
                            },
                            "start_date": {
                                "type": "string",
                                "description": "Start date of the project in dd/mm/yyyy format",
                            },
                            "end_date": {
                                "type": "string",
                                "description": "End date of the project in dd/mm/yyyy format",
                            },
                        },
                        "required": [
                            "title",
                            "technology",
                            "description",
                            "responsibilities",
                            "start_date",
                            "end_date",
                        ],
                    },
                },
                "candidate_certifications": {
                    "type": "array",
                    "description": "A list of certifications attained by the candidate.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Name of the certification",
                            },
                            "authority": {
                                "type": "string",
                                "description": "Issuing authority of the certification",
                            },
                            "issue_date": {
                                "type": "string",
                                "description": "Issue date of the certification in dd/mm/yyyy format",
                            },
                            "expiry_date": {
                                "type": "string",
                                "description": "Expiry date of the certification in dd/mm/yyyy format or 'None' if it doesn't expire",
                            },
                        },
                        "required": ["name", "authority", "issue_date"],
                    },
                },
                "summary": {
                    "type": "string",
                    "description": "candidate overall summary",
                },
                "candidate_technical_skills": {
                    "type": "array",
                    "description": "A list of lists where each inner list represents a technical skill and its associated type. The structure is as follows: \n- Each item in the outer list is a list containing exactly two elements. \n- The first element of each inner list is a string representing the skill name. \n- The second element is a string representing the type or category of the skill. \nExample format: [['Python', 'backend'], ['React', 'frontend'], ['Talent Acquisition', 'recruitment'], ['Employee Relations', 'employee_relations']]. \nEnsure that all skills are represented consistently as pairs of skill name and type.",
                    "items": {
                        "type": "array",
                        "description": "A list containing two elements: the skill name and its type.",
                        "items": {"type": "string"},
                        "minItems": 2,
                        "maxItems": 2,
                    },
                },
                "candidate_soft_skills": {
                    "type": "string",
                    "description": "A string representing a list of soft skills of the candidate. Convert this string into a Python list directly. The string will have the format of a Python list literal, e.g., \"['React', None, 'Vue', 'Angular']\".",
                },
                "candidate_achievements": {
                    "type": "string",
                    "description": "Extract Python List of achievements of the candidate e.g awards, certifications etc.",
                },
                "is_fresher": {
                    "type": "boolean",
                    "description": "Boolean value to check if the candidate is a fresher or not",
                },
                "candidate_qualification": {
                    "type": "string",
                    "description": "Highest 'educational' Qualification of the candidate",
                },
                "candidate_designation": {
                    "type": "string",
                    "description": "Latest/Current Designation of the candidate",
                },
                "total_experience": {
                    "type": "number",
                    "description": "Total experience of the candidate in years, calculated from the summary or left as None if not available.",
                },
            },
            "required": [
                "candidate_personal_info",
                "candidate_past_experience",
                "candidate_education_info",
                "summary",
                "candidate_soft_skills",
                "candidate_achievements",
                "is_fresher",
                "candidate_qualification",
                "candidate_projects",
                "candidate_certifications",
            ],
        },
    }
]

job_description_info_extraction = [
    {
        "name": "JobDescription_information",
        "description": "extract all the information of the job description that is available",
        "parameters": {
            "type": "object",
            "properties": {
                "job_requirements": {
                    "name": "job_requirements",
                    "description": "Extract job requirements like skills, qualifications, experience etc",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "skills": {
                                "type": "string",
                                "description": "Extract Python List of technical skills of jobs",
                            },
                            "qualifications": {
                                "type": "string",
                                "description": "Extract Python List of qualifications required for the job",
                            },
                            "experience": {
                                "type": "string",
                                "description": "Required experience for the job",
                            },
                        },
                        "required": ["skills", "qualifications", "experience"],
                    },
                },
                "job_responsibilities": {
                    "name": "job_responsibilities",
                    "description": "Extract job responsibilities",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "responsibilities": {
                                "type": "string",
                                "description": "Extract Python List of job responsibilities",
                            }
                        },
                        "required": ["responsibilities"],
                    },
                },
                "job_benefits": {
                    "name": "job_benefits",
                    "description": "Extract job benefits",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "benefits": {
                                "type": "string",
                                "description": "List of job benefits",
                            }
                        },
                        "required": ["benefits"],
                    },
                },
                "skills": {
                    "type": "string",
                    "description": "Extract Python List of technical skills required for this job",
                },
                "qualifications": {
                    "type": "string",
                    "description": "Extract Python List of qualifications required for this job",
                },
                "others": {
                    "type": "string",
                    "description": "Extract any other details required for this job",
                },
            },
            "required": [
                "job_requirements",
                "job_responsibilities",
                "job_benefits",
                "skills",
                "qualifications",
            ],
        },
    }
]


job_description_question_creation = [
    {
        "name": "Job_Description_Question_Generation",
        "description": "Generate a set of 5 questions (MCQs and subjective) based on the provided job description along with their answers.",
        "parameters": {
            "type": "object",
            "properties": {
                "job_description": {
                    "type": "string",
                    "description": "The text of the job description from which to generate questions.",
                },
                "question_count": {
                    "type": "integer",
                    "description": "Total number of questions to generate.",
                    "default": 5,
                },
                "question_types": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "enum": ["multiple_choice", "subjective"],
                    },
                    "description": "Types of questions to include.",
                    "default": ["multiple_choice", "subjective"],
                },
                "question_answers_list": {
                    "type": "array",
                    "items": {
                        "question_answer": {
                            "type": "object",
                            "items": {
                                "question": {
                                    "type": "string",
                                    "description": "Question based on job description",
                                },
                                "option": {
                                    "type": "string",
                                    "description": "If MCQ question then options based on question else empty string",
                                },
                                "answer": {
                                    "type": "string",
                                    "description": "Answer based on question",
                                },
                            },
                            "description": "contains question answer pairs related to the job description",
                        }
                    },
                    "description": "Contains list of objects having question and answers",
                },
            },
            "required": ["question_answers_list", "question_count"],
        },
    }
]

top_candidate_information = [
    {
        "name": "TopCandidateInformation",
        "description": "Returns the most relevant candidate for the given job profile",
        "parameters": {
            "type": "object",
            "properties": {
                "top_candidate_id": {
                    "type": "integer",
                    "description": "ID of the most suitable candidate",
                },
                "reason": {
                    "type": "string",
                    "description": "A brief reason why this candidate was selected",
                },
            },
            "required": ["top_candidate_id", "reason"],
        },
    }
]
