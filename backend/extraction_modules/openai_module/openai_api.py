from openai import OpenAI, AsyncOpenAI
import json
from typing import Optional
from peewee import Model

from app.config import OPENAI_KEY

client = OpenAI(api_key=OPENAI_KEY)
async_client = AsyncOpenAI(api_key=OPENAI_KEY)

COMPLETED_STATUS = "Completed"
FAILED_STATUS = "Failed"


class OpenAIAPI:
    def __init__(
        self,
        model: str = None,
        temperature: int = None,
        db_model: Optional[Model] = None,
    ) -> None:
        if model:
            self.model = model
        else:
            self.model = "gpt-4o"
        if temperature:
            self.temperature = temperature
        else:
            self.temperature = 0.1
        self.db_track_model = db_model

    async def async_func_response(
        self,
        system_message: str,
        messages: str,
        model: Optional[str] = None,
        func: Optional[object] = None,
        function: Optional[bool] = None,
        function_name: Optional[str] = "Candidate_information",
    ):
        gpt_model = model or self.model
        track_usage = None
        if self.db_track_model is not None:
            track_usage = self.db_track_model.dup()
            track_usage.api_key = self.mask_api_key(OPENAI_KEY, 4)
            track_usage.model_name = gpt_model
            track_usage.save()
        try:
            # Prepare the message payload
            message_payload = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": messages},
            ]

            # Determine function or non-function request
            request_kwargs = {
                "model": gpt_model,
                "temperature": self.temperature,
                "response_format": {"type": "json_object"},
                "messages": message_payload,
            }
            if function:
                request_kwargs.update(
                    {"functions": func, "function_call": {"name": function_name}}
                )
                response = await async_client.chat.completions.create(**request_kwargs)
                print("response")
                ### print(response.choices[0].message.function_call.arguments,"=======================------------------")
                response_content = json.loads(
                    response.choices[0].message.function_call.arguments, strict=False
                )
            else:
                response = await async_client.chat.completions.create(**request_kwargs)
                ### print(response.choices[0].message.content,"=======================------------------")
                response_content = response.choices[0].message.content

            # Mark the track_usage as completed if tracking is enabled
            if track_usage:
                track_usage.response_status = COMPLETED_STATUS
                track_usage.token_used = self.token_used(response=response)
                track_usage.save()

            return response_content
        except Exception as e:
            if track_usage is not None:
                track_usage.response_status = FAILED_STATUS
                track_usage.save()
            raise e

    def func_response(
        self,
        system_message,
        messages,
        func: Optional[object] = None,
        function: Optional[bool] = None,
    ):
        if function:
            response = client.chat.completions.create(
                model=self.model,
                temperature=self.temperature,
                response_format={"type": "json_object"},
                functions=func,
                function_call="auto",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": messages},
                ],
            )
            # ### print(response.choices[0].message.content,"=======================------------------")
            return json.loads(response.choices[0].message.function_call.arguments)
        else:
            response = client.chat.completions.create(
                model=self.model,
                temperature=self.temperature,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": messages},
                ],
            )
            # ### print(response.choices[0].message.content,"=======================------------------")
            return response.choices[0].message.content

    def image_response(self, base64_image):
        response = client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Give sentiment of this image if anything is written in this image then please write it down.",
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            },
                        },
                    ],
                }
            ],
            max_tokens=300,
        )
        return response.choices[0].message.content

    async def image_response_async(self, base64_image):
        response = await async_client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": """
                                    You are Helpful AI Assistant Helps in Finding hate sentiment in image
                                    if anything is written in this image then please write it down.
                                    if and hate sentiment is there then please write it down briefly.
                                    """,
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            },
                        },
                    ],
                }
            ],
            max_tokens=300,
        )
        return response.choices[0].message.content

    def token_used(self, response):
        ### print(response)
        try:
            return response.usage.total_tokens
        except Exception as e:
            return 0

    @staticmethod
    def mask_api_key(api_key: str, visible_chars: int = 4):
        """
        Mask the API key, keeping only the first and last 'visible_chars' visible.

        :param api_key: The API key to be masked.
        :param visible_chars: Number of characters to keep visible at the start and end.
        :return: Masked API key.
        """
        if len(api_key) <= 2 * visible_chars:
            return api_key  # Don't mask if the API key is too short
        return f'{api_key[:visible_chars]}{"*" * (len(api_key) - 2 * visible_chars)}{api_key[-visible_chars:]}'
