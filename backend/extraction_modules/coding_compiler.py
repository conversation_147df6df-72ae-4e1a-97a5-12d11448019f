import httpx
import logging
from typing import <PERSON><PERSON>, Any, List, Optional
from app.config import COMPILER_BASE_URL


class CodingCompiler:
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = base_url or COMPILER_BASE_URL

    async def execute_code(
        self, code: str, test_cases: List[dict], language: str = "python"
    ) -> Tuple[bool, int, int, str, Optional[Any]]:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/execute",
                    json={"code": code, "language": language, "test_cases": test_cases},
                )
                response.raise_for_status()
                response_data = response.json()

                logging.info(f"Execution response: {response_data}")

                test_case_results = response_data.get("test_case_results", [])
                total_success = sum(
                    1
                    for result in test_case_results
                    if result.get("status") == "Passed"
                )
                total = len(test_case_results)
                message = f"{total_success} successes from {total} test cases."

                return True, total_success, total, message, response_data

        except httpx.RequestError as e:
            logging.error(f"Request error: {e}")
            return False, 0, 0, "Request failed.", None

        except Exception as e:
            logging.error(f"Unexpected error: {e}")
            return False, 0, 0, "An error occurred.", None
