import dateparser


class JsonProcessorHelper:
    @staticmethod
    def convert_date_format(date_str: str) -> str:
        date = dateparser.parse(date_str)
        if date is not None:
            return date.strftime("%d/%m/%Y")
        else:
            return ""

    @classmethod
    def standardize_dates_in_json(cls, json_obj):
        for key, value in json_obj.items():
            if isinstance(value, dict):
                cls.standardize_dates_in_json(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        cls.standardize_dates_in_json(item)
            else:
                if (
                    key in ["start_date", "end_date"]
                    and isinstance(value, str)
                    and value.lower() != "present"
                ):
                    json_obj[key] = cls.convert_date_format(value)
        return json_obj

    @classmethod
    def convert_comma_separated_to_list(cls, json_obj: dict) -> dict:
        for key, value in json_obj.items():
            if isinstance(value, dict):
                cls.convert_comma_separated_to_list(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        cls.convert_comma_separated_to_list(item)
            else:
                if key in [
                    "candidate_skills",
                    "candidate_soft_skills",
                    "candidate_qualification",
                    "candidate_achievements",
                ]:
                    try:
                        json_obj[key] = [item.strip() for item in value.split(",")]
                    except Exception as e:
                        json_obj[key] = []
                        print(f"Error converting comma separated string to list: {e}")
        return json_obj
