from urllib.parse import urlparse
from pathlib import Path


class FileHelper:
    def get_file_extension(self, path: str) -> str:
        """
        Extracts the file extension from a given file path.

        Args:
            path (str): The path of the file or file path url.

        Returns:
            str: The file extension.
        """

        # Parse the URL to get the path
        if urlparse(path).scheme in ["http", "https"]:
            # It's a URL
            path = urlparse(path).path
        # For local file paths
        return Path(path).suffix

    def get_file_name(self, path: str) -> str:
        """
        Extracts the file name from a given file path.

        Args:
            file_path (str): The path of the file.

        Returns:
            str: The name of the file.
        """
        # Parse the URL to get the path
        if urlparse(path).scheme in ["http", "https", "ftp"]:
            # It's a URL
            path = urlparse(path).path
        # For local file paths
        return Path(path).stem
