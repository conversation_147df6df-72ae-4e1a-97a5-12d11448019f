import pandas as pd
import spacy
import numpy as np
import json
import datetime

# Load spaCy model
nlp = spacy.load("en_core_web_lg")


class CVMatcher:
    def __init__(self, nlp, cvs, threshold=0.7):
        self.nlp = nlp
        self.cvs = pd.DataFrame(cvs)
        self.threshold = threshold

    def calculate_similarity(self, vector1, vector2):
        """Calculate cosine similarity between two vectors."""
        return np.dot(vector1, vector2) / (
            np.linalg.norm(vector1) * np.linalg.norm(vector2)
        )

    def calculate_match_score(self, role_keywords, cv_skills):
        """Calculate match score based on keyword matches and semantic similarity."""
        cv_skills_series = pd.Series(cv_skills)
        cv_skills_vectors = cv_skills_series.apply(lambda skill: self.nlp(skill).vector)
        matching_keywords = [
            keyword
            for keyword in role_keywords
            if any(
                self.calculate_similarity(self.nlp(keyword).vector, vec)
                > self.threshold
                for vec in cv_skills_vectors
            )
        ]
        match_score = (
            len(matching_keywords) / len(role_keywords) if role_keywords else 0
        )
        return match_score * 30, matching_keywords  # Scale to 0 to 30

    def recursive_score(self, cv, features):
        """Recursively search for features in cv and increment score."""
        score = 0
        for key, value in cv.items():
            if key in features:
                score += 1.25  # Each field worth 1.25 points to scale up to 5 or 10
            if isinstance(value, dict):
                score += self.recursive_score(value, features)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        score += self.recursive_score(item, features)
        return score

    def score_contact_info(self, cv):
        """Score based on presence of essential contact information."""
        required_fields = ["name", "email", "phone", "linkedin", "github"]
        return min(self.recursive_score(cv, required_fields), 5)  # Max score of 5

    def score_summary(self, cv):
        """Score the presence of a career summary or objective."""
        summary_features = ["summary", "responsibilities"]
        return min(self.recursive_score(cv, summary_features), 5)  # Max score of 5

    def score_experience(self, cv, job_experience):
        """Score based on the number of years of experience relative to job description."""
        cv_experience_years = cv.get("total_experience", 0)
        return (
            20
            if cv_experience_years >= job_experience
            else (cv_experience_years / job_experience) * 20
        )

    def score_education(self, cv, required_qualifications):
        """Score educational qualifications by finding the maximum match among all degrees and required qualifications."""
        cv_degrees = cv.get("processed_degrees", [])
        if not cv_degrees or not required_qualifications:
            print("Either CV degrees or required qualifications are missing.")
            return 0  # No degrees or no required qualifications lead to 0 score.

        print("CV Degrees:", cv_degrees)
        print("Required Qualifications:", required_qualifications)

        degree_vectors = [self.nlp(degree).vector for degree in cv_degrees]
        qualification_vectors = [
            self.nlp(qualification).vector for qualification in required_qualifications
        ]

        max_similarity = 0

        # Compare each degree vector with each qualification vector
        for degree_index, degree_vector in enumerate(degree_vectors):
            for qual_index, qual_vector in enumerate(qualification_vectors):
                similarity = self.calculate_similarity(degree_vector, qual_vector)
                print(
                    f"Comparing '{cv_degrees[degree_index]}' with '{required_qualifications[qual_index]}': Similarity = {similarity}"
                )
                max_similarity = max(max_similarity, similarity)

        if max_similarity > 0:
            print(f"Maximum similarity score: {max_similarity}")
            final_score = max_similarity * 10
            print(f"Final Score: {final_score}")
            return final_score
        else:
            print("No matches found.")
            return 0

    def score_certifications(self, achievements):
        if achievements is None:
            return 0  # Return 0 if there are no achievements to process
        certifications_count = 0
        for achievement in achievements:
            certifications_count += len(
                achievement
            )  # Summing the number of entries in each dictionary
        return min(certifications_count * 1.5, 10)  # Scale to max 10 points

    def score_projects(self, cv):
        responsibilities_count = 0
        # Extract projects from current experience, ensuring it's a list of dictionaries
        current_projects = cv.get("candidate_current_experience", [])
        # Ensure past experiences are iterable and each item is treated as a dictionary
        past_experience = cv.get("candidate_past_experience", [])
        if past_experience is None:
            past_experience = []

        try:
            # Count responsibilities in current projects
            for project in current_projects:
                if isinstance(project, dict):
                    responsibilities_count += len(project.get("responsibilities", []))

            # Count responsibilities in past experiences, ensuring each is treated as a dictionary
            for experience in past_experience:
                if isinstance(experience, dict):
                    responsibilities_count += len(
                        experience.get("responsibilities", [])
                    )
                elif isinstance(experience, str):
                    # If it's a string, this suggests the entire experience is summarized in one string
                    responsibilities_count += (
                        1  # Optionally count this as one responsibility
                    )
        except Exception as e:
            print(f"Error while processing projects: {e}")
            return 0
        return min(responsibilities_count, 10)  # Scale to max 10 points

    def score_cv(self, cv, jd):
        """Score a single CV against the job description, including new scoring for certifications and projects."""
        scores = {}
        scores["contact_info"] = self.score_contact_info(cv["candidate_personal_info"])
        scores["summary"] = self.score_summary(cv)
        scores["experience"] = self.score_experience(cv, jd["total_experience"])
        scores["skills"], _ = self.calculate_match_score(
            jd["candidate_skills"], cv["candidate_skills"]
        )
        scores["education"] = self.score_education(cv, jd["candidate_qualification"])
        scores["certifications"] = self.score_certifications(
            cv.get("candidate_achievements", [])
        )
        scores["projects"] = self.score_projects(cv)
        scores["Overall Impression"] = 10
        scores["total"] = sum(scores.values())
        return scores


def preprocess_education_info(cvs):
    """Preprocess education information to extract degrees into a list."""
    for cv in cvs:
        degrees = []
        education_info = cv.get("candidate_education_info", [])
        for edu in education_info:
            if isinstance(edu, dict) and "degree" in edu:
                degrees.append(edu["degree"])
        cv["processed_degrees"] = degrees
