SMTP_API_KEY = 'api-key'
SMTP_PASSWORD = 'api-passwprd'
SMTP_FROM_EMAIL = 'email-address'
SMTP_SERVER = 'smtp-server'
SMTP_PORT = '587'

APP_URL = 'http://ats.local'
API_URL = 'http://api.ats.local'
BASE_DOMAIN = 'ats.local'
ALLOWED_ORIGINS = 'http://ats.local,http://*.ats.local'

HOST = 0.0.0.0
PORT = 3082
PYTHON_ENV = 'development'

DATABASE_URL = 'mysql://root:password@localhost:3306/ats'

OPENAI_KEY = "your-open-api-key"
STRIPE_SECRET_KEY = 'sk_secret_key'
STRIPE_WEBHOOK_SECRET = 'webhook_key'

CC_EMAILS = 'cc-email-address'
ENQUERY_EMAILS = 'enquery-email-address'
REDIS_URL = 'redis://localhost:6379/0'
