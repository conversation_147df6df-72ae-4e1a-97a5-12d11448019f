{% extends "layout/mailer_layout.html" %} {% block mail_content %} {% set
interview_mode = schedule_interview.interview_mode.name %}

<table width="100%" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td width="25"></td>
      <td>
        <table width="100%" cellspacing="0" cellpadding="0">
          <tbody>
            <tr>
              <td>
                <table width="100%" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td height="30"></td>
                    </tr>
                    <tr>
                      <td>
                        Dear
                        <span style="color: #000">{{ employee_name }}</span>,
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>

                    <tr>
                      <td>
                        This is a reminder for your upcoming interview with the
                        candidate.
                      </td>
                    </tr>

                    <tr>
                      <td height="20"></td>
                    </tr>

                    <tr>
                      <td>
                        <strong>Candidate Details:</strong>
                        <p><strong>Name:</strong> {{ candidate.name }}</p>
                        <p><strong>Email:</strong> {{ candidate.email }}</p>
                        {% if candidate.phone %}
                        <p><strong>Phone:</strong> {{ candidate.phone }}</p>
                        {% endif %}
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <strong>Interview Details:</strong>
                        <p>
                          <strong>Date & Time:</strong>
                          {{ schedule_interview.interview_at |
                          to_timezone(business.timezone) }}
                        </p>
                        <p>
                          <strong>Interview Mode:</strong> {{ interview_mode }}
                        </p>
                        <p>
                          <strong>Interview Round:</strong> {{
                          schedule_interview.interview_round }}
                        </p>
                        {% if schedule_interview.comment %}
                        <p>
                          <strong>Notes:</strong> {{ schedule_interview.comment
                          }}
                        </p>
                        {% endif %} {% if interview_mode == 'Video Call' %}
                        <p>
                          <strong>Meeting Link:</strong>
                          <a href="{{ schedule_interview.meeting_link }}">
                            {{ schedule_interview.meeting_link }}</a
                          >
                        </p>
                        {% endif %}
                      </td>
                    </tr>

                    <tr>
                      <td height="20"></td>
                    </tr>

                    {% if has_attachment %}
                    <tr>
                      <td>
                        Please find the candidate's resume attached to this
                        email for your reference.
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    {% endif %}

                    <tr>
                      <td>
                        Please ensure you are available at the scheduled time.
                        If you need to reschedule or have any concerns, kindly
                        coordinate with the recruitment team.
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>
                        Best regards,<br />
                        The Recruitease Pro Team
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
      <td width="25"></td>
    </tr>
    <tr>
      <td height="20"></td>
    </tr>
  </tbody>
</table>
{% endblock %}
