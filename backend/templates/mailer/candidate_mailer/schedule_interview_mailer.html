{% extends "layout/mailer_layout.html" %} {% block mail_content %} {% set
interview_mode = schedule_interview.interview_mode.name %}
<table width="100%" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td width="25"></td>
      <td>
        <table width="100%" cellspacing="0" cellpadding="0">
          <tbody>
            <tr>
              <td>
                <table width="100%" cellspacing="0" cellpadding="0">
                  <tbody>
                    <tr>
                      <td height="30"></td>
                    </tr>
                    <tr>
                      <td>
                        Dear
                        <span style="color: #000">{{ candidate.name }}</span>,
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>{{ title }}</td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>

                    {% if password %}
                    <tr>
                      <td>
                        <strong>Login Detail:</strong>
                        <p><strong>Email:</strong> {{ candidate.email }}</p>
                        <p><strong>Password:</strong> {{ password }}</p>
                      </td>
                    </tr>
                    <tr>
                      <td height="40"></td>
                    </tr>
                    <tr>
                      <td>
                        You can log in using the following URL:
                        <a
                          href="{{ login_url }}"
                          class="login-url"
                          target="_blank"
                          >{{ login_url }}</a
                        >
                      </td>
                    </tr>
                    {% endif %} {% if show_comment_only %}
                    <tr>
                      <td>
                        <p>{{ schedule_interview.comment }}</p>
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    {% else %}
                    <tr>
                      <td>
                        <strong>Detail:</strong>
                        <p>
                          <strong>Interviewer Name:</strong> {{
                          schedule_interview.interviewer.full_name() }}
                        </p>
                        {% if schedule_interview.comment %}
                        <p>
                          <strong>Message:</strong> {{
                          schedule_interview.comment }}
                        </p>
                        {% endif %}
                        <p>
                          <strong>Date & Time:</strong>
                          {{ schedule_interview.interview_at |
                          to_timezone(business.timezone) }}
                        </p>
                        <p>
                          <strong>Interview Mode:</strong>
                          {{ interview_mode }}
                        </p>
                        <p>
                          <strong>Interview Round:</strong>
                          {{ schedule_interview.interview_round }}
                        </p>
                        {% if interview_mode == 'Video Call' %}
                        <p>
                          <strong>Meeting Link:</strong>
                          <a href="{{ schedule_interview.meeting_link }}">
                            {{ schedule_interview.meeting_link }}</a
                          >
                        </p>
                        {% endif %}
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    {% endif %}

                    <tr>
                      <td>
                        Thank you for your interest in joining
                        <span style="color: #000">{{ business.name }}</span>. We
                        look forward to speaking with you.
                      </td>
                    </tr>
                    <tr>
                      <td height="20"></td>
                    </tr>
                    <tr>
                      <td>Best regards,<br />The Recruitease Pro Team</td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </td>
      <td width="25"></td>
    </tr>
    <tr>
      <td height="20"></td>
    </tr>
  </tbody>
</table>
{% endblock %}
