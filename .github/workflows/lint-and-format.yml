name: Format and Lint PRs

concurrency:
  group: format-lint-pr
  cancel-in-progress: true

on:
  workflow_dispatch:
  pull_request:
    branches:
      - development
      - main
      - master

jobs:
  format-lint:
    name: Format and Lint
    runs-on: ubuntu-latest
    permissions:
      id-token: write      # This is required for requesting the JWT
      contents: write      # This is required for actions/checkout
      pull-requests: write # This is required for commenting on PRs
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        if: github.event_name != 'pull_request'

      - name: Checkout
        uses: actions/checkout@v4
        if: github.event_name == 'pull_request'
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Install black
        run: |
          python -m pip install --upgrade pip
          pip install black

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 21.7.3

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 9.0.6
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v3
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Run PNPM Setup
        run: |
          pnpm install

      - name: Run PNPM Format and Lint
        id: check_format_lint
        env:
          FORMAT_OPTION: ${{ secrets.FORMAT_OPTION_COMMAND }} # Access the secret
          LINT_OPTION: ${{ secrets.LINT_OPTION_COMMAND }} # Access the secret
          BACKEND_FORMAT_OPTION: ${{ secrets.BACKEND_FORMAT_OPTION_COMMAND }} # Access the secret
        run: |
          # Run formatting
          $FORMAT_OPTION
          
          # Capture formatting issues
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          # Automatically generate the base URL for the repository and branch
          BASE_URL="https://github.com/${GITHUB_REPOSITORY}/blob/${{ github.event.pull_request.head.ref }}"

          # Run linting
          eval "$LINT_OPTION"| tee lint_output.txt

          # Run backend formatting
          eval "$BACKEND_FORMAT_OPTION" | tee backend_format_output.txt

          combined_errors=""

          # Capture format issues and replace newlines with --###--
          format_error_files=""
          while IFS= read -r line; do
            # Create a clickable link for each file
            file_link="[$line]($BASE_URL/$line)"
            format_error_files+="$file_link--###--"
          done < <(git diff --name-only)

          # Run the pnpm command and read the output line by line
          while IFS= read -r line; do
            if [[ "$line" =~ would\ reformat\ (.*) ]]; then
              file_path="${BASH_REMATCH[1]}"
              file_path="${file_path//\/home\/<USER>\/work\/ATS\/ATS\//}"
              file_link="[$file_path]($BASE_URL/$file_path)"
              format_error_files+="$file_link--###--"
            fi
          done < <(cat backend_format_output.txt)

          # Remove the trailing delimiter if any
          format_error_files=${format_error_files%--###--}

          # Capture lint errors and replace newlines with --###--
          lint_errors=""
          while IFS= read -r line; do
            # Create a clickable link for each file
            file_link="[$line]($BASE_URL/$line)"
            lint_errors+="$file_link--###--"
          done < <(cat lint_output.txt)

          # Remove the trailing delimiter if any
          lint_errors=${lint_errors%--###--}

          # Combine errors into the final output with formatting
          if [ -n "$format_error_files" ]; then
            combined_errors="Formatting issues found in the following files:--###--$format_error_files"
          fi
          if [ -n "$lint_errors" ]; then
            if [ -n "$combined_errors" ]; then
              combined_errors+="--###----###--"
            fi
            combined_errors+="Linting issues found in the following files:--###--$lint_errors"
          fi

          # Output the combined errors if they exist
          if [ -n "$combined_errors" ]; then
            echo "::set-output name=combined_errors::$combined_errors"
            echo "$combined_errors"
            exit 1
          fi

      - name: Comment on PR if there are issues
        if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            // Get the last commit details from the pull request event payload
            let authorUsername = undefined;
            try {
              const user = context.payload.pull_request.user ?? context.payload.pull_request.head.user;
              authorUsername = user.login;
              if(authorUsername) {
                authorUsername = `@${authorUsername}`;
              } else {
                const lastCommitSha = context.payload.pull_request.head.sha;
                const commitResponse = await github.rest.git.getCommit({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  commit_sha: lastCommitSha
                });
                authorUsername = commitResponse.data.author.name;
              }
            } catch {
              authorUsername = "Repo Owner";
            }

            let commentBody = `:x: **Attention ${authorUsername}: Issues detected. Please resolve these issues before proceeding with the merge.**\n\n`;
            try {
              const combinedErrors = `${{ steps.check_format_lint.outputs.combined_errors }}`;
              if (combinedErrors) {
                commentBody += combinedErrors.split("--###--").map(line => {
                  // Add bullet points to errors but not to titles
                  if (line.startsWith("Formatting issues") || line.startsWith("Linting issues")) {
                    return `**${line}**`; // Bold titles
                  }
                  return line.trim() ? `- ${line.trim()}` : ''; // Add bullet points to errors
                }).join("\n");
              } else {
                commentBody += "No specific issues found.\n";
              }
            } catch {
              commentBody += "No specific issues found.\n";
            }

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: commentBody
            })