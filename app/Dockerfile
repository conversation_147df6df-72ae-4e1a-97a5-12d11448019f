# Use Node.js 20 <PERSON> as the base image
FROM node:21.7.3-alpine

# Set the working directory
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@9.0.6

# Set environment variables
ARG NEXT_PUBLIC_APP_URL=https://recruiteasepro.com
ARG NEXT_PUBLIC_API_URL=https://api.recruiteasepro.com
ARG NEXT_PUBLIC_BASE_DOMAIN=recruiteasepro.com
ARG NEXT_PUBLIC_KEY=E5FBPJne7udyY8GgbV83vPwqe
ARG NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN=api.recruiteasepro.com
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY

# Set environment variables using the build arguments
ENV NEXT_PUBLIC_APP_URL=${NEXT_PUBLIC_APP_URL}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_BASE_DOMAIN=${NEXT_PUBLIC_BASE_DOMAIN}
ENV NEXT_PUBLIC_KEY=${NEXT_PUBLIC_KEY}
ENV NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN=${NEXT_PUBLIC_ALLOW_IMAGES_DOMAIN}
ARG NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}

# Copy package.json and pnpm-lock.yaml files
COPY package.json ./
#COPY pnpm-lock.yaml ./

# Install dependencies using pnpm
RUN pnpm install

# Copy the rest of the application code
COPY . .

# Build the Next.js application
RUN pnpm build

# Expose the port the app runs on
EXPOSE 3000

# Start the Next.js application
CMD ["pnpm", "start"]

