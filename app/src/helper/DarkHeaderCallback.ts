import { useEffect, useState } from "react";

/**
 * Custom React hook to track the scroll position of the window and determine
 * whether to display a dark header based on the scroll position.
 * Returns a boolean indicating whether the header should be in dark mode.
 */
function useDarkHeader(): boolean {
  // State variable to track whether the header should be in dark mode
  const [isDark, setIsDark] = useState<boolean>(false);

  useEffect(() => {
    // Event listener to handle scroll events
    const handleScroll = () => {
      // Get the vertical scroll position of the window
      const scroll = window.scrollY;
      // Update the state based on the scroll position
      setIsDark(scroll >= 20);
    };

    // Add event listener for the "scroll" event when the component mounts
    window.addEventListener("scroll", handleScroll);

    // Clean up by removing the event listener when the component unmounts
    return () => window.removeEventListener("scroll", handleScroll);
  }, []); // Empty dependency array ensures the effect runs only once when the component mounts

  // Return the current state indicating whether the header should be in dark mode
  return isDark;
}

export default useDarkHeader;
