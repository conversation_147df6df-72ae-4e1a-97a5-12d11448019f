import React from "react";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";

const SpeechToText: React.FC = () => {
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();

  if (!browserSupportsSpeechRecognition) {
    return <span>Browser doesn&apos;t support speech recognition.</span>;
  }

  const startListening = () =>
    SpeechRecognition.startListening({ continuous: true, language: "en-US" });

  const stopListening = () => SpeechRecognition.stopListening();

  return (
    <div style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h2>Speech to Text</h2>
      <p>
        Click the button below and start speaking. Your speech will be converted
        to text.
      </p>
      <button onClick={listening ? stopListening : startListening}>
        {listening ? "Stop Listening" : "Start Listening"}
      </button>
      <button onClick={resetTranscript}>Reset Transcript</button>
      <h3>Transcript:</h3>
      <p>{transcript || "No speech detected yet."}</p>
    </div>
  );
};

export default SpeechToText;
