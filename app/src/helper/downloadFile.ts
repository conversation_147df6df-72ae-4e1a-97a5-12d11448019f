import flashMessage from "@src/components/FlashMessage";
import axios from "axios";
import { getFileNameFromPresignedUrl } from "./common";

export const downloadFile = async (url: any, filename?: string) => {
  try {
    // Making a GET request to fetch the file as a blob
    const response = await axios({
      url,
      method: "GET",
      responseType: "blob", // Important for handling file downloads
    });

    // Creating a URL for the blob and a temporary link element
    const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = blobUrl;

    // Use the provided filename or extract from URL
    const fileName = filename || getFileNameFromPresignedUrl(url);
    if (fileName) {
      link.setAttribute("download", fileName); // Setting the download attribute with the filename
    }

    // Appending the link to the document, triggering the download, and then cleaning up
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link); // Clean up
  } catch (error) {
    flashMessage("Something went wrong!", "error"); // Logging any errors
  }
};
