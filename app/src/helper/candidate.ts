import { getCandidateSelfDetail } from "@src/redux/actions";
import { CandidateInterface } from "@src/redux/interfaces";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";

/**
 * A custom React hook that returns the permissions for the current employee.
 * Returns default permissions if the employee is a "Super Admin" or if the role is not found.
 * @returns The permissions for the current employee's role.
 */
export const useCurrentCandidate = (): CandidateInterface | null => {
  const dispatch = useAppDispatch();
  const [candidateDetail, setCandidateDetail] =
    useState<CandidateInterface | null>(null);
  const candidateInfo = useSelector(
    (state: RootState) => state.wildcardCandidate.candidate,
  );
  const candidate = useSelector((state: RootState) => state.auth.candidate);

  useEffect(() => {
    if (candidate && candidateInfo) {
      if (candidateInfo.id == candidate.id) {
        setCandidateDetail(candidateInfo);
      } else {
        setCandidateDetail(null);
      }
    } else {
      setCandidateDetail(null);
    }
  }, [candidate, candidateInfo]);

  const fetchAndSetCandidate = async () => {
    await dispatch(getCandidateSelfDetail());
  };

  useEffect(() => {
    fetchAndSetCandidate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return candidateDetail;
};
