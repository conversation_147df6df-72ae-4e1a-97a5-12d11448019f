import { setting<PERSON>pi } from "@src/apis/commonApis";
import { healthCheck<PERSON>pi } from "@src/apis/wildcardApis";
import { BASE_DOMAIN } from "@src/constants";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";

/**
 * Extracts the subdomain and domain from the request headers and constructs a redirect URL.
 * Provides flexibility for handling subdomain routing, redirection, and validation based on options.
 * @param req The request object containing headers.
 * @param options An optional object specifying options like "www", "admin", "wildcard", or "wildcardAdmin".
 * @returns An object with properties: subdomain, domain, redirectUrl, validate.
 */
export const extractSubdomainAndDomain = async (
  req: any,
  options: {
    www?: boolean;
    admin?: boolean;
    wildcard?: boolean;
    wildcardAdmin?: boolean;
    wildcardCandidate?: boolean;
  } = {},
  page_key?: string,
  checkWildcard?: boolean,
): Promise<{
  subdomain: string | null;
  domain: string;
  redirectUrl: string;
  validate: boolean;
  allowedRoles: string[];
  pagePermissions: PagePermissionInterface;
  pageDetail: KeyPairInterface;
}> => {
  const host = req.headers.host || "";
  const {
    www = false,
    admin = false,
    wildcard = false,
    wildcardAdmin = false,
    wildcardCandidate = false,
  } = options;
  const parts = host.split(".");
  let subdomain: string | null = parts[0];
  let domain = host;
  let validate = true;
  let redirect_page = "/";
  let pagePermissions: PagePermissionInterface = {
    "Super Admin": ["read", "write", "edit", "delete"],
  };
  const minimumLength = BASE_DOMAIN.split(".").length;
  let pageDetail = {};

  if (parts.length == minimumLength) {
    subdomain = "www";
  }
  let wildcardSubdomainCheck = false;
  let allowedRoles = ["Super Admin"];

  // Ensure only one option is enabled
  if (
    (www ? 1 : 0) +
      (admin ? 1 : 0) +
      (wildcard ? 1 : 0) +
      (wildcardAdmin ? 1 : 0) +
      (wildcardCandidate ? 1 : 0) !==
    1
  ) {
    throw new Error("Exactly one of www, admin, or wildcard must be enabled.");
  }

  // Handle www option
  if (www) {
    if (parts[0] === "www" || parts.length <= minimumLength) {
      parts.shift();
      subdomain = "www";
      domain = parts.join(".");
    } else {
      validate = false;
    }
  }

  // Handle admin option
  if (admin) {
    if (parts[0] !== "admin" || parts.length <= minimumLength) {
      validate = false;
    } else {
      validate = true;
      subdomain = parts[0];
      domain = parts.slice(1).join(".");
    }
  }

  // Handle wildcard option
  if (wildcard || wildcardCandidate) {
    if (
      parts[0] === "admin" ||
      parts[0] === "www" ||
      parts.length <= minimumLength
    ) {
      validate = false;
    } else {
      validate = true;
      subdomain = parts[0];
      domain = parts.slice(1).join(".");
    }
  }

  if (wildcardAdmin) {
    if (parts[0] === "www" || parts.length <= minimumLength) {
      validate = false;
    } else {
      validate = true;
      subdomain = parts[0];
      domain = parts.slice(1).join(".");
    }
  }

  if (!(subdomain === null || subdomain === "www")) {
    redirect_page = wildcardCandidate
      ? "/candidates/auth/login"
      : "/auth/login";
    wildcardSubdomainCheck =
      subdomain != "admin" &&
      req.url.startsWith(wildcardCandidate ? "/candidates/auth" : "/auth");
    if (validate && page_key) {
      const { success, ...response } = await settingApi.getRoutesRoles(
        subdomain,
        {
          path: req.url,
          unique_key: `${subdomain == "admin" ? "admin" : "employee"}_${page_key ?? ""}`,
        },
      );
      if (success) {
        const { roles, roles_permission, description, singular_name, title } =
          response.data;
        pagePermissions = { ...pagePermissions, ...roles_permission };
        allowedRoles = [...allowedRoles, ...roles];
        pageDetail = {
          title: title,
          singular_name: singular_name,
          description: description,
        };
      }
    }

    if (wildcardCandidate || wildcardSubdomainCheck) {
      const { success } = await healthCheckApi.checkBusinessExist(subdomain);
      // if (!success) {
      //   redirect_page = "/";
      //   subdomain = "www";
      //   validate = false;
      // }
    }
  }

  if (!validate && checkWildcard && subdomain && subdomain !== "www") {
    if (subdomain == "admin") {
      redirect_page = "/admin/dashboard";
    } else {
      const { success } = await healthCheckApi.checkBusinessExist(subdomain);
      // if (success) {
      //   redirect_page = wildcardCandidate
      //     ? "/candidates/dashboard"
      //     : "/admin/dashboard";
      // } else {
      //   redirect_page = "/";
      //   subdomain = "www";
      // }
    }
  }

  // Construct the redirect URL
  const protocol = (req.headers["x-forwarded-proto"] || "http") as string;
  const redirectUrl = `${protocol}://${subdomain}.${BASE_DOMAIN ?? host}${redirect_page}`;

  return {
    subdomain,
    domain,
    redirectUrl,
    validate,
    allowedRoles,
    pagePermissions,
    pageDetail,
  };
};

/**
 * Builds a subdomain link using the provided subdomain and path.
 * Constructs the link using the base domain defined in the BASE_DOMAIN constant.
 * @param subdomain The subdomain to include in the link.
 * @param path The path to append to the subdomain link.
 * @returns The constructed subdomain link.
 */
export const buildSubdomainLink = (subdomain: string, path: string): string => {
  return `https://${subdomain}.${BASE_DOMAIN}${path}`;
};
