import React, { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";

type AudioRecorderType = {
  title?: string;
  setText: (text: string) => void;
  uniqueKey: number | string;
};

export const AudioRecorder: React.FC<AudioRecorderType> = ({
  uniqueKey,
  setText,
  title = "",
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const recognition = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    setIsRecording(false);

    // Cleanup on unmount or when uniqueKey changes
    return () => {
      if (recognition.current) {
        recognition.current.stop();
        recognition.current = null;
      }
    };
  }, [uniqueKey]);

  const startRecording = () => {
    if (!("webkitSpeechRecognition" in window)) {
      alert(
        "Web Speech API is not supported in this browser. Use Chrome or Edge.",
      );
      return;
    }

    const SpeechRecognition =
      window.SpeechRecognition || (window as any).webkitSpeechRecognition;
    recognition.current = new SpeechRecognition();

    recognition.current.maxAlternatives = 5;
    recognition.current.continuous = true;
    recognition.current.interimResults = true;
    recognition.current.lang = "en-US";

    recognition.current.onstart = () => {
      setIsRecording(true);
    };

    recognition.current.onresult = (event: any) => {
      try {
        const results = Array.from(event.results);
        const transcript = results
          .map((result: any) => result[0].transcript)
          .join("");
        setText(transcript);
      } catch (error: any) {
        console.log(error, "error");
      }
    };

    recognition.current.onerror = (event: any) => {
      console.error("Speech recognition error:", event.error);
      setIsRecording(false);
    };

    recognition.current.onend = () => {
      setIsRecording(false);
    };

    recognition.current.start();
  };

  const stopRecording = () => {
    setIsRecording(false);
    if (recognition.current) {
      recognition.current.stop();
      recognition.current = null;
    }
  };

  return (
    <Button
      onClick={isRecording ? stopRecording : startRecording}
      variant="audio-answer">
      {isRecording ? "Stop" : "Start"} {title}
    </Button>
  );
};
