import React from "react";
import Link from "next/link";
import { Dropdown, DropdownItemProps } from "react-bootstrap";
import { Switch, SwitchProps, Tooltip } from "antd";

type DropDownLink = {
  linkHref: string;
  disabled?: boolean;
  hidden?: boolean;
  children: React.ReactNode;
  tooltipTitle?: string;
  target?: string;
} & DropdownItemProps;

export const DropdownLink: React.FC<DropDownLink> = ({
  hidden = false,
  disabled = false,
  linkHref,
  children,
  tooltipTitle,
  ...props
}) => {
  if (hidden) {
    return <></>;
  }

  const BaseComponent = () => (
    <Dropdown.Item
      role="Link"
      href={linkHref}
      className={`no-decoration ${disabled ? "disabled" : ""}`}
      disabled={disabled}
      {...props}>
      {children}
    </Dropdown.Item>
  );

  if (disabled && tooltipTitle) {
    return (
      <Tooltip placement="top" title={tooltipTitle} trigger={"hover"}>
        <>
          <BaseComponent />
        </>
      </Tooltip>
    );
  } else if (disabled) {
    return <BaseComponent />;
  }

  return (
    <Link
      href={linkHref}
      shallow
      target={props.target}
      className={`no-decoration ${disabled ? "disabled" : ""}`}>
      <BaseComponent />
    </Link>
  );
};

type SwitchWithTooltipProps = {
  tooltipTitle?: string;
  hidden?: boolean;
  htmlToolTip?: boolean;
} & SwitchProps;

export const SwitchWithTooltip: React.FC<SwitchWithTooltipProps> = ({
  checked,
  onChange,
  disabled = false,
  tooltipTitle,
  hidden = false,
  htmlToolTip = false,
  ...props
}) => {
  if (hidden) {
    return <></>;
  }

  const BaseComponent = () => (
    <Switch
      checked={checked}
      onChange={onChange}
      disabled={disabled}
      {...props}
    />
  );

  if (disabled && tooltipTitle) {
    return (
      <Tooltip
        placement="top"
        title={
          htmlToolTip ? (
            <div dangerouslySetInnerHTML={{ __html: tooltipTitle }} />
          ) : (
            tooltipTitle
          )
        }
        trigger={"hover"}>
        <>
          <BaseComponent />
        </>
      </Tooltip>
    );
  }

  return <BaseComponent />;
};
