export async function fetchGoogleDriveFile(
  fileId: string,
  fileName: string,
  mimeType: string,
  authToken: string,
) {
  let exportUrl = `https://www.googleapis.com/drive/v3/files/${fileId}/export?mimeType=application/pdf`;
  if (mimeType.includes("pdf")) {
    exportUrl = `https://www.googleapis.com/drive/v3/files/${fileId}?alt=media`;
  }

  const response = await fetch(exportUrl, {
    headers: {
      Authorization: `Bearer ${authToken}`,
    },
  });

  const blob = await response.blob();
  let finalFileName = fileName;
  if (!fileName.includes(".")) {
    finalFileName = `${fileName}.pdf`;
  }
  return new File([blob], finalFileName, {
    type: blob.type,
  });
}
