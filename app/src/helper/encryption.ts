import * as crypto from "crypto-js";

const key = process.env.NEXT_PUBLIC_KEY;

export const encrypt = (data: string) => {
  if (key !== undefined) {
    let encrypted_data = crypto.AES.encrypt(data, key).toString();
    encrypted_data = encrypted_data.replace(/\//g, "_");
    return encrypted_data;
  }
  return 0;
};

export const decrypt = (data: string) => {
  if (key !== undefined && data !== undefined) {
    try {
      const sanitized_data = data.replace(/_/g, "/");
      const decrypted_data = crypto.AES.decrypt(sanitized_data, key).toString(
        crypto.enc.Utf8,
      );
      if (decrypted_data.length == 0) {
        return 0;
      }
      return parseInt(decrypted_data);
    } catch {
      return 0;
    }
  }
  return 0;
};

export const encryptString = (data: string): string => {
  if (key !== undefined) {
    let encrypted_data = crypto.AES.encrypt(data, key).toString();
    encrypted_data = encrypted_data.replace(/\//g, "_");
    return encrypted_data;
  }
  return "";
};

export const decryptString = (data: string): string => {
  if (key !== undefined && data !== undefined) {
    try {
      const sanitized_data = data.replace(/_/g, "/");
      const decrypted_data = crypto.AES.decrypt(sanitized_data, key).toString(
        crypto.enc.Utf8,
      );
      if (decrypted_data.length == 0) {
        return "";
      }
      return String(decrypted_data);
    } catch {
      return "";
    }
  }
  return "";
};
