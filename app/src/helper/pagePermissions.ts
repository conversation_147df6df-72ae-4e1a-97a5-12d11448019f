import { useState, useEffect } from "react";
import {
  AuthEmployeeInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { settingApi } from "@src/apis/commonApis";

type VerifyEmployeePermissionsProps = {
  employee: AuthEmployeeInterface;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};

/**
 * Verifies if an employee has the required permissions to access a page.
 *
 * @param {VerifyEmployeePermissionsProps} props - The properties required for permission verification.
 * @param {AuthEmployeeInterface} props.employee - The employee whose permissions are being verified.
 * @param {string[]} [props.allowedRoles] - The roles that are allowed to access the page.
 * @param {PagePermissionInterface} [props.pagePermissions] - The permissions for each role.
 * @param {string[]} [props.requiredPermission] - The permissions required to access the page.
 * @returns {boolean} - Returns true if the employee has the required permissions, false otherwise.
 */
export const VerifyEmployeePermissions = ({
  employee,
  allowedRoles = [],
  pagePermissions = {},
  requiredPermission = [],
}: VerifyEmployeePermissionsProps): boolean => {
  if (employee && allowedRoles.length > 0) {
    const employeeRole = employee.employee_role ?? "";
    // Check if the employee's role is in the allowed roles
    if (!allowedRoles.includes(employeeRole)) {
      return false;
    }

    // Get the permissions for the employee's role
    const permissions = pagePermissions[employeeRole] ?? [];
    // Check if the employee has all the required permissions
    const hasPermissions = requiredPermission.every((value: string) =>
      permissions.includes(value),
    );

    if (!hasPermissions) {
      return false;
    }

    return true;
  }
  return true;
};

type GetEmployeePermissionsProps = {
  pagePermissions?: PagePermissionInterface;
};

/**
 * A custom React hook that returns the permissions for the current employee.
 * Returns default permissions if the employee is a "Super Admin" or if the role is not found.
 * @returns The permissions for the current employee's role.
 */
export const useEmployeePagePermissions = ({
  pagePermissions = {},
}: GetEmployeePermissionsProps): string[] => {
  const [permissions, setPermissions] = useState<string[]>([]);
  const employee = useSelector((state: RootState) => state.auth.employee);

  useEffect(() => {
    if (employee) {
      const employeeRole = employee.employee_role ?? "";
      const defaultPermissions = ["read", "write", "edit", "delete"];

      // Determine permissions based on employee role
      if (employeeRole === "Super Admin") {
        setPermissions(defaultPermissions);
      } else {
        const rolePermissions =
          pagePermissions[employeeRole] ?? defaultPermissions;
        setPermissions(rolePermissions);
      }
    } else {
      // Handle the case where there is no employee data
      setPermissions([]);
    }
  }, [employee, pagePermissions]);

  return permissions;
};

/**
 * A custom React hook that returns the permissions for the current employee.
 * Returns default permissions if the employee is a "Super Admin" or if the role is not found.
 * @returns The permissions for the current employee's role.
 */
export const useEmployeeSelectedPagePermissions = (
  pageKey: string,
): string[] => {
  const [permissions, setPermissions] = useState<string[]>([]);
  const employee = useSelector((state: RootState) => state.auth.employee);
  const fetchAndSetPermissions = async () => {
    if (employee) {
      const employeeRole = employee.employee_role ?? "";
      const defaultPermissions = ["read", "write", "edit", "delete"];

      // Determine permissions based on employee role
      if (employeeRole === "Super Admin") {
        setPermissions(defaultPermissions);
      } else {
        const { success, ...response } = await settingApi.getPagePermissions(
          false,
          { unique_key: `employee_${pageKey ?? ""}` },
        );
        if (success) {
          setPermissions(response.data);
        } else {
          setPermissions([]);
        }
      }
    } else {
      // Handle the case where there is no employee data
      setPermissions([]);
    }
  };

  useEffect(() => {
    fetchAndSetPermissions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [employee?.id]);

  return permissions;
};
