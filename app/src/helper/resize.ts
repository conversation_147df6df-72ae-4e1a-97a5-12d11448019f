import { useState, useEffect } from "react";

/**
 * A custom React hook that returns the current window width and updates it on resize.
 * @returns The current width of the window.
 */
const useWindowWidth = () => {
  const [width, setWidth] = useState(window.innerWidth);

  useEffect(() => {
    // Function to update the width state when the window is resized
    const handleResize = () => setWidth(window.innerWidth);

    // Add event listener for window resize
    window.addEventListener("resize", handleResize);

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []); // Empty dependency array ensures the effect runs only once after the initial render

  return width;
};

export default useWindowWidth;
