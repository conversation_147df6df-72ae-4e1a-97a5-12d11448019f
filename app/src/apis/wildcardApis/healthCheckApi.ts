import { API } from "../api";

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to check business exist or not
const checkBusinessExist = (subdomain: string) => {
  return API.post(`/api/v1/${subdomain}/health_check`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  checkBusinessExist,
};
