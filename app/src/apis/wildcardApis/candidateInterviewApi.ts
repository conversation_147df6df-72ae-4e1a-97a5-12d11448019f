import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { CANDIDATE_INTERVIEW } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getInterviewsList = (params: any) => {
  return API.get(CANDIDATE_INTERVIEW, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const getInterviewDetails = (id: number) => {
  return API.get(`${CANDIDATE_INTERVIEW}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getInterviewCandidateDetail = (id: number) => {
  return API.get(`${CANDIDATE_INTERVIEW}/${id}/candidate_and_job_detail`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateInterviewStatus = (id: number, data: any) => {
  return API.put(`${CANDIDATE_INTERVIEW}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to add new round
const addNewRound = (id: number, data: any) => {
  return API.post(`${CANDIDATE_INTERVIEW}/${id}/new_round`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getInterviewReviewDetail = (id: number, interview_id: number) => {
  return API.get(`${CANDIDATE_INTERVIEW}/${id}/review/${interview_id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getInterviewsList,
  updateInterviewStatus,
  getInterviewDetails,
  addNewRound,
  getInterviewCandidateDetail,
  getInterviewReviewDetail,
};
