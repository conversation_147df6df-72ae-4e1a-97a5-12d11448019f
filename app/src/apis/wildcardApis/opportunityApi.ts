import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { OPPORTUNITY } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getOpportunitiesList = (params: any) => {
  return API.get(OPPORTUNITY, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new business
const createOpportunity = (params: any) => {
  return API.post(OPPORTUNITY, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get department detail
const getOpportunityDetail = (id: number) => {
  return API.get(`${OPPORTUNITY}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateOpportunity = (id: number, data: any) => {
  return API.put(`${OPPORTUNITY}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateOpportunityStatus = (id: number, data: any) => {
  return API.put(`${OPPORTUNITY}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get Opportunity options of a business
const getOpportunityOptions = (data: any) => {
  return API.get(`${OPPORTUNITY}/options/all`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get email template
const getEmailTemplate = (id: number) => {
  return API.get(`${OPPORTUNITY}/${id}/email/template`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get shortlisted candidates
const shortlistedCandidates = (id: number, params?: any) => {
  return API.get(`${OPPORTUNITY}/${id}/shorlisted/candidates`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to generate a job description
const generateJobDescription = (body?: any) => {
  return API.post(`${OPPORTUNITY}/generate/job_description`, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get AI driven candidate:
const getAIDrivenCandidate = (id: number) => {
  return API.get(`${OPPORTUNITY}/${id}/top/candidate`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getOpportunitiesList,
  createOpportunity,
  updateOpportunity,
  updateOpportunityStatus,
  getOpportunityDetail,
  getOpportunityOptions,
  getEmailTemplate,
  shortlistedCandidates,
  generateJobDescription,
  getAIDrivenCandidate,
};
