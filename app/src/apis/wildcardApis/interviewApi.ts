import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { INTERVIEW } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getInterviewsList = (params: any) => {
  return API.get(INTERVIEW, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new business
const createInterview = (params: any) => {
  return API.post(INTERVIEW, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get department detail
const getInterviewDetail = (id: number) => {
  return API.get(`${INTERVIEW}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateInterview = (id: number, data: any) => {
  return API.put(`${INTERVIEW}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateInterviewStatus = (id: number, data: any) => {
  return API.put(`${INTERVIEW}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get the list of businesses
const getInterviewHistoryList = (id: number, params: any) => {
  return API.get(`${INTERVIEW}/${id}/histories`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get feedbacks
const getFeedbacks = (id: number, params: any) => {
  return API.get(`${INTERVIEW}/${id}/feedback`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to save a feedback
const saveFeedback = (id: number, data: any) => {
  return API.post(`${INTERVIEW}/${id}/feedback`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getInterviewsList,
  createInterview,
  updateInterview,
  updateInterviewStatus,
  getInterviewDetail,
  getInterviewHistoryList,
  getFeedbacks,
  saveFeedback,
};
