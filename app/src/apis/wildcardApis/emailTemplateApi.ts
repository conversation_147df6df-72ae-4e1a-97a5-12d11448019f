import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { EMAIL_TEMPLATE } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getEmailTemplateList = (params: any) => {
  return API.get(EMAIL_TEMPLATE, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new business
const createEmailTemplate = (params: any) => {
  return API.post(EMAIL_TEMPLATE, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get emailTemplate detail
const getEmailTemplateDetail = (id: number) => {
  return API.get(`${EMAIL_TEMPLATE}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateEmailTemplate = (id: number, data: any) => {
  return API.put(`${EMAIL_TEMPLATE}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateEmailTemplateStatus = (id: number, data: any) => {
  return API.put(`${EMAIL_TEMPLATE}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get emailTemplates options of a business
const getEmailTemplateOptions = (data: any) => {
  return API.get(`${EMAIL_TEMPLATE}/options/all`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getEmailTemplateList,
  createEmailTemplate,
  updateEmailTemplate,
  updateEmailTemplateStatus,
  getEmailTemplateDetail,
  getEmailTemplateOptions,
};
