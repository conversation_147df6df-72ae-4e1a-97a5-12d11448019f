import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { CANDIDATE_DOCUMENT } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

const UploadCandidateDocument = (data: any) => {
  return API.post(`${CANDIDATE_DOCUMENT}/uploads`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const ValidateCandidateDocumentLink = (subdomain: string, data: any) => {
  return API.post(
    `${CANDIDATE_DOCUMENT.replace("wildcard", subdomain)}/validate_link`,
    data,
  )
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  UploadCandidateDocument,
  ValidateCandidateDocumentLink,
};
