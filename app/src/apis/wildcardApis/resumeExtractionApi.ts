import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { RESUME_EXTRACTION } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// function to upload resume and extract detail
const EmployeeResumeExtraction = (
  data: any,
  onUploadProgress: (progress: any) => void,
) => {
  return API.post(`${RESUME_EXTRACTION}/upload`, data, onUploadProgress)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// function to save duplicate record
const saveDuplicateRecord = (data: any) => {
  return API.post(`${RESUME_EXTRACTION}/save_records`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const saveCandidateFromResume = (id: number) => {
  return API.post(`${RESUME_EXTRACTION}/save_candidate/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  EmployeeResumeExtraction,
  saveDuplicateRecord,
  saveCandidateFromResume,
};
