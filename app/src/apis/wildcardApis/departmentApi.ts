import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { DEPARTMENT } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getCategoriesList = (params: any) => {
  return API.get(DEPARTMENT, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new business
const createDepartment = (params: any) => {
  return API.post(DEPARTMENT, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get department detail
const getDepartmentDetail = (id: number) => {
  return API.get(`${DEPARTMENT}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateDepartment = (id: number, data: any) => {
  return API.put(`${DEPARTMENT}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const updateDepartmentStatus = (id: number, data: any) => {
  return API.put(`${DEPARTMENT}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get departments options of a business
const getCategoriesOptions = (data: any) => {
  return API.get(`${DEPARTMENT}/options/all`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getCategoriesList,
  createDepartment,
  updateDepartment,
  updateDepartmentStatus,
  getDepartmentDetail,
  getCategoriesOptions,
};
