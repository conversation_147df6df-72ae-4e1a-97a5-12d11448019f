import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const {
  LOGIN,
  VERIFY_ME,
  LOG_OUT,
  FORGOT_PASSWORD,
  CHANGE_PASSWORD,
  VERIFY_FORGOT_PASSWORD_OTP,
  RESEND_FORGOT_PASSWORD_OTP,
} = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

const EmployeeLoginApi = (data: any) => {
  return API.post(LOGIN, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeForgotPasswordApi = (data: any) => {
  return API.post(FORGOT_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeResetPasswordApi = (data: any) => {
  return API.post(CHANGE_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeVerifyMe = () => {
  return API.post(VERIFY_ME)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeVerifyForgotOtp = (data: any) => {
  return API.post(VERIFY_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeResendForgotPasswordOtp = (data: any) => {
  return API.post(RESEND_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const EmployeeLogOutUser = () => {
  return API.delete(`${LOG_OUT}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  EmployeeLoginApi,
  EmployeeLogOutUser,
  EmployeeVerifyMe,
  EmployeeForgotPasswordApi,
  EmployeeVerifyForgotOtp,
  EmployeeResendForgotPasswordOtp,
  EmployeeResetPasswordApi,
};
