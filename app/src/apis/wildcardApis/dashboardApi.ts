import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { DASHBOARD } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch wildcard dashboard details from the API
const dashboardDetails = () => {
  return API.get(DASHBOARD)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const dashboardJobStats = () => {
  return API.get(`${DASHBOARD}/top_jobs`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to fetch dashboard stats from the API
const dashboardStats = (params: any) => {
  return API.get(`${DASHBOARD}/stats`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to fetch dashboard stats from the API
const apiKeyUsageStats = (params?: any) => {
  return API.get(`${DASHBOARD}/api-usage-stats`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  dashboardDetails,
  dashboardStats,
  dashboardJobStats,
  apiKeyUsageStats,
};
