import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { JOB_REQUEST } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of job requestes
const getJobRequestList = (params: any) => {
  return API.get(JOB_REQUEST, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new job request
const createJobRequest = (params: any) => {
  return API.post(JOB_REQUEST, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get job request detail
const getJobRequestDetail = (id: number) => {
  return API.get(`${JOB_REQUEST}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a job request
const updateJobRequest = (id: number, data: any) => {
  return API.put(`${JOB_REQUEST}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a job request
const updateJobRequestStatus = (id: number, data: any) => {
  return API.put(`${JOB_REQUEST}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getJobRequestList,
  createJobRequest,
  updateJobRequest,
  updateJobRequestStatus,
  getJobRequestDetail,
};
