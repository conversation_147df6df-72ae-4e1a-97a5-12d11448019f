import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { JOBS } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const getJobsList = (params: any) => {
  return API.get(JOBS, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// function to apply job request
const applyJobRequest = (job_id: number, params: any) => {
  return API.post(`${JOBS}/${job_id}/apply`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getJobsList,
  applyJobRequest,
};
