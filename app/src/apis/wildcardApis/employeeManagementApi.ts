import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { EMPLOYEES, EMPLOYEE_ROLES } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

const getEmployeesRoles = (params?: any) => {
  return API.get(EMPLOYEE_ROLES, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const createEmployeeRole = (body?: any) => {
  return API.post(EMPLOYEE_ROLES, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getEmployeesRolesOption = (params?: any) => {
  return API.get(`${EMPLOYEE_ROLES}/options/all`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getEmployeesList = (params: any) => {
  return API.get(EMPLOYEES, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getEmployeeDetail = (id: number) => {
  return API.get(`${EMPLOYEES}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const createEmployee = (params: any) => {
  return API.post(EMPLOYEES, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const updateEmployeeStatus = (id: number, data: any) => {
  return API.put(`${EMPLOYEES}/${id}/status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const updateEmployee = (id: number, data: any) => {
  return API.put(`${EMPLOYEES}/${id}`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const deleteEmployee = (id: number) => {
  return API.delete(`${EMPLOYEES}/${id}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get employees options of a business
const getEmployeesOptions = (data: any) => {
  return API.get(`${EMPLOYEES}/options/all`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to get employees options of a business
const checkEmployeeDependency = (id: number) => {
  return API.get(`${EMPLOYEES}/${id}/dependency`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getEmployeesRoles,
  createEmployeeRole,
  getEmployeesRolesOption,
  getEmployeesList,
  getEmployeeDetail,
  createEmployee,
  updateEmployeeStatus,
  updateEmployee,
  getEmployeesOptions,
  deleteEmployee,
  checkEmployeeDependency,
};
