import { HOMEPAGE_APIS } from "../ApiConstant";
import { API } from "../api";

const { CONTACT_US_ENQUERY } = HOMEPAGE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// contact us enqueries
const ContactEnqueryApi = (data: any) => {
  return API.post(CONTACT_US_ENQUERY, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  ContactEnqueryApi,
};
