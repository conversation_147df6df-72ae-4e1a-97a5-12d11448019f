import { WILDCARD_APIS } from "../ApiConstant";
import { API } from "../api";

const { BUSINESS } = WILDCARD_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of candidates
const getBusinessesList = (params?: any) => {
  return API.get(BUSINESS, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const wildcardLogin = (id: number) => {
  return API.post(`${BUSINESS}/${id}/portal_login`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const currentBusinessInfo = () => {
  return API.get(`${BUSINESS}/detail`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getBusinessesList,
  wildcardLogin,
  currentBusinessInfo,
};
