import { CANDIDATE_APIS } from "../ApiConstant";
import { API } from "../api";

const { OPPORTUNITY } = CANDIDATE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch wildcard dashboard details from the API
const getJobsList = (params?: any) => {
  return API.get(OPPORTUNITY, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getJobsList,
};
