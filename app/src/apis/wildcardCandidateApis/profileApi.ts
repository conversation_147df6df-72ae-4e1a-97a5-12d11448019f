import { CANDIDATE_APIS } from "../ApiConstant";
import { API } from "../api";

const { PROFILE } = CANDIDATE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch self detail
const getSelfDetail = () => {
  return API.get(`${PROFILE}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getFeedbacks = (id: number, params?: any) => {
  return API.get(`${PROFILE}/feedbacks`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getDocuments = (id: number, params?: any) => {
  return API.get(`${PROFILE}/documents`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const getProfileComments = (id: number, params?: any) => {
  return API.get(`${PROFILE}/comments`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getSelfDetail,
  getFeedbacks,
  getDocuments,
  getProfileComments,
};
