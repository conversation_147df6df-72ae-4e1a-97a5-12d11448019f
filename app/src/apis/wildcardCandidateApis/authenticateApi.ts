import { CANDIDATE_APIS } from "../ApiConstant";
import { API } from "../api";

const {
  LOGIN,
  VERIFY_ME,
  LOG_OUT,
  FORGOT_PASSWORD,
  CHANGE_PASSWORD,
  VERIFY_FORGOT_PASSWORD_OTP,
  RESEND_FORGOT_PASSWORD_OTP,
  UPDATE_PASSWORD,
} = CANDIDATE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

const CandidateLoginApi = (data: any) => {
  return API.post(LOGIN, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateVerifyMe = () => {
  return API.post(VERIFY_ME)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateLogOutUser = () => {
  return API.delete(`${LOG_OUT}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateForgotPasswordApi = (data: any) => {
  return API.post(FORGOT_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateResetPasswordApi = (data: any) => {
  return API.post(CHANGE_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateVerifyForgotOtp = (data: any) => {
  return API.post(VERIFY_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const CandidateResendForgotPasswordOtp = (data: any) => {
  return API.post(RESEND_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

/**
 * Updates the password for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is for an admin or a wildcard user.
 * @param {UpdatePasswordBody} [body] - The parameters to be sent in the API request, including old and new passwords.
 * @returns {Promise<SuccessResponse>} - A promise that resolves with the API response data in the form of `SuccessResponse` or an error response.
 */
const updatePassword = (body?: any) => {
  return API.put(UPDATE_PASSWORD, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  CandidateLoginApi,
  CandidateVerifyMe,
  CandidateLogOutUser,
  CandidateForgotPasswordApi,
  CandidateResetPasswordApi,
  CandidateVerifyForgotOtp,
  CandidateResendForgotPasswordOtp,
  updatePassword,
};
