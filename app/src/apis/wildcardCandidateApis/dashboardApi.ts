import { CANDIDATE_APIS } from "../ApiConstant";
import { API } from "../api";

const { DASHBOARD } = CANDIDATE_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch wildcard dashboard details from the API
const dashboardDetails = () => {
  return API.get(DASHBOARD)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to fetch wildcard dashboard details from the API
const dashboardActiveJob = () => {
  return API.get(`${DASHBOARD}/active_job`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to fetch wildcard dashboard details from the API
const activeJobInterviewStats = () => {
  return API.get(`${DASHBOARD}/active_job/interviews`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  dashboardDetails,
  dashboardActiveJob,
  activeJobInterviewStats,
};
