import { WILDCARD_APIS, ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

/**
 * Handles successful API responses by returning the data.
 * @param {any} data - The data from the API response.
 * @returns {any} - The data from the API response.
 */
const ReturnSuccess = (data: any) => data;

/**
 * Handles API errors by returning an error object.
 * @param {any} error - The error from the API response.
 * @returns {Object} - An object with a success field set to false, an error message, and an array of errors.
 */
const ReturnError = (error: any) => ({
  success: false,
  message: error.message,
  errors: error.errors ?? [],
});

/**
 * Updates the password for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is for an admin or a wildcard user.
 * @param {UpdatePasswordBody} [body] - The parameters to be sent in the API request, including old and new passwords.
 * @returns {Promise<SuccessResponse>} - A promise that resolves with the API response data in the form of `SuccessResponse` or an error response.
 */
const updatePassword = (admin: boolean, body?: any) => {
  const url = `${admin ? ADMIN_APIS.UPDATE_PASSWORD : WILDCARD_APIS.UPDATE_PASSWORD}`;
  return API.put(url, body)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  updatePassword,
};
