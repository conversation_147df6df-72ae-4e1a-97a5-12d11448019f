import { WILDCARD_APIS, ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

/**
 * Handles successful API responses by returning the data.
 * @param {any} data - The data from the API response.
 * @returns {any} - The data from the API response.
 */
const ReturnSuccess = (data: any) => data;

/**
 * Handles API errors by returning an error object.
 * @param {any} error - The error from the API response.
 * @returns {Object} - An object with a success field set to false, an error message, and an array of errors.
 */
const ReturnError = (error: any) => ({
  success: false,
  message: error.message,
  errors: error.errors ?? [],
});

/**
 * Retrieves the permissions list for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is from admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */
const getPermissionsList = (admin: boolean, params?: any) => {
  const url = `${admin ? ADMIN_APIS.SETTINGS : WILDCARD_APIS.SETTINGS}/permissions`;
  return API.get(url, params)
    .then((response) => ReturnSuccess(response.data))
    .catch((error) => ReturnError(error));
};

/**
 * Retrieves the nodes list for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is from admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */
const getNodesList = (admin: boolean, params?: any) => {
  const url = `${admin ? ADMIN_APIS.SETTINGS : WILDCARD_APIS.SETTINGS}/nodes`;
  return API.get(url, params)
    .then((response) => ReturnSuccess(response.data))
    .catch((error) => ReturnError(error));
};

/**
 * Retrieves the nodes list for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is from admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */
const updateNodesPermissions = (admin: boolean, id: number, params?: any) => {
  const url = `${admin ? ADMIN_APIS.SETTINGS : WILDCARD_APIS.SETTINGS}/nodes/${id}`;
  return API.put(url, params)
    .then((response) => ReturnSuccess(response.data))
    .catch((error) => ReturnError(error));
};

/**
 * Retrieves the nodes list for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is from admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */
const updateBulkNodePermissions = (admin: boolean, params?: any) => {
  const url = `${admin ? ADMIN_APIS.SETTINGS : WILDCARD_APIS.SETTINGS}/nodes/bulk_update`;
  return API.post(url, params)
    .then((response) => ReturnSuccess(response.data))
    .catch((error) => ReturnError(error));
};

/**
 * Retrieves the nodes list for either admin or wildcard.
 * @param {string} subdomain - subdomain name.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */

// Function to create a new business
const getRoutesRoles = (subdomain: string, params: any) => {
  return API.post(`/api/v1/${subdomain}/settings/nodes/detail`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

/**
 * Retrieves the permissions list for either admin or wildcard.
 * @param {boolean} admin - Indicates whether the request is from admin.
 * @param {any} body - The parameters to be sent in the API request.
 * @returns {Promise<Object>} - A promise that resolves with the API response data or an error object.
 */
const getPagePermissions = (admin: boolean, body?: any) => {
  const url = `${admin ? ADMIN_APIS.SETTINGS : WILDCARD_APIS.SETTINGS}/permissions/selected_page`;
  return API.post(url, body)
    .then((response) => ReturnSuccess(response.data))
    .catch((error) => ReturnError(error));
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getPermissionsList,
  getNodesList,
  updateNodesPermissions,
  updateBulkNodePermissions,
  getRoutesRoles,
  getPagePermissions,
};
