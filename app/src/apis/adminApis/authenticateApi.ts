import { ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

const {
  LOGIN,
  REGISTER,
  VERIFY_ME,
  LOG_OUT,
  FORGOT_PASSWORD,
  CHANGE_PASSWORD,
  VERIFY_FORGOT_PASSWORD_OTP,
  RESEND_FORGOT_PASSWORD_OTP,
  RESEND_ACCOUNT_VERIFY_OTP,
  VERIFY_USER_ACCOUNT,
  BUSINESS_LOGIN,
} = ADMIN_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to handle admin login
const AdminLoginApi = (data: any) => {
  return API.post(LOGIN, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to handle admin registration
const AdminRegisterApi = (data: any) => {
  return API.post(REGISTER, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to handle admin forgot password
const AdminForgotPasswordApi = (data: any) => {
  return API.post(FORGOT_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to handle admin password reset
const AdminResetPasswordApi = (data: any) => {
  return API.post(CHANGE_PASSWORD, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to verify admin identity
const AdminVerifyMe = () => {
  return API.post(VERIFY_ME)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to verify OTP for forgot password
const AdminVerifyForgotOtp = (data: any) => {
  return API.post(VERIFY_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to resend OTP for forgot password
const AdminResendForgotPasswordOtp = (data: any) => {
  return API.post(RESEND_FORGOT_PASSWORD_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to resend OTP for account verification
const AdminResendAccountVerifyOtp = (data: any) => {
  return API.post(RESEND_ACCOUNT_VERIFY_OTP, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to verify user account
const AdminVerifyUserAccount = (data: any) => {
  return API.put(VERIFY_USER_ACCOUNT, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to log out user
const AdminLogOutUser = () => {
  return API.delete(LOG_OUT)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

/**
 * Function to verify the current user and set authentication token for a specific business subdomain.
 *
 * @param subdomain - The subdomain of the business to login.
 * @returns Promise with the success response or error.
 */
const AdminBusinessLogin = (subdomain: string) => {
  return API.post(`${BUSINESS_LOGIN}/${subdomain}`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  AdminLoginApi,
  AdminRegisterApi,
  AdminLogOutUser,
  AdminVerifyMe,
  AdminForgotPasswordApi,
  AdminVerifyForgotOtp,
  AdminResendForgotPasswordOtp,
  AdminResetPasswordApi,
  AdminResendAccountVerifyOtp,
  AdminVerifyUserAccount,
  AdminBusinessLogin,
};
