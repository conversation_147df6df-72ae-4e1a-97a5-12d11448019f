import { ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

const { BUSINESS } = ADMIN_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to get the list of businesses
const adminBusinessList = (params: any) => {
  return API.get(BUSINESS, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a new business
const adminCreateBusiness = (params: any) => {
  return API.post(BUSINESS, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update business detail
const adminUpdateBusiness = (id: number, params: any) => {
  return API.put(`${BUSINESS}/${id}`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to verify a business email
const adminVerifyBusinessEmail = (id: number, params: any) => {
  return API.post(`${BUSINESS}/${id}/verify-email`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to resend the business verification email
const adminResendBusinessEmail = (id: number) => {
  return API.put(`${BUSINESS}/${id}/resend-verify-email`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to update the status of a business
const adminUpdateBusinessStatus = (id: number, data: any) => {
  return API.put(`${BUSINESS}/${id}/update-status`, data)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const adminBusinessPayment = (id: number) => {
  return API.put(`${BUSINESS}/${id}/payment`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

const wildcardLogin = (id: number) => {
  return API.post(`${BUSINESS}/${id}/portal_login`)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  adminBusinessList,
  adminCreateBusiness,
  adminUpdateBusiness,
  adminVerifyBusinessEmail,
  adminResendBusinessEmail,
  adminUpdateBusinessStatus,
  adminBusinessPayment,
  wildcardLogin,
};
