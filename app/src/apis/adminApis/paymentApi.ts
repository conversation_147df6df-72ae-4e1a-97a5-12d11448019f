import { ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

const { PAYMENT } = ADMIN_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to create a payment intent
const getPlans = (params?: any) => {
  return API.get(`${PAYMENT}/plans`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a payment intent
const createSubscrption = (params?: any) => {
  return API.post(`${PAYMENT}/create-subscription`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a payment intent
const createPaymentIntent = (params: any) => {
  return API.post(`${PAYMENT}/create-payment-intent`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to create a payment intent
const buyPlan = (params: any) => {
  return API.post(`${PAYMENT}/buy-plan`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  getPlans,
  createPaymentIntent,
  createSubscrption,
  buyPlan,
};
