import { ADMIN_APIS } from "../ApiConstant";
import { API } from "../api";

const { DASHBOARD } = ADMIN_APIS;

const ReturnSuccess = (data: any) => data;

const ReturnError = (error: any) => {
  return {
    success: false,
    message: error.message,
    errors: error.errors ?? [],
  };
};

// Function to fetch admin dashboard stats from the API
const adminDashboardDetails = () => {
  return API.get(DASHBOARD)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// Function to fetch admin dashboard stats from the API
const adminDashboardStats = (params: any) => {
  return API.get(`${DASHBOARD}/stats`, params)
    .then((response) => response.data)
    .then(ReturnSuccess)
    .catch(ReturnError);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default {
  adminDashboardDetails,
  adminDashboardStats,
};
