import {
  FAQSection,
  FeatureSection,
  HowItWorkSection,
  IntroSection,
  KeyFeaturesSection,
  MainBannerSection,
} from "@src/components/PublicPage";
import { PublicLayout } from "@src/components/Layout";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function Home() {
  return (
    <>
      <MainBannerSection />
      {/* <PartnerSection /> */}
      <IntroSection />
      <FeatureSection />
      <KeyFeaturesSection />
      <HowItWorkSection />
      <FAQSection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(
    req,
    {
      www: true,
    },
    "",
    true,
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

Home.layout = PublicLayout;
