import { PublicLayout } from "@src/components/Layout";
import { PageTitle, PrivacyPolicySection } from "@src/components/PublicPage";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function PrivacyPolicy() {
  return (
    <>
      <PageTitle title="Privacy Policy" />
      <PrivacyPolicySection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

PrivacyPolicy.layout = PublicLayout;
