import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllJobRequestList, setLoader } from "@src/redux/actions";
import { WildcardJobRequestList } from "@src/components/WildCard/JobRequest";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";

interface JobRequestSearchParams {
  page: number;
  search?: string;
}

type JobRequestPageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: JobRequestSearchParams;
  pageDetail: KeyPairInterface;
};

export default function JobRequestPage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: JobRequestPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { currentPage, limit, rows } = useSelector(
    (state: RootState) => state.jobRequest,
  );

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      await dispatch(setLoader(true));
      dispatch(
        getAllJobRequestList({
          page: currentPage,
          limit: limit,
        }),
      );
      await dispatch(setLoader(false));
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  useEffect(() => {
    let queryParams: JobRequestSearchParams = { page: currentPage };
    router.push(
      { pathname: APP_ROUTE.JOB_REQUEST_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage]);

  return (
    <>
      <section className="jobRequest">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Job Request Management"}
          pageDescription={
            pageDetail?.description ?? "Job Request Management Description"
          }>
          <div className="box-content">
            <WildcardJobRequestList
              fetchData={fetchData}
              subdomain={subdomain}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(req, { wildcard: true }, "job_requests");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: JobRequestSearchParams = {
    page: 1,
  };

  if (query) {
    const { search } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

JobRequestPage.layout = PrivateLayout;
