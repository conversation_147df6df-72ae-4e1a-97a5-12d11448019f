import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getAllLocationList, openDialog, setLoader } from "@src/redux/actions";
import { Button } from "react-bootstrap";
import DialogComponents from "@src/components/DialogComponents";
import { WildcardLocationList } from "@src/components/WildCard/Location";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { locationApi } from "@src/apis/wildcardApis";

interface LocationSearchParams {
  page: number;
  search?: string;
}

type LocationPageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: LocationSearchParams;
  pageDetail: KeyPairInterface;
};

export default function LocationPage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: LocationPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { currentPage, limit, rows } = useSelector(
    (state: RootState) => state.location,
  );

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      try {
        await dispatch(setLoader(true));
        const response = await locationApi.getLocationList({
          page: currentPage,
          limit: limit,
        });
        if (response.success) {
          dispatch(getAllLocationList(response.data.rows));
        }
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching locations data:", err);
        await dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  // Open the modal to add a new location
  const openNewLocationModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ADD_NEW_LOCATION, // Use ADD_NEW_LOCATION
        options: {
          onLocationCreate: onLocationCreate,
        },
      }),
    );
  };

  // Handle location creation and refetch data
  const onLocationCreate = async () => {
    fetchData(currentPage, limit);
  };

  useEffect(() => {
    let queryParams: LocationSearchParams = { page: currentPage };
    router.push(
      { pathname: APP_ROUTE.LOCATION_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage]);

  const addNewButton = currentPagePermissions.includes("write") ? (
    <Button onClick={openNewLocationModal} className="btn btn-theme ms-auto">
      + Add Location
    </Button>
  ) : null;

  return (
    <>
      <section className="location">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Location Management"}
          pageDescription={
            pageDetail?.description ?? "Location Management Description"
          }
          buttonComponent={rows.length > 0 && addNewButton}>
          <div className="box-content">
            <WildcardLocationList
              fetchData={fetchData}
              subdomain={subdomain}
              addNewButton={addNewButton}
              locationPagePermssions={currentPagePermissions}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(req, { wildcard: true }, "locations");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: true,
      },
    };
  }

  let filters: LocationSearchParams = {
    page: 1,
  };

  if (query) {
    const { search } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
      filters: filters,
    },
  };
};

LocationPage.layout = PrivateLayout;
