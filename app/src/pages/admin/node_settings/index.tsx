import React from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { NodeSettingList } from "@src/components/NodeSetting";
import { KeyPairInterface } from "@src/redux/interfaces";

type SettingPageProps = {
  subdomain: string;
  pageDetail: KeyPairInterface;
};

export default function SettingPage({
  subdomain,
  pageDetail,
}: SettingPageProps) {
  return (
    <>
      <section className="node-settings">
        <NodeSettingList subdomain={subdomain} pageDetail={pageDetail} />
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "settings",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

SettingPage.layout = PrivateLayout;
