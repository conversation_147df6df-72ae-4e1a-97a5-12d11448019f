import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllAdminBusinessList,
  openDialog,
  setLoader,
  updateBusinessDetail,
} from "@src/redux/actions";
import { AdminBusinessList } from "@src/components/Admin";
import { Button } from "react-bootstrap";
import DialogComponents from "@src/components/DialogComponents";
import { BusinessInterface, KeyPairInterface } from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";

type BusinessPageProps = {
  subdomain: string;
  pageDetail: KeyPairInterface;
};

export default function BusinessPage({
  subdomain,
  pageDetail,
}: BusinessPageProps) {
  const dispatch = useAppDispatch();

  const currentUser = useSelector((state: RootState) => state.auth.admin);

  const { currentPage, limit } = useSelector(
    (state: RootState) => state.business,
  );

  const count = useSelector((state: RootState) => state.business.count);

  // Fetch the business data with pagination
  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllAdminBusinessList({ page: currentPage, limit: limit }),
        );
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching departments data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(currentPage ?? 1, limit ?? 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  // Open the modal to add a new business
  const openNewBusinessModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ADD_NEW_BUSINESS,
        options: {
          onBusinessCreate: (business: BusinessInterface) =>
            onBusinessCreate(business),
        },
      }),
    );
  };

  // Handle business creation and verification
  const onBusinessCreate = async (business: BusinessInterface) => {
    fetchData(currentPage, limit);
    verifyBusiness(business);
  };

  // Open the modal to verify a business
  const verifyBusiness = (business: BusinessInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.VERIFY_BUSINESS,
        options: {
          business: business,
          onOtpVerified: onOtpVerified,
        },
      }),
    );
  };

  // Handle OTP verification for a business
  const onOtpVerified = (business: BusinessInterface) => {
    dispatch(
      updateBusinessDetail({
        id: business.id,
        data: { ...business, email_verified: true },
      }),
    );
  };

  const addNewButton = (
    <Button
      onClick={openNewBusinessModal}
      className="btn btn-theme ms-auto mw-180px">
      + Add {pageDetail.singular_name ?? "Business"}
    </Button>
  );

  return (
    <>
      <section className="business">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Business Management"}
          pageDescription={
            pageDetail?.description ?? "Business Management Description"
          }
          buttonComponent={count > 0 && addNewButton}>
          <div className="box-content">
            {currentUser && (
              <AdminBusinessList
                fetchData={fetchData}
                currentUser={currentUser}
                addNewButton={addNewButton}
                verifyBusiness={verifyBusiness}
              />
            )}
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate, subdomain, pageDetail } =
    await extractSubdomainAndDomain(
      req,
      {
        admin: true,
      },
      "business",
    );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      pageDetail: pageDetail,
    },
  };
};

BusinessPage.layout = PrivateLayout;
