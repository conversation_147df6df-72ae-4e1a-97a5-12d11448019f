import React, { useEffect, useCallback, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Link from "next/link";
import { getCandidateDetail, setLoader } from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { ScheduleInterview } from "@src/components/WildCard";
import { decrypt } from "@src/helper/encryption";
import { candidateApi, opportunityApi } from "@src/apis/wildcardApis";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { OpportunityInterface } from "@src/redux/interfaces";

/**
 * Renders the Interview page for scheduling an interview for a candidate.
 *
 * @param {Object} props - The component props.
 * @param {string} props.subdomain - The subdomain of the user.
 * @param {string} props.encryptedId - The encrypted ID of the candidate.
 * @return {JSX.Element} The rendered Interview page.
 */

export default function CandidateInterview({
  subdomain,
  encryptedId,
  jobId = null,
}: any) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const id = decrypt(encryptedId);
  const candidate = useSelector(
    (state: RootState) => state.candidate.candidate,
  );
  const [opportunity, setOpportunity] = useState<OpportunityInterface | null>(
    null,
  );

  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");

  const fetchAndSetOpportunity = useCallback(async () => {
    if (jobId) {
      await dispatch(setLoader(true));
      const { success, ...response } =
        await opportunityApi.getOpportunityDetail(jobId);
      await dispatch(setLoader(false));
      if (success) {
        setOpportunity(response.data);
      } else {
        flashMessage(response.message, "error");
        router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
      }
    }
  }, [dispatch, jobId, router]);

  useEffect(() => {
    fetchAndSetOpportunity();
  }, [fetchAndSetOpportunity]);

  const fetchAndSetCandidate = useCallback(async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getCandidateDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
        }
      }),
    );
  }, [dispatch, id, router]);

  useEffect(() => {
    fetchAndSetCandidate();
  }, [fetchAndSetCandidate]);

  const handleScheduleSubmit = async (data: any) => {
    if (!candidate) {
      flashMessage("Candidate must exists", "error");
      return;
    }

    try {
      await dispatch(setLoader(true));
      const { success, ...response } =
        await candidateApi.createScheduleCandidateInterview(candidate.id, data);
      await dispatch(setLoader(false));
      flashMessage(response.message, success ? "success" : "error");
      if (success) {
        router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
      }
    } catch (error) {
      await dispatch(setLoader(false));
      flashMessage("Error scheduling interview", "error");
    }
  };

  useEffect(() => {
    if (candidate?.has_interview) {
      flashMessage("Interview has already been schedule/completed", "warning");
      router.back();
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [candidate]);

  const hasJobReadPermission = jobPermissions.includes("read");

  if (!candidate) {
    return null;
  }

  return (
    <section className="opportunity">
      <PageHeader
        pageTitle="Schedule Interview"
        buttonComponent={
          jobId && opportunity ? (
            <span className="ml-auto job-id">
              Job ID{" "}
              {hasJobReadPermission ? (
                <Link
                  target="_blank"
                  href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${jobId}`}>
                  #J0B-
                  {jobId.toString().padStart(4, "0")}
                </Link>
              ) : (
                `#J0B-${jobId.toString().padStart(4, "0")}`
              )}
            </span>
          ) : null
        }
        pageDescription="Efficiently coordinate and arrange interviews with candidates through an intuitive scheduling interface.">
        <div className="box-content">
          {
            <ScheduleInterview
              subdomain={subdomain}
              candidate={candidate}
              opportunity_id={jobId}
              onSubmit={handleScheduleSubmit}
            />
          }
        </div>
      </PageHeader>
    </section>
  );
}

export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain, allowedRoles, pagePermissions } =
    await extractSubdomainAndDomain(req, { wildcard: true }, "interviews");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const encryptedId = String(query.id);
  let jobId = null;
  if (query.job_id) {
    jobId = Number(decrypt(String(query.job_id)));
  }

  return {
    props: {
      encryptedId: encryptedId,
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      jobId: jobId,
      requiredPermission: ["read", "write"],
    },
  };
};

CandidateInterview.layout = PrivateLayout;
