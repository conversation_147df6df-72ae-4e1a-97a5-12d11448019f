import React, { useEffect, useRef } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  getCandidateDetail,
  setCandidateState,
  setLoader,
} from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import { PagePermissionInterface } from "@src/redux/interfaces";

type DepartmentPageProps = {
  encryptedId: string;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};
import { decrypt } from "@src/helper/encryption";
import { EditCandidateDetail } from "@src/components/WildCard";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { Button } from "antd";
import { candidateApi } from "@src/apis/wildcardApis";

export default function CandidatePage({
  pagePermissions,
  encryptedId,
  subdomain,
}: DepartmentPageProps) {
  const dispatch = useAppDispatch();
  const submitRef = useRef<HTMLButtonElement | null>(null);
  const router = useRouter();
  const id = decrypt(encryptedId);
  const candidate = useSelector(
    (state: RootState) => state.candidate.candidate,
  );
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const currentEmployeeRole: string = currentEmployee?.employee_role ?? "";
  const currentEmployeeId: number = currentEmployee?.id ?? 0;

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  const fetchAndSetCandidate = async () => {
    if (candidate && candidate.id != id) {
      await dispatch(setCandidateState(null));
    }
    await dispatch(setLoader(true));
    await dispatch(
      getCandidateDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
        } else {
          let candidate = response.data;
          let interviewerOrAdminCandidate =
            (currentEmployeeRole == "Interviewer" &&
              currentEmployeeId == candidate?.created_by_id) ||
            currentEmployeeRole != "Interviewer";
          if (!interviewerOrAdminCandidate) {
            flashMessage("Not Authorized", "error");
            router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
          }
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetCandidate();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [id]);

  const onSaveDetail = () => {
    if (submitRef && submitRef.current) {
      submitRef.current.click();
    }
  };

  const onUpdateDetail = async (data: any) => {
    dispatch(setLoader(true));
    const { success, message } = await candidateApi.updateCandidateDetail(
      id,
      data,
    );
    dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
    }
  };

  if (!candidate) {
    return null;
  }

  return (
    <>
      <section className="edit-candidate">
        <PageHeader
          pageTitle={"Candidates"}
          breadcrumb={["Edit Details"]}
          pageDescription={"Edit Detail"}
          buttonComponent={
            currentPagePermissions.includes("edit") && (
              <Button
                onClick={onSaveDetail}
                className="btn btn-theme mx-150px ms-auto">
                Save Detail
              </Button>
            )
          }>
          <EditCandidateDetail
            submitRef={submitRef}
            onSubmit={onUpdateDetail}
            subdomain={subdomain}
            candidate={candidate}
          />
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain, allowedRoles, pagePermissions } =
    await extractSubdomainAndDomain(req, { wildcard: true }, "candidates");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const encryptedId = String(query.id) as string;
  return {
    props: {
      subdomain: subdomain,
      encryptedId: encryptedId,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "edit"],
    },
  };
};

CandidatePage.layout = PrivateLayout;
