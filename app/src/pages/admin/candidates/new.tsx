import React, { useRef } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import { PagePermissionInterface } from "@src/redux/interfaces";

type DepartmentPageProps = {
  encryptedId: string;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};
import { NewCandidateDetail } from "@src/components/WildCard";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { Button } from "antd";
import { candidate<PERSON><PERSON> } from "@src/apis/wildcardApis";

export default function CandidatePage({
  pagePermissions,
  subdomain,
}: DepartmentPageProps) {
  const dispatch = useAppDispatch();
  const submitRef = useRef<HTMLButtonElement | null>(null);
  const router = useRouter();

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  const onSaveDetail = () => {
    if (submitRef && submitRef.current) {
      submitRef.current.click();
    }
  };

  const onUpdateDetail = async (data: any) => {
    dispatch(setLoader(true));
    const { success, message } = await candidateApi.createCandidate(data);
    dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.CANDIDATE_MANAGEMENT);
    }
  };

  return (
    <>
      <section className="new-candidate">
        <PageHeader
          pageTitle={"Candidates"}
          breadcrumb={["New Candidate"]}
          pageDescription={"New Candidate Detail"}
          buttonComponent={
            currentPagePermissions.includes("edit") && (
              <Button
                onClick={onSaveDetail}
                className="btn btn-theme mx-150px ms-auto">
                Save Detail
              </Button>
            )
          }>
          <NewCandidateDetail
            submitRef={submitRef}
            onSubmit={onUpdateDetail}
            subdomain={subdomain}
          />
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain, allowedRoles, pagePermissions } =
    await extractSubdomainAndDomain(req, { wildcard: true }, "candidates");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "write"],
    },
  };
};

CandidatePage.layout = PrivateLayout;
