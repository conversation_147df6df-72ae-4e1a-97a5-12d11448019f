import React, { useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { getOpportunityDetail, setLoader } from "@src/redux/actions";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { WildcardEditOpportunity } from "@src/components/WildCard";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";

type OpportunityEditPageProps = {
  id: number;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  pageDetail: KeyPairInterface;
};

export default function OpportunityEditPage({
  subdomain,
  id,
  pageDetail,
}: OpportunityEditPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const opportunity = useSelector(
    (state: RootState) => state.opportunity.opportunity,
  );
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const currentEmployeeCreated = (id: number) => {
    if (!currentEmployee) {
      return false;
    }

    return currentEmployee.id == id;
  };

  const isAdmin = ["Super Admin", "Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  const fetchAndSetOppertunity = async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getOpportunityDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.OPPORTUNITY_MANAGEMENT);
        } else {
          let opportunity = response?.data;
          if (
            !(
              isAdmin || currentEmployeeCreated(opportunity?.created_by_id ?? 0)
            )
          ) {
            flashMessage("Not Authorized", "error");
            router.push(APP_ROUTE.OPPORTUNITY_MANAGEMENT);
          }
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetOppertunity();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  return (
    <>
      <section className="opportunity">
        {opportunity && (
          <WildcardEditOpportunity
            opportunity={opportunity}
            subdomain={subdomain}
            pageTitle={pageDetail?.title ?? "Opportunity Management"}
            breadcrumb={[`Edit Details`]}
            pageDescription={`Edit ${pageDetail?.singular_name} Details`}
          />
        )}
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "opportunities",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "edit"],
      pageDetail: pageDetail,
    },
  };
};

OpportunityEditPage.layout = PrivateLayout;
