import React, { useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { getOpportunityDetail, setLoader } from "@src/redux/actions";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { WildcardOpportunityDetail } from "@src/components/WildCard";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Link from "next/link";
import { Button } from "react-bootstrap";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";

type OpportunityEditPageProps = {
  id: number;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  pageDetail: KeyPairInterface;
};

export default function OpportunityDetailPage({
  subdomain,
  id,
  pagePermissions,
  pageDetail,
}: OpportunityEditPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const opportunity = useSelector(
    (state: RootState) => state.opportunity.opportunity,
  );

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const opportunityPagePermssions = useEmployeePagePermissions({
    pagePermissions,
  });

  const fetchAndSetOppertunity = async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getOpportunityDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.OPPORTUNITY_MANAGEMENT);
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetOppertunity();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const currentEmployeeCreated = (id: number) => {
    if (!currentEmployee) {
      return false;
    }

    return currentEmployee.id == id;
  };

  const hasEditPermission = opportunityPagePermssions.includes("edit");
  const isAdmin = ["Super Admin", "Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  return (
    <>
      <section className="opportunity">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Opportunity Management"}
          breadcrumb={[`${pageDetail?.singular_name ?? "Opportunity"} Detail`]}
          pageDescription={`${pageDetail?.singular_name ?? "Opportunity"} Detail`}
          buttonComponent={
            <div className="ms-auto">
              {(currentEmployeeCreated(opportunity?.id ?? 0) || isAdmin) &&
                hasEditPermission && (
                  <Link
                    href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${id}/edit`.subdomainLink(
                      subdomain,
                    )}
                    className="no-decoration">
                    <Button className="btn btn-theme">
                      Edit {pageDetail?.singular_name ?? "Opportunity"} Detail
                    </Button>
                  </Link>
                )}
              <Link
                href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${id}/shortlisted`.subdomainLink(
                  subdomain,
                )}
                className="no-decoration ms-1">
                <Button className="btn btn-theme">
                  Shortlisted Candidates
                </Button>
              </Link>
            </div>
          }>
          <div className="box-content">
            {opportunity && (
              <WildcardOpportunityDetail
                opportunity={opportunity}
                subdomain={subdomain}
              />
            )}
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "opportunities",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

OpportunityDetailPage.layout = PrivateLayout;
