import React, { useEffect, useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { OpportunityDetailCard } from "@src/components/WildCard";
import { useAppDispatch } from "@src/redux/store";
import { getOpportunityDetail, setLoader } from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { ShortlistedCandidate } from "@src/components/WildCard/Opportunity/ShortlistedCandidates";
import { Skeleton } from "antd";

type ShortlistedDetailPageProps = {
  subdomain: string;
  readonly id: number;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  pageDetail: KeyPairInterface;
};

export default function ShortlistedDetailPage({
  subdomain,
  id,
  pagePermissions,
  pageDetail,
}: ShortlistedDetailPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState<boolean>(false);
  const opportunity = useSelector(
    (state: RootState) => state.opportunity.opportunity,
  );

  const fetchAndSetOpportunity = async () => {
    await setLoading(true);
    await dispatch(
      getOpportunityDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.OPPORTUNITY_MANAGEMENT);
        }
      }),
    );
    await setLoading(false);
  };

  useEffect(() => {
    fetchAndSetOpportunity();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <section className="business">
        <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-2 justify-content-between mb-3">
          <div className="bredcrubs d-flex gap-3 align-items-end">
            <h1 className="m-0 page-head">
              {pageDetail?.title ?? "Opportunity Management"}
            </h1>
            <h4 className="m-0 page-head primary-clr position-relative ps-3">
              Shortlisted Details
            </h4>
          </div>
        </div>

        <div className="row row-gap-mobile row-gap-3">
          {opportunity ? (
            <>
              <div className="col-xl-5 col-lg-12">
                <OpportunityDetailCard
                  opportunity={opportunity}
                  skelton={false}
                />
              </div>
              <div className="col-xl-7 col-lg-12">
                <ShortlistedCandidate opportunity={opportunity} />
              </div>
            </>
          ) : (
            <>
              <div className="col-xl-5 col-lg-12">
                <div className="card m-0">
                  <div className="p-3 border-bottom">
                    <h6 className="m-0 fw-semibold heading-clr">
                      <Skeleton active paragraph={false} />
                    </h6>
                  </div>
                  <div className="p-3">
                    <Skeleton active paragraph={{ rows: 10 }} />
                  </div>
                </div>
              </div>
              <div className="col-xl-7 col-lg-12">
                <div className="card m-0">
                  <div className="p-3 border-bottom">
                    <h6 className="m-0 fw-semibold heading-clr">
                      <Skeleton active paragraph={false} />
                    </h6>
                  </div>
                  <div className="p-3">
                    <Skeleton active paragraph={{ rows: 10 }} />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "opportunities",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      pageDetail: pageDetail,
    },
  };
};

ShortlistedDetailPage.layout = PrivateLayout;
