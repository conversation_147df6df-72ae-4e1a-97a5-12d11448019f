import React from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { AdminDashboardComponent } from "@src/components/Admin";
import { WildcardDashboardComponent } from "@src/components/WildCard";
import { decryptString } from "@src/helper/encryption";

export default function Dashboard({ subdomain }: any) {
  return (
    <>
      {/* <section className="dashbaord"> */}
      {subdomain === "admin" ? (
        <AdminDashboardComponent />
      ) : (
        <WildcardDashboardComponent subdomain={subdomain} />
      )}
      {/* </section> */}
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardAdmin: true },
    "dashboard",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let token: string[] = [];

  try {
    let loginToken: string = decryptString(String(query.login_token ?? ""));
    token = loginToken.split("-");
  } catch {
    token = [];
  }

  return {
    props: {
      subdomain: subdomain,
      ghostMode: token.includes("Super Admin"),
    },
  };
};

Dashboard.layout = PrivateLayout;
