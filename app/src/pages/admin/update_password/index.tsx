import React, { useState } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { KeyPairInterface } from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { profileApi } from "@src/apis/commonApis";
import flashMessage from "@src/components/FlashMessage";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";

// Default values for the reset password form state
const defaultValue: KeyPairInterface = {
  old_password: "",
  new_password: "",
  confirm_password: "",
};

const UpdatePasswordFields: GlobalInputFieldType[] = [
  {
    name: "old_password",
    label: "Old Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
    groupName: "Update Password",
    className: "col-sm-12 col-md-6 col-lg-6",
  },
  {
    name: "new_password",
    label: "New Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
    className: "col-sm-12 col-md-6 col-lg-6",
  },
  {
    name: "confirm_password",
    label: "Confirm Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
    className: "col-sm-12 col-md-6 col-lg-6",
  },
];

export default function UpdatePassword({ subdomain }: any) {
  const [state, setState] = useState<KeyPairInterface>(defaultValue); // State to store form input values
  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter();

  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } = await profileApi.updatePassword(
      subdomain == "admin",
      state,
    );
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.DASHBOARD);
    }
  };

  return (
    <>
      <section className="profile">
        <div className="card shadow">
          <div className="card-header py-3">
            <div className="d-sm-flex align-items-center justify-content-between mb-2">
              <h1 className="h5 mb-0 text-theme-secondary">Profile</h1>
            </div>
          </div>
          <div className="card-body">
            <div className="box-content">
              <ModalFormInput
                state={state}
                setState={setState}
                fields={UpdatePasswordFields}
                onSubmit={handleSubmit}
                buttonTitle={"Update Password"}
              />
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardAdmin: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

UpdatePassword.layout = PrivateLayout;
