import React, { useCallback, useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import {
  getAllDepartmentList,
  openDialog,
  setLoader,
} from "@src/redux/actions";
import { Button } from "react-bootstrap";
import DialogComponents from "@src/components/DialogComponents";
import { WildcardDepartmentList } from "@src/components/WildCard";
import { useEmployeePagePermissions } from "@src/helper/pagePermissions";
import {
  KeyPairInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";

interface DepartmentSearchParams {
  page: number;
  search?: string;
}

type DepartmentPageProps = {
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  filters: DepartmentSearchParams;
  pageDetail: KeyPairInterface;
};

export default function DepartmentPage({
  subdomain,
  pagePermissions,
  filters,
  pageDetail,
}: DepartmentPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { currentPage, limit, rows } = useSelector(
    (state: RootState) => state.department,
  );

  const currentPagePermissions: string[] = useEmployeePagePermissions({
    pagePermissions,
  });

  // Fetch the department data with pagination
  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      try {
        await dispatch(setLoader(true));
        await dispatch(
          getAllDepartmentList({ page: currentPage, limit: limit }),
        );
        await dispatch(setLoader(false));
      } catch (err) {
        console.error("Error fetching departments data:", err);
      } finally {
        await dispatch(setLoader(false));
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (subdomain) {
      fetchData(filters?.page ?? 1, limit ?? 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subdomain]);

  // Open the modal to add a new department
  const openNewDepartmentModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.ADD_NEW_DEPARTMENT,
        options: {
          onDepartmentCreate: onDepartmentCreate,
        },
      }),
    );
  };

  // Handle department creation and refetch data
  const onDepartmentCreate = async () => {
    fetchData(currentPage, limit);
  };

  useEffect(() => {
    let queryParams: DepartmentSearchParams = { page: currentPage };
    router.push(
      { pathname: APP_ROUTE.DEPARTMENT_MANAGEMENT, query: queryParams as any },
      undefined,
      { shallow: true },
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage]);

  const addNewButton = currentPagePermissions.includes("write") ? (
    <Button onClick={openNewDepartmentModal} className="btn btn-theme ms-auto">
      + Add {pageDetail.singular_name ?? "Department"}
    </Button>
  ) : null;

  return (
    <>
      <section className="department">
        <PageHeader
          pageTitle={pageDetail?.title ?? "Department Management"}
          pageDescription={
            pageDetail?.description ?? "Department Management Description"
          }
          buttonComponent={rows.length > 0 && addNewButton}>
          <div className="box-content">
            <WildcardDepartmentList
              fetchData={fetchData}
              subdomain={subdomain}
              addNewButton={addNewButton}
              currentPagePermissions={currentPagePermissions}
            />
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(req, { wildcard: true }, "departments");
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  let filters: DepartmentSearchParams = {
    page: 1,
  };

  if (query) {
    const { search } = query;
    const page = query.page ? Number(query.page) : 1;
    // `search` should be a string or undefined
    const searchString = typeof search === "string" ? search : undefined;

    // Construct filters object with default values or undefined
    filters = {
      ...filters, // Spread the existing properties (if any)
      ...(searchString ? { search: searchString } : {}), // Conditionally add `search`
      ...(page && Number.isNaN(page) ? {} : { page }), // Conditionally add `page` if it's valid
    };
  }

  return {
    props: {
      subdomain: subdomain,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read"],
      filters: filters,
      pageDetail: pageDetail,
    },
  };
};

DepartmentPage.layout = PrivateLayout;
