import React, { useEffect } from "react";
import { PrivateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { getCandidateInterviewDetail, setLoader } from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import {
  InterviewDetailInterface,
  PageDetailInterface,
} from "@src/redux/interfaces";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import PageHeader from "@src/components/PageHeader/PageHeader";
import { useRouter } from "next/router";
import { RescheduleInterview } from "@src/components/WildCard";
import flashMessage from "@src/components/FlashMessage";
import Link from "next/link";

interface InterviewSearchParams {
  page: number;
  search?: string;
}

type InterviewPageProps = {
  subdomain: string;
  filters: InterviewSearchParams;
  pageDetail: PageDetailInterface;
  id: number;
};

export default function InterviewPage({
  subdomain,
  id,
  pageDetail,
}: InterviewPageProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");

  const interviewDetail = useSelector(
    (state: RootState) => state.candidateInterview.detail,
  );

  const fetchAndSetDetail = async () => {
    await dispatch(setLoader(true));
    await dispatch(
      getCandidateInterviewDetail(id, async (success, response) => {
        await dispatch(setLoader(false));
        if (!success) {
          flashMessage(response.message, "error");
          router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const hasJobReadPermission = jobPermissions.includes("read");
  const interview = interviewDetail?.interview;
  const interviews = interviewDetail?.records ?? [];
  const lastInterview = interviews[
    interviews.length - 1
  ] as InterviewDetailInterface;

  return (
    <>
      <section className="interview">
        <PageHeader
          pageTitle={pageDetail.title ?? "Interview Management"}
          breadcrumb={[`Reschedule Interview`]}
          pageDescription={`Reschedule Interview`}
          buttonComponent={
            interview && (
              <span className="ml-auto job-id">
                Job ID{" "}
                {hasJobReadPermission ? (
                  <Link
                    target="_blank"
                    href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${interview.opportunity_id}`}>
                    #J0B-{interview.opportunity_id.toString().padStart(4, "0")}
                  </Link>
                ) : (
                  `#J0B-${interview.opportunity_id.toString().padStart(4, "0")}`
                )}
              </span>
            )
          }>
          <div className="box-content">
            {interview && interview.id == id && (
              <RescheduleInterview
                interview={interview}
                lastInterview={lastInterview}
                subdomain={subdomain}
              />
            )}
          </div>
        </PageHeader>
      </section>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const {
    redirectUrl,
    validate,
    subdomain,
    allowedRoles,
    pagePermissions,
    pageDetail,
  } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
    "interviews",
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const id = Number(query.id) as Number;
  return {
    props: {
      subdomain: subdomain,
      id: id,
      allowedRoles: allowedRoles,
      pagePermissions: pagePermissions,
      requiredPermission: ["read", "edit"],
      pageDetail: pageDetail,
    },
  };
};

InterviewPage.layout = PrivateLayout;
