import { useEffect } from "react";
import { useRouter } from "next/router";

const GoogleAuthCallback = () => {
  const router = useRouter();

  useEffect(() => {
    const { token, expires_at } = router.query;

    console.log(token, "token", window.opener);
    if (token && window.opener) {
      window.opener.postMessage({ token, expires_at }, window.location.origin);
      window.close();
    }
  }, [router.query]);

  return <p>Authenticating...</p>;
};

export async function getServerSideProps() {
  return {
    props: {
      preventPreserveState: true,
    },
  };
}

export default GoogleAuthCallback;
