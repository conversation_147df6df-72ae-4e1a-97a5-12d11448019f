import React, { useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import {
  EmailPasswordInputInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { useAppDispatch } from "@src/redux/store";
import { loginCandidate, setLoader } from "@src/redux/actions";
import { useRouter } from "next/router";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";

// Default values for the authentication state
const defaultValue: EmailPasswordInputInterface = {
  email: "",
  password: "",
};

// Field definitions for the authentication form
const AuthFields: GlobalInputFieldType[] = [
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
  },
  {
    name: "password",
    label: "Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
];

type LoginProps = {
  subdomain: string;
};

// Component for login password
export default function Login({ subdomain }: LoginProps) {
  const [loginState, setLoginState] = useState<
    EmailPasswordInputInterface | KeyPairInterface
  >(defaultValue); // State to store form input values

  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Function to handle form submission for candidate login
  const handleCandidateSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    await dispatch(loginCandidate(loginState)); // Dispatch action to login candidate
    await dispatch(setLoader(false)); // Hide loader after API response
  };

  return (
    <>
      <h4>Welcome!</h4>
      <p>Please enter the detail below to continue the login process</p>

      {/* Authentication form component */}
      <AuthFormInput
        state={loginState}
        setState={setLoginState}
        fields={AuthFields}
        onSubmit={handleCandidateSubmit}
        buttonTitle={"Login"}
        formType={"candidate"}
        forgotPasswordLink
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      wildcardCandidate: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      layoutfor: "candidate",
    },
  };
};

// Assign layout for the Login component
Login.layout = AuthLayout;
