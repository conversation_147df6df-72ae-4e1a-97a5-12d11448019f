import React, { useCallback, useEffect, useState } from "react";
import { CandidatePublicLayout } from "@src/components/Layout";
import { PreventCopyPaste, ScreenShareBlock, SecurityBlock } from "@src/utils";
import { ScreeningRound } from "@src/components/WildcardCandidate";
import { GetServerSideProps } from "next";
import { decrypt } from "@src/helper/encryption";
import flashMessage from "@src/components/FlashMessage";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { InterviewDetailInterface } from "@src/redux/interfaces";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";

type ScreeningInterviewProps = {
  interview_id: number;
};

export default function ScreeningInterview({
  interview_id,
}: ScreeningInterviewProps) {
  const [interview, setInterview] = useState<InterviewDetailInterface | null>(
    null,
  );
  const router = useRouter();

  const fetchAndSetInterview = useCallback(
    async () => {
      const { success, ...response } =
        await interviewsApi.candidateInterviewsDetail(interview_id);
      if (success) {
        if (response.data?.status_name != "Scheduled") {
          router.replace(APP_ROUTE.CANDIDATE_DASHBOARD);
        } else {
          setInterview(response.data);
        }
      } else {
        flashMessage(response.message, "error");
        router.replace(APP_ROUTE.CANDIDATE_DASHBOARD);
        // close()
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  useEffect(() => {
    fetchAndSetInterview();
  }, [fetchAndSetInterview]);

  return (
    <>
      <PreventCopyPaste />
      <ScreenShareBlock />
      <SecurityBlock />
      <div className="container-fluid p-0">
        <div className="d-flex align-items-center justify-content-between mb-3">
          <div className="bredcrubs d-flex gap-3 align-items-center">
            <h1 className="m-0 page-head">Interview</h1>
            <h4 className="m-0 page-head primary-clr position-relative ps-3">
              Screening Round
            </h4>
          </div>
        </div>

        <section className="video-record">
          {interview && (
            <ScreeningRound interviewId={interview_id} interview={interview} />
          )}
        </section>
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const { id } = query;
  let interview_id = Number(decrypt(id as string));

  if (Number.isNaN(interview_id)) {
    return {
      redirect: {
        destination: "/candidates/dashboard",
        permanent: false,
      },
    };
  }

  return {
    props: {
      interview_id: interview_id,
    },
  };
};

ScreeningInterview.layout = CandidatePublicLayout;
