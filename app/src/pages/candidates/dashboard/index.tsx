import React from "react";
import { CandidateLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { CurrentTime } from "@src/components/Common";
import { useCurrentCandidate } from "@src/helper/candidate";
import {
  CandidateInterviewJobCard,
  CandidateProfileCard,
  CandidateRunningInterview,
} from "@src/components/WildcardCandidate";

export default function Dashboard() {
  const candidate = useCurrentCandidate();

  return (
    <>
      <div className="container-fluid p-0">
        <div className="dashboard-pattern-top">
          <div className="d-flex justify-content-between align-items-end mb-4 row-gap-mobile flex-wrap flex-lg-nowrap relative">
            <div className="welcome-heading">
              <h1 className="text-white">Welcome!</h1>
              <p className="mb-0 text-white">
                Here&apos;s what happening in your desk today. Stay updated.
              </p>
            </div>
            <CurrentTime />
          </div>

          <div className="card bg-transparent shadow-none">
            <div className="row row-gap-mobile row-gap-3">
              <div className="col-xl-5 col-lg-12">
                <CandidateProfileCard candidate={candidate} />
                <CandidateInterviewJobCard candidate={candidate} />
              </div>
              <div className="col-xl-7 col-lg-12">
                <CandidateRunningInterview candidate={candidate} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

Dashboard.layout = CandidateLayout;
