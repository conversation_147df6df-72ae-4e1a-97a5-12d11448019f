import React, { useState } from "react";
import { WildcardPublicLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { Upload, List, Button } from "antd";
import { DraggerProps } from "antd/lib/upload/Dragger";
import type { UploadChangeParam, UploadFile } from "antd/lib/upload";
import { FormLabel, Spinner } from "react-bootstrap";
import ArchiveIcon from "@mui/icons-material/Archive";
import flashMessage from "@src/components/FlashMessage";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import Image from "next/image";
import {
  DocumentRequestInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import candidateDocumentApi from "@src/apis/wildcardApis/candidateDocumentApi";

type DocumentFileInterface = {
  adharFront: Array<UploadFile>;
  adharBack: Array<UploadFile>;
  panFront: Array<UploadFile>;
  panBack: Array<UploadFile>;
  degreeCertifcates: Array<UploadFile>;
  others: Array<UploadFile>;
  [key: string]: Array<UploadFile>;
};

const InitalErrorState: KeyPairInterface = {
  adharFront: null,
  adharBack: null,
  panFront: null,
  panBack: null,
  degreeCertifcates: null,
  others: null,
};

const InitalDocumentState: DocumentFileInterface = {
  adharFront: [],
  adharBack: [],
  panFront: [],
  panBack: [],
  degreeCertifcates: [],
  others: [],
};

type UploadDocumentProps = {
  token: string;
  currentObject?: DocumentRequestInterface;
  success: boolean;
};
export default function UploadDocuments({
  token,
  success,
  currentObject,
}: UploadDocumentProps) {
  const adhar_certificate = currentObject?.adhar_certificate;
  const degree_certificate = currentObject?.degree_certificate;
  const pan_certificate = currentObject?.pan_certificate;
  const other_certificate = currentObject?.other_certificate;

  const dispatch = useAppDispatch();
  const [documents, setDocuments] =
    useState<DocumentFileInterface>(InitalDocumentState);

  const [errors, setErrors] = useState<KeyPairInterface>(InitalErrorState);
  const [submitted, setSubmitted] = useState<boolean>(false);

  // Function to validate the uploaded file before submission
  const beforeUpload = async (name: string, file: File) => {
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    const allowedFileTypes = ["pdf", "png", "jpg", "jpeg", "docx", "doc"];
    if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
      flashMessage(
        `Only ${allowedFileTypes.join(", ")} files are accepted.`,
        "error",
      );
      return Upload.LIST_IGNORE;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      flashMessage("Maximum file upload size allowed is 1 MB.", "error");
      return Upload.LIST_IGNORE;
    }

    const uploadedFiles = documents[name];
    const isFileExist = uploadedFiles.some(
      (uploadedFile) =>
        uploadedFile.name === file.name && uploadedFile.size === file.size,
    );

    if (isFileExist) {
      flashMessage(`${file.name} has already been uploaded.`, "error");
      return Upload.LIST_IGNORE; // Prevent the file from being uploaded
    }
    return true;
  };

  // Function to handle file changes
  const handleChange = async (
    name: string,
    info: UploadChangeParam<UploadFile>,
  ) => {
    const { status } = info.file;
    if (!status || status === "error") {
      return null;
    }

    const files = info.fileList;
    if (files) {
      setDocuments((prev) => ({ ...prev, [name]: files }));
      setErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  // Function to upload the resume file and extract details
  const uploadDocuments = async () => {
    let error;
    if (documents.adharFront.length === 0 && adhar_certificate) {
      setErrors((prev) => ({
        ...prev,
        adharFront: "Adhar Card Front Side is required.",
      }));
      error = true;
    }

    if (documents.adharBack.length === 0 && adhar_certificate) {
      setErrors((prev) => ({
        ...prev,
        adharBack: "Adhar Card Back Side is required.",
      }));
      error = true;
    }

    if (documents.panFront.length === 0 && pan_certificate) {
      setErrors((prev) => ({
        ...prev,
        panFront: "PAN Card Front Side is required.",
      }));
      error = true;
    }

    if (documents.panBack.length === 0 && pan_certificate) {
      setErrors((prev) => ({
        ...prev,
        panBack: "PAN Card Back Side is required.",
      }));
      error = true;
    }

    if (documents.degreeCertifcates.length === 0 && degree_certificate) {
      setErrors((prev) => ({
        ...prev,
        degreeCertifcates: "Degree/Education Certificates are required.",
      }));
      error = true;
    }

    if (documents.others.length === 0 && other_certificate) {
      setErrors((prev) => ({
        ...prev,
        degreeCertifcates: "Others Documents are required.",
      }));
      error = true;
    }

    if (error) {
      return null;
    }

    const formData = new FormData();
    let index = 0;
    if (adhar_certificate) {
      documents.adharFront.forEach((file: any) => {
        formData.append(`files[${index}]`, file.originFileObj, "Adhar Card");
        index++;
      });

      documents.adharBack.forEach((file: any) => {
        formData.append(`files[${index}]`, file.originFileObj, "Adhar Card");
        index++;
      });
    }

    if (pan_certificate) {
      documents.panFront.forEach((file: any) => {
        formData.append(`files[${index}]`, file.originFileObj, "PAN Card");
        index++;
      });

      documents.panBack.forEach((file: any) => {
        formData.append(`files[${index}]`, file.originFileObj, "PAN Card");
        index++;
      });
    }

    if (degree_certificate) {
      documents.degreeCertifcates.forEach((file: any) => {
        formData.append(
          `files[${index}]`,
          file.originFileObj,
          "Education Certificate",
        );
        index++;
      });
    }

    if (other_certificate) {
      documents.others.forEach((file: any) => {
        formData.append(`files[${index}]`, file.originFileObj, "Others");
        index++;
      });
    }

    await dispatch(setLoader(true));
    formData.append("token", token);
    const { success, ...response } =
      await candidateDocumentApi.UploadCandidateDocument(formData);
    await dispatch(setLoader(false));

    if (success) {
      setDocuments(InitalDocumentState);
      setSubmitted(true);
    } else {
      flashMessage(response.message, "error");
    }
  };

  const handleRemove = (name: string, file: any) => {
    const uploadedFiles = documents[name];
    const files = uploadedFiles.filter(
      (uploadedFile) => uploadedFile.name !== file.name,
    );
    setDocuments((prev) => ({ ...prev, [name]: files }));
  };

  if (!success) {
    return (
      <section className="candidate-documents">
        <FormLabel className="main-label">Expired/Invalid Link</FormLabel>
        <div className="document-row">
          <div className="p-3">
            The link has expired or is invalid. Please request a new link or
            contact support for assistance.
          </div>
        </div>
      </section>
    );
  }

  if (submitted) {
    return (
      <section className="candidate-documents">
        <FormLabel className="main-label">Success</FormLabel>
        <div className="document-row">
          <div className="p-3">
            Thank you for submitting your document. We will review it shortly
            and get back to you.
          </div>
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="candidate-documents">
        <FormLabel className="main-label">
          Upload Documents<span className="required-field">*</span>
        </FormLabel>

        {adhar_certificate && (
          <div className="document-row">
            <FormLabel className="file-label">
              Adhar Card<span className="required-field">*</span>
            </FormLabel>
            <div className="row row-gap-3">
              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    name="adharFront"
                    maxCount={20}
                    error={errors.adharFront}
                    beforeUpload={(file) => beforeUpload("adharFront", file)}
                    onChange={(info) => handleChange("adharFront", info)}
                    title={"Front Side"}
                    fileList={documents.adharFront}
                  />
                </div>
                <RenderDocumentList
                  documents={documents.adharFront}
                  className="mt-2"
                  onDelete={(file) => handleRemove("adharFront", file)}
                />
              </div>

              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    name="adharBack"
                    maxCount={20}
                    error={errors.adharBack}
                    beforeUpload={(file) => beforeUpload("adharBack", file)}
                    onChange={(info) => handleChange("adharBack", info)}
                    title={"Back Side"}
                    fileList={documents.adharBack}
                  />
                </div>
                <RenderDocumentList
                  documents={documents.adharBack}
                  className="mt-2"
                  onDelete={(file) => handleRemove("adharBack", file)}
                />
              </div>
            </div>
          </div>
        )}

        {pan_certificate && (
          <div className="document-row">
            <FormLabel className="file-label">
              PAN Card<span className="required-field">*</span>
            </FormLabel>
            <div className="row row-gap-3">
              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    name="panFront"
                    maxCount={20}
                    error={errors.panFront}
                    beforeUpload={(file) => beforeUpload("panFront", file)}
                    onChange={(info) => handleChange("panFront", info)}
                    title={"Front Side"}
                    fileList={documents.panFront}
                  />
                </div>
                <RenderDocumentList
                  documents={documents.panFront}
                  className="mt-2"
                  onDelete={(file) => handleRemove("panFront", file)}
                />
              </div>

              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    name="panBack"
                    maxCount={20}
                    error={errors.panBack}
                    beforeUpload={(file) => beforeUpload("panBack", file)}
                    onChange={(info) => handleChange("panBack", info)}
                    title={"Back Side"}
                    fileList={documents.panBack}
                  />
                </div>
                <RenderDocumentList
                  documents={documents.panBack}
                  className="mt-2"
                  onDelete={(file) => handleRemove("panBack", file)}
                />
              </div>
            </div>
          </div>
        )}

        {degree_certificate && (
          <div className="document-row">
            <FormLabel className="file-label">
              Degree/Education Certificates
              <span className="required-field">*</span>
              <span className="max-documents">(Maximum 20 files)</span>
            </FormLabel>
            <div className="row row-gap-3">
              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    multiple={true}
                    name="degreeCertifcates"
                    accept=".pdf,.png,.jpg, .jpeg, .docx, .doc"
                    maxCount={20}
                    error={errors.degreeCertifcates}
                    beforeUpload={(file) =>
                      beforeUpload("degreeCertifcates", file)
                    }
                    onChange={(info) => handleChange("degreeCertifcates", info)}
                    title={"Certificates"}
                    fileList={documents.degreeCertifcates}
                  />
                </div>{" "}
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6">
                <RenderDocumentList
                  documents={documents.degreeCertifcates}
                  onDelete={(file) => handleRemove("degreeCertifcates", file)}
                />
              </div>
            </div>
          </div>
        )}

        {other_certificate && (
          <div className="document-row">
            <FormLabel className="file-label">
              Other Documents
              <span className="required-field">*</span>
              <span className="max-documents">(Maximum 20 files)</span>
            </FormLabel>
            <div className="row row-gap-3">
              <div className="col-sm-12 col-md-6 col-lg-6">
                <div>
                  <RenderUploader
                    multiple={true}
                    name="others"
                    accept=".pdf,.png,.jpg, .jpeg, .docx, .doc"
                    maxCount={20}
                    error={errors.others}
                    beforeUpload={(file) => beforeUpload("others", file)}
                    onChange={(info) => handleChange("others", info)}
                    title={"Others"}
                    fileList={documents.others}
                  />
                </div>
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6">
                <RenderDocumentList
                  documents={documents.others}
                  onDelete={(file) => handleRemove("others", file)}
                />
              </div>
            </div>
          </div>
        )}

        <div className="mt-3 text-center d-block">
          <Button className="submit-btn mb-4" onClick={uploadDocuments}>
            Submit
          </Button>
        </div>
      </section>
    </>
  );
}

type RenderUploaderProps = DraggerProps & {
  title: string;
  error?: string;
};

const RenderUploader: React.FC<RenderUploaderProps> = ({
  title,
  accept,
  error,
  ...props
}) => {
  return (
    <>
      <Upload.Dragger
        accept={accept ?? ".pdf,.png,.jpg, .jpeg"}
        showUploadList={false}
        style={{ borderColor: error ? "red" : "black" }}
        {...props}>
        <ArchiveIcon fontSize="medium" color="primary" />
        {title}
        <p className="ant-upload-text">
          Maximum file upload size allowed is 2 MB.
        </p>
      </Upload.Dragger>
      {error && <p className="error-message">{error}</p>}
    </>
  );
};

type RenderDocumentListProps = {
  documents: Array<UploadFile>;
  onDelete: (item: UploadFile) => void;
  className?: string;
};

const RenderDocumentList: React.FC<RenderDocumentListProps> = ({
  documents,
  onDelete,
  className,
}) => {
  if (!documents || documents.length === 0) {
    return null;
  }

  return (
    <List
      itemLayout="horizontal"
      className={className}
      dataSource={documents}
      renderItem={(item, index) => (
        <List.Item key={index} className="upload-file-name">
          <List.Item.Meta
            title={
              <div className="d-flex w-100 relative justify-content-between">
                <span className="document-name">
                  {item.status !== "done" && <Spinner className="spinner" />}
                  {item.name}
                </span>
                <button
                  onClick={() => onDelete(item)}
                  className="remove-document">
                  <Image
                    src="/images/cancel.svg"
                    height={15}
                    width={15}
                    alt={""}
                  />
                </button>
              </div>
            }
          />
        </List.Item>
      )}
    />
  );
};

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      wildcard: true,
    },
  );
  const token = query.token ?? ("" as string);
  const { success, ...response } =
    await candidateDocumentApi.ValidateCandidateDocumentLink(subdomain ?? "", {
      token,
    });

  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      token: token,
      success: success,
      response: response,
      currentObject: (response && response.data) ?? null,
    },
  };
};

UploadDocuments.layout = WildcardPublicLayout;
