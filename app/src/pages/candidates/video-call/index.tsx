import React, { useEffect, useState } from "react";
import { WildcardPublicLayout } from "@src/components/Layout";
import { Row, Col } from "react-bootstrap";
import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";

export default function VideoCall() {
  const [timeRemaining, setTimeRemaining] = useState<any>({
    percentage: 100,
    value: "10:00",
  });
  useEffect(() => {
    setInterval(() => {
      setTimeRemaining((prev: any) => ({
        ...timeRemaining,
        percentage: prev.timeRemaining - 1,
      }));
    }, 6000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <div className="container-fluid p-0">
        <div className="d-flex align-items-center justify-content-between mb-3">
          <div className="bredcrubs d-flex gap-3 align-items-center">
            <h1 className="m-0 page-head">Interview Scheduled</h1>
            <h4 className="m-0 page-head primary-clr position-relative ps-3">
              Video Record Round
            </h4>
          </div>
        </div>

        <section className="video-record">
          <Row style={{ rowGap: "20px" }}>
            <Col lg={8}>
              <div className="video-screen card p-4 m-0">
                <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                  <h4 className="mb-2 heading-clr">Video Recoding Round </h4>
                  <p className="mb-0 text-light-clr">
                    Start recording your video and answer the questions display
                    on right side with the the time limit.
                  </p>
                </div>
                <div className="video-box">
                  <div className="box">
                    {/* <img
                      src="/images/video-person.svg"
                      alt="video-person"
                      className="w-100"
                    /> */}
                    <div className="overlay"></div>
                  </div>

                  <div className="start-box">
                    {/* <Button className="btn btn-primary border-0 d-flex gap-2 align-items-center">
                      {" "}
                      <img src="/images/play.svg" alt="play" /> Start
                    </Button>
                    <Button className="btn btn-danger border-0 d-flex gap-2 align-items-center">
                      {" "}
                      <img src="/images/stop.svg" alt="stop" /> Stop
                    </Button> */}
                    <span className="record">
                      Recording: <b>12:34</b>
                    </span>
                    <div className="box">
                      <span className="round">
                        Round: <b>03</b>
                      </span>
                      <div className="time-name">
                        <h4>12 Sept, 2024 | 12:30 PM - 1:30 PM</h4>
                        <span>
                          Interviewer: <b>Malik Shah (Sr. Manager)</b>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={4}>
              <div className="interview-question card p-4 m-0 mb-3 h-100">
                <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                  <h4 className="mb-2 heading-clr">Interviewer Questions</h4>
                  <span>
                    Interview Record Time: <b>20 min</b>
                  </span>
                </div>
              </div>

              <div className="start-box">
                {/* <Button className="btn btn-primary border-0 d-flex gap-2 align-items-center">
                  {" "}
                  <img src="/images/play.svg" alt="play" /> Start
                </Button>
                <Button className="btn btn-danger border-0 d-flex gap-2 align-items-center">
                  {" "}
                  <img src="/images/stop.svg" alt="stop" /> Stop
                </Button> */}
                <span className="record">
                  Recording: <b>12:34</b>
                </span>
                <div className="box">
                  <span className="round">
                    Round: <b>03</b>
                  </span>
                  <div className="time-name">
                    <h4>12 Sept, 2024 | 12:30 PM - 1:30 PM</h4>
                    <span>
                      Interviewer: <b>Malik Shah (Sr. Manager)</b>
                    </span>
                  </div>
                </div>
              </div>
            </Col>
            <Col lg={5}>
              <div className="interview-question card p-4 m-0 mb-3 h-100">
                <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
                  <h4 className="mb-2 heading-clr">Interviewer Questions</h4>
                </div>
                <ul>
                  <li className="complete">
                    <span>1</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis ultricies consequat?
                    </h4>
                    <div className="count">
                      <div className="count check">
                        {/* <img src="/images/count-check.svg" alt="count-check" /> */}
                      </div>
                    </div>
                  </li>
                  <li>
                    <span>2</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis?
                    </h4>
                    <div className="count">
                      <CircularProgressbar
                        value={timeRemaining.percentage}
                        styles={{
                          // Customize the root svg element
                          root: {},
                          // Customize the path, i.e. the "completed progress"
                          path: {
                            // Path color
                            stroke: `rgba(238,238,238, ${timeRemaining.percentage / 100})`,
                            // Whether to use rounded or flat corners on the ends - can use 'butt' or 'round'
                            strokeLinecap: "butt",
                            // Customize transition animation
                            transition: "stroke-dashoffset 0.5s ease 0s",
                            // Rotate the path
                            transform: "rotate(0.25turn)",
                            transformOrigin: "center center",
                          },
                          // Customize the circle behind the path, i.e. the "total progress"
                          trail: {
                            // Trail color
                            stroke: "#EEEEEE",
                            // Whether to use rounded or flat corners on the ends - can use 'butt' or 'round'
                            strokeLinecap: "butt",
                            // Rotate the trail
                            transform: "rotate(0.25turn)",
                            transformOrigin: "center center",
                          },
                          // Customize the text
                          text: {
                            // Text color
                            fill: "#000000",
                            // Text size
                            fontSize: "14px",
                          },
                          // Customize background - only used when the `background` prop is true
                          background: {
                            fill: "#fff",
                          },
                        }}
                      />
                    </div>
                  </li>
                  <li>
                    <span>3</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis?
                    </h4>
                  </li>
                  <li>
                    <span>4</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis?
                    </h4>
                  </li>
                  <li>
                    <span>5</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis?
                    </h4>
                  </li>
                  <li>
                    <span>6</span>
                    <h4>
                      Phasellus et velit sed est vulputate tempus id id est.
                      Aliquam finibus ex quis?
                    </h4>
                  </li>
                </ul>
              </div>
            </Col>
          </Row>
        </section>
      </div>
    </>
  );
}

VideoCall.layout = WildcardPublicLayout;
