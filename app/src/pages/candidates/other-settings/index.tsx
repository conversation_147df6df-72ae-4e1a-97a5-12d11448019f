import React, { useState } from "react";
import { CandidateLayout } from "@src/components/Layout";
import { useAppDispatch } from "@src/redux/store";
import { Row, Col, Card } from "react-bootstrap";
import CandidatePageHeader from "@src/components/PageHeader/CandidatePageHeader";
import { KeyPairInterface } from "@src/redux/interfaces";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { setLoader } from "@src/redux/actions";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useRouter } from "next/router";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { candidateAuthenticate<PERSON><PERSON> } from "@src/apis/wildcardCandidate<PERSON>pis";

// Default values for the reset password form state
const defaultValue: KeyPairInterface = {
  old_password: "",
  new_password: "",
  confirm_password: "",
};

const UpdatePasswordFields: GlobalInputFieldType[] = [
  {
    name: "old_password",
    label: "Old Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
    groupName: "Update Password",
  },
  {
    name: "new_password",
    label: "New Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
  {
    name: "confirm_password",
    label: "Confirm Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
];

export default function OtherSettings() {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(defaultValue); // State to store form input values
  const router = useRouter();

  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      await candidateAuthenticateApi.updatePassword(state);
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
    }
  };

  return (
    <>
      <CandidatePageHeader pageTitle="Other Settings">
        <div className="card bg-transparent shadow-none">
          <Row className="row-gap-mobile row-gap-3">
            <Col sm={12} lg={4}>
              <Card className="p-0 mb-0 candidate-self-card schedule-interview-self-card my-profile">
                <div className="card-header common-heading border-bottom p-3">
                  <div className="d-flex align-items-center flex-wrap gap-1">
                    <h4 className="mb-0 heading-clr page-description w-100">
                      Credential Settings
                    </h4>
                    <p className="mb-0 text-light-clr">
                      You can change your password anytime.
                    </p>
                  </div>
                </div>
                <Card.Body className="p-3">
                  <div className="interview-history box-content">
                    <ModalFormInput
                      state={state}
                      setState={setState}
                      fields={UpdatePasswordFields}
                      onSubmit={handleSubmit}
                      buttonTitle={"Update Password"}
                    />
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      </CandidatePageHeader>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

OtherSettings.layout = CandidateLayout;
