import React, { useEffect, useState } from "react";
import { CandidateLayout } from "@src/components/Layout";
import { Row, Col } from "react-bootstrap";
import { Card } from "react-bootstrap";
import CandidatePageHeader from "@src/components/PageHeader/CandidatePageHeader";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { GetServerSideProps } from "next";
import { decrypt } from "@src/helper/encryption";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { CandidateInterviewCard } from "@src/components/WildcardCandidate";
import { useCurrentCandidate } from "@src/helper/candidate";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";

interface JobDetail {
  job_id: number; // Unique identifier for the job
  job_title: string; // Title of the job
  job_description: string; // Description of the job
  job_status: number; // Status of the job (could also be an enum if specific values are known)
  interview_at: string; // Date and time of the interview in ISO format
}

interface InterviewHistoryInterface {
  rows: JobDetail[];
  count: number;
  page: number;
  limit: number;
}

const defaultInterviewState: InterviewHistoryInterface = {
  rows: [],
  count: 0,
  page: 1,
  limit: 10,
};

type InterviewHistoryProps = {
  opportunity_id: number;
};

export default function InterviewHistory({
  opportunity_id,
}: InterviewHistoryProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const candidate = useCurrentCandidate();

  const [interviewDetails, setInterviewDetails] = useState<any[]>([]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchData = async () => {
    dispatch(setLoader(true));
    const { success, ...response } =
      await interviewsApi.candidateJobInterviews(opportunity_id);
    if (success) {
      setInterviewDetails(response.data);
    } else {
      flashMessage(response.message, "error");
      router.push(APP_ROUTE.CANDIDATE_DASHBOARD);
    }
    dispatch(setLoader(false));
  };

  return (
    <>
      <CandidatePageHeader
        pageTitle="Interview History"
        breadcrumb={["Detail"]}>
        <div className="card bg-transparent shadow-none">
          <Row className="row-gap-mobile row-gap-3">
            <Col lg={12}>
              <Card className="p-0 mb-0 candidate-self-card schedule-interview-self-card my-profile">
                <div className="card-header common-heading border-bottom p-3">
                  <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-3 w-100">
                    <h4 className="mb-0 heading-clr page-description">
                      Interview History
                    </h4>
                  </div>
                </div>
                <Card.Body className="p-3">
                  <div className="round-steps">
                    <ul>
                      {candidate &&
                        interviewDetails &&
                        interviewDetails.map((interview, index) => (
                          <CandidateInterviewCard
                            key={index}
                            interview={interview}
                          />
                        ))}
                    </ul>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      </CandidatePageHeader>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  const { id } = query;
  let opportunity_id = Number(decrypt(id as string));

  return {
    props: {
      opportunity_id: opportunity_id,
    },
  };
};

InterviewHistory.layout = CandidateLayout;
