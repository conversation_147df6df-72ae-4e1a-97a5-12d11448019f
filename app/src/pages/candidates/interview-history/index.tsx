import React, { useEffect, useState } from "react";
import { CandidateLayout } from "@src/components/Layout";
import { Pagination } from "antd";
import { Row, Col, Button } from "react-bootstrap";
import { Card } from "react-bootstrap";
import CandidatePageHeader from "@src/components/PageHeader/CandidatePageHeader";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import Link from "next/link";
import { encrypt } from "@src/helper/encryption";
import { APP_ROUTE } from "@src/constants";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";

interface JobDetail {
  job_id: number; // Unique identifier for the job
  job_title: string; // Title of the job
  job_description: string; // Description of the job
  job_status: number; // Status of the job (could also be an enum if specific values are known)
  interview_at: string; // Date and time of the interview in ISO format
}

interface InterviewHistoryInterface {
  rows: JobDetail[];
  count: number;
  page: number;
  limit: number;
}

const defaultInterviewState: InterviewHistoryInterface = {
  rows: [],
  count: 0,
  page: 1,
  limit: 10,
};

export default function InterviewHistory() {
  const dispatch = useAppDispatch();
  const [{ count, page, limit, rows }, setState] =
    useState<InterviewHistoryInterface>(defaultInterviewState);

  useEffect(() => {
    fetchData(1, 10);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const fetchData = async (page: number, limit: number) => {
    dispatch(setLoader(true));
    const { success, ...response } =
      await interviewsApi.candidateInterviewsList({
        page,
        limit,
      });
    if (success) {
      setState(response.data);
    }
    dispatch(setLoader(false));
  };

  const hasJobs = rows.length > 0;

  return (
    <>
      <CandidatePageHeader pageTitle="Interview History">
        <div className="card bg-transparent shadow-none">
          <Row className="row-gap-mobile row-gap-3">
            <Col lg={12}>
              <Card className="p-0 mb-0 candidate-self-card schedule-interview-self-card my-profile">
                <div className="card-header common-heading border-bottom p-3">
                  <div className="d-flex align-items-center flex-wrap flex-lg-nowrap gap-3 w-100">
                    <h4 className="mb-0 heading-clr page-description">
                      Interview History
                    </h4>
                  </div>
                </div>
                <Card.Body className="p-3">
                  <div className="interview-history">
                    <div className="row row-gap-3">
                      {hasJobs ? (
                        rows.map((job: JobDetail, index: number) => (
                          <div className="col-lg-6 col-12" key={index}>
                            <div className="box">
                              <div className="content-box">
                                <span>
                                  Date:{" "}
                                  <b>
                                    {job.interview_at?.strftime("%B %d, %Y")}
                                  </b>
                                </span>
                                <h5>{job.job_title}</h5>
                              </div>
                              <Link
                                href={`${APP_ROUTE.CANDIDATE_INTREVIEW_HISTORY}/${encrypt(job.job_id.toString())}`}>
                                <Button className="btn btn-primary border-0">
                                  View Details
                                </Button>
                              </Link>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="col-lg-12">
                          <div className="box">
                            <div className="content-box">
                              <h5 className="text-center">
                                No History Available
                              </h5>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <Pagination
                    className="mt-4"
                    current={page}
                    total={count}
                    pageSize={limit}
                    hideOnSinglePage
                    onChange={handlePageChange}
                  />
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </div>
      </CandidatePageHeader>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    { wildcardCandidate: true },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

InterviewHistory.layout = CandidateLayout;
