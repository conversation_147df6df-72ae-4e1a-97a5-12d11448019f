import { PublicLayout } from "@src/components/Layout";
import { PageTitle } from "@src/components/PublicPage";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";
import Accordion from "react-bootstrap/Accordion";

interface FAQItem {
  title: string;
  value: string;
}

const FAQList: Array<FAQItem> = [
  {
    title: "What benefits can Recruitease Pro bring to my company?",
    value:
      "Recruitease Pro can help you manage open positions, screen candidates, and track applications effectively by streamlining your recruiting process.",
  },
  {
    title: "Is Recruitease Pro suitable for businesses of all sizes?",
    value:
      "Yes, Recruitease Pro benefits businesses of all sizes, from small startups to large enterprises, by helping to improve and manage the recruitment process well.",
  },
  {
    title: "Can I integrate Recruitease Pro into my existing software?",
    value:
      "Integration of an Recruitease Pro into an existing HR system will vary depending on the software and complexity of the existing process. However, many Recruitease Pro providers offer support and services to assist with integration.",
  },
  {
    title: "Can Recruitease Pro help diversification?",
    value:
      "Yes, Recruitease Pro can assist with diversity and inclusion efforts by providing tools to track and identify candidates to ensure fairness and equity in hiring.",
  },
  {
    title: "What is an application tracking system?",
    value:
      "An Recruitease Pro, Applicant Tracking System is software designed to simplify hiring. It organizes data, tracks application progress, and helps recruiters stay up to date.",
  },
  {
    title: "How secure is the data stored in an Recruitease Pro?",
    value:
      "Data in an Recruitease Pro is typically encrypted and stored on secure servers. Recruitease Pro providers implement security measures like access controls and regular data backups to safeguard candidate information.",
  },
  {
    title:
      "How can an Recruitease Pro streamline the candidate screening and selection process?",
    value:
      "An Recruitease Pro automates tasks like resume parsing, keyword identification, and candidate ranking. This helps recruiters quickly identify suitable candidates for further consideration.",
  },
  {
    title: "Is training required to use an Recruitease Pro effectively?",
    value:
      "While some training may be beneficial, many Recruitease Pro platforms are user-friendly and intuitive, requiring minimal training for basic use. Providers often offer tutorials and support to assist users.",
  },
  {
    title:
      "Can I tailor an Recruitease Pro to meet my specific hiring requirements?",
    value:
      "Certainly, most Recruitease Pro platforms offer customization options such as custom workflows, tailored job requirements, and user permission configurations to align with your unique recruitment process.",
  },
];

export default function FrequentlyAskedQuestions() {
  return (
    <>
      <PageTitle title="Frequently Asked Questions" />
      <section className="faq-section py-80">
        <div className="container">
          <div className="text-left semibold common-heading mb-5 ft20">
            <p>
              Whether you&apos;re a seasoned HR professional or just getting
              started, we&apos;ve compiled a comprehensive list of frequently
              asked questions to address any queries you may have about our
              advanced Recruitease Pro platform. Checkout our FAQ section now to
              simplify your recruitment process with our innovative Recruitease
              Pro.
            </p>
          </div>
          {/*--accordian-*/}
          <Accordion
            defaultActiveKey="0"
            className="accordion faq-accordian mb-5"
            id="accordionExample">
            {FAQList.map((faq: FAQItem, index: number) => (
              <Accordion.Item
                eventKey={index.toString()}
                className="accordion-item border-0 mb-3"
                key={index}>
                <Accordion.Header>{faq.title}</Accordion.Header>
                <Accordion.Body>{faq.value}</Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>

          {/*--accordian-*/}
        </div>
      </section>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

FrequentlyAskedQuestions.layout = PublicLayout;
