import React, { useEffect, useState } from "react";
import { WildcardPublicLayout } from "@src/components/Layout";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { useAppDispatch } from "@src/redux/store";
import { jobsApi } from "@src/apis/wildcardApis";
import { OpportunityInterface } from "@src/redux/interfaces";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { JobCard } from "@src/components/Opportunity";

type JobsProps = {
  subdomain: string;
  success: boolean;
};
export default function Jobs({ success, subdomain }: JobsProps) {
  const dispatch = useAppDispatch();
  const [page, setPage] = useState<number>(1);
  const [state, setState] = useState<{
    currentPage: number;
    limit: number;
    count: number;
    rows: OpportunityInterface[];
  }>({
    currentPage: 1,
    limit: 10,
    count: 0,
    rows: [],
  });

  const fetchData = async (page: number) => {
    const { success, ...response } = await jobsApi.getJobsList({
      subdomain: subdomain,
      page: page,
      limit: state.limit,
    });

    if (success) {
      const { rows, count } = response.data;
      setState((prevState) => {
        const mergedRows = [...prevState.rows, ...rows];

        // Create a Map to ensure unique rows based on the `id` field
        const uniqueRows = Array.from(
          new Map(mergedRows.map((row) => [row.id, row])).values(),
        );

        return { ...prevState, count: count, rows: uniqueRows };
      });
    }
  };

  useEffect(() => {
    fetchData(page);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const applyJob = (job: OpportunityInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.JOB_REQUEST_MODAL,
        options: {
          title: "Apply Job",
          jobId: job.id,
        },
      }),
    );
  };

  return (
    <>
      <div className="d-flex align-items-center justify-content-between">
        <div className="bredcrubs d-flex gap-3 align-items-end">
          {/* <h1 className="h3 mb-3 page-head">Matching Jobs</h1> */}
        </div>
        {/* <!-- <p className="heading-clr">June 24, 2024 | 9:56 PM</p> --> */}
      </div>
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              {/* <!--filter--> */}
              {/* <div className="brief-filter-wrap gap-3 d-flex flex-wrap justify-content-between align-items-center mb-4">
                <div className="d-flex gap-3 flex-wrap">
                  <div className="search position-relative">
                    <SearchIcon
                      className="position-absolute top-0 bottom-0 m-auto"
                      style={{ left: "12px" }}
                    />
                    <input
                      type="text"
                      className="form-control ps-5"
                      placeholder="Search by id, keyword, etc."
                    />
                  </div>
                </div>
              </div> */}
              {/* <!--filter-end--> */}

              {/* <!--list--> */}
              <div className="candidate-list-wrap p-3 matching-jobs">
                <div className="row">
                  {state.rows.map((row, index) => (
                    <JobCard jobDetail={row} key={index} applyJob={applyJob} />
                  ))}
                </div>
              </div>
              {/* <!--list-end--> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

// Get server-side props to validate and extract subdomain
export const getServerSideProps: GetServerSideProps = async ({
  req,
  query,
}) => {
  const { subdomain } = await extractSubdomainAndDomain(req, {
    wildcard: true,
  });

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

Jobs.layout = WildcardPublicLayout;
