import { PublicLayout } from "@src/components/Layout";
import {
  FAQSection,
  HowItWorkSection,
  AboutIntroSection,
  PageTitle,
} from "@src/components/PublicPage";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function AboutUs() {
  return (
    <>
      <PageTitle title="About Us" />

      {/* <PartnerSection /> */}

      <section className="beief-intro lightbg py-80 text-center">
        <div className="container">
          <p className="mb-0 ">
            Recruitease Pro is dedicated to simplifying the hiring process. With
            our innovative Applicant Tracking System (Recruitease Pro), we help
            streamline their recruitment processes, saving valuable time and
            hiring the best candidates.
          </p>
        </div>
      </section>

      <AboutIntroSection />
      <HowItWorkSection />
      <FAQSection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

AboutUs.layout = PublicLayout;
