import { PublicLayout } from "@src/components/Layout";
import {
  FAQSection,
  PageTitle,
  WorkProcessCRMSection,
  WorkProcessSection,
} from "@src/components/PublicPage";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function WorkProcess() {
  return (
    <>
      <PageTitle title="How It Works?" />
      <WorkProcessSection />
      <WorkProcessCRMSection />
      <FAQSection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

WorkProcess.layout = PublicLayout;
