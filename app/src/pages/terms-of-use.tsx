import { PublicLayout } from "@src/components/Layout";
import { PageTitle } from "@src/components/PublicPage";
import { TermsOfUseSection } from "@src/components/PublicPage/TermsOfUseSection";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function TermsofService() {
  return (
    <>
      <PageTitle title="Terms of Service" />
      <TermsOfUseSection />
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

TermsofService.layout = PublicLayout;
