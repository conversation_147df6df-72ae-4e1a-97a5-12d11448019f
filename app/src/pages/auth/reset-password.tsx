import React, { useEffect, useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import {
  EmailPasswordInputInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { useAppDispatch } from "@src/redux/store";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import {
  AdminResetPassword,
  EmployeeResetPassword,
  setLoader,
} from "@src/redux/actions";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";

// Default values for the reset password form state
const defaultValue: KeyPairInterface = {
  email: "",
  otp: "",
  password: "",
  confirm_password: "",
};

// Field definitions for the reset password form
const ResetPasswordFields: GlobalInputFieldType[] = [
  {
    name: "password",
    label: "Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
  {
    name: "confirm_password",
    label: "Confirm Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
];

type ResetPasswordProps = {
  query: {
    token: string;
  };
  subdomain: string;
};

// Component for resetting password
export default function ResetPassword({
  query,
  subdomain,
}: ResetPasswordProps) {
  const [resetPasswordState, setResetPasswordState] = useState<
    EmailPasswordInputInterface | KeyPairInterface
  >(defaultValue); // State to store form input values
  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Parse token from query parameters and set state when component mounts
  useEffect(() => {
    try {
      var token = (query.token ?? "") as string;
      const authState = JSON.parse(atob(token));
      setResetPasswordState((prev) => ({ ...prev, ...authState }));
    } catch (e) {
      router.replace(`${APP_ROUTE.LOGIN}`);
    }
  }, [router, query.token]);

  // Function to handle form submission for resetting password
  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const action =
      subdomain === "admin"
        ? AdminResetPassword(resetPasswordState) // API call to reset admin password
        : EmployeeResetPassword(resetPasswordState); // API call to reset employee password
    await dispatch(action);
    await dispatch(setLoader(false)); // Hide loader after API response
  };

  return (
    <>
      <h4>Reset Password!</h4>
      <p>Please enter new and confirm password</p>

      {/* Reset password form component */}
      <AuthFormInput
        state={resetPasswordState}
        setState={setResetPasswordState}
        fields={ResetPasswordFields}
        onSubmit={handleSubmit}
        buttonTitle={"Reset Password"}
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      wildcardAdmin: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      query: query,
    },
  };
};

// Assign layout for the ResetPassword component
ResetPassword.layout = AuthLayout;
