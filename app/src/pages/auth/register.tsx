import React, { useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import {
  KeyPairInterface,
  RegisterInputInterface,
} from "@src/redux/interfaces";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { useAppDispatch } from "@src/redux/store";
import { useRouter } from "next/router";
import { setLoader } from "@src/redux/actions";
import { adminAuthenticateApi } from "@src/apis/adminApis";
import { APP_ROUTE } from "@src/constants";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import flashMessage from "@src/components/FlashMessage";

// Default values for the registration form state
const defaultValue: RegisterInputInterface = {
  first_name: "",
  last_name: "",
  email: "",
  password: "",
  dob: "",
};

// Today's date
const today = new Date();

// Field definitions for the registration form
const RegisterFields: GlobalInputFieldType[] = [
  {
    name: "first_name",
    label: "First Name",
    type: "text",
    dataType: "alphabetics",
    strict: true,
    maxLength: 40,
    required: true,
  },
  {
    name: "last_name",
    label: "Last Name",
    type: "text",
    strict: true,
    dataType: "alphabetics",
    maxLength: 40,
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
  },
  {
    name: "password",
    label: "Password",
    type: "password",
    dataType: "password",
    minLength: 8,
    maxLength: 20,
    required: true,
  },
  // {
  //   name: "dob",
  //   label: "Date Of Birth",
  //   type: "date",
  //   dataType: "date",
  //   required: true,
  //   strict: true,
  //   minDate: new Date(
  //     today.getFullYear() - 100,
  //     today.getMonth(),
  //     today.getDate(),
  //   ).toDateString(),
  //   maxDate: new Date(
  //     today.getFullYear() - 20,
  //     today.getMonth(),
  //     today.getDate(),
  //   ).toDateString(),
  // },
  // {
  //   name: "gender",
  //   label: "Gender",
  //   type: "select",
  //   dataType: "select",
  //   required: true,
  //   selectEmpty: true,
  //   placeholder: "Select Gender",
  //   options: [
  //     { label: "Male", value: "Male" },
  //     { label: "Female", value: "Female" },
  //     { label: "Other", value: "Other" },
  //   ],
  // },
];

// Component for registration page
export default function Register() {
  const [registerState, setRegisterState] = useState<
    RegisterInputInterface | KeyPairInterface
  >(defaultValue); // State to store form input values

  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Function to handle form submission for registration
  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      await adminAuthenticateApi.AdminRegisterApi(registerState); // API call to register admin user
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error"); // Show flash message based on API response
    if (success) {
      router.push(
        `${APP_ROUTE.VERIFY_ACCOUNT}?token=${btoa(registerState.email.trim())}`,
      ); // Redirect to verify account page on success
    }
  };

  return (
    <>
      <h4>Create your account!</h4>
      <p>Please enter the detail below to continue the register process</p>

      {/* Registration form component */}
      <AuthFormInput
        state={registerState}
        setState={setRegisterState}
        fields={RegisterFields}
        onSubmit={handleSubmit}
        buttonTitle={"Register"}
        loginLink
        acceptTerms
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      admin: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

// Assign layout for the Register component
Register.layout = AuthLayout;
