import React, { useEffect, useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import { KeyPairInterface } from "@src/redux/interfaces";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { setLoader } from "@src/redux/actions";
import { adminAuthenticateApi } from "@src/apis/adminApis";
import { employeeAuthenticate<PERSON>pi } from "@src/apis/wildcardApis";
import { APP_ROUTE } from "@src/constants";
import { useAppDispatch } from "@src/redux/store";
import { useRouter } from "next/router";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import flashMessage from "@src/components/FlashMessage";

// Default value for the OTP input
const defaultValue: KeyPairInterface = {
  otp: "",
};

// Field definition for the OTP input
const AuthFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "onlynumber",
    dataType: "onlynumber",
    strict: true,
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

type VerifyOtpProps = {
  query: {
    token: string;
  };
  subdomain: string;
};

// Component for verifying OTP
export default function VerifyOtp({ query, subdomain }: VerifyOtpProps) {
  const [authState, setAuthState] = useState<KeyPairInterface>(defaultValue); // State to store form input values
  const [email, setEmail] = useState<string | undefined>(undefined); // State to store decoded email from token
  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Decode email from token and set state when component mounts
  useEffect(() => {
    try {
      var token = (query.token ?? "") as string;
      setEmail(atob(token));
    } catch (e) {
      router.replace(`${APP_ROUTE.LOGIN}`);
    }
  }, [router, query.token]);

  // Function to handle form submission for verifying OTP
  const handleSubmit = async () => {
    const payload = { ...authState, email: email }; // Construct payload with OTP and email
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      subdomain == "admin"
        ? await adminAuthenticateApi.AdminVerifyForgotOtp(payload) // API call to verify OTP for admin
        : await employeeAuthenticateApi.EmployeeVerifyForgotOtp(payload); // API call to verify OTP for employee
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error"); // Show flash message based on API response
    if (success) {
      // Redirect to reset password page on success
      const encryptedToken = btoa(
        JSON.stringify({ email: email, otp: authState.otp }),
      );
      router.push(`${APP_ROUTE.RESET_PASSWORD}?token=${encryptedToken}`);
    }
  };

  // Function to handle OTP resend
  const onResend = async () => {
    const payload = { email: email }; // Construct payload with email
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      subdomain == "admin"
        ? await adminAuthenticateApi.AdminResendForgotPasswordOtp(payload) // API call to resend OTP for admin
        : await employeeAuthenticateApi.EmployeeResendForgotPasswordOtp(
            payload,
          ); // API call to resend OTP for employee
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error"); // Show flash message based on API response
    return success;
  };

  return (
    <>
      <h4>Verify OTP!</h4>
      <p>Please verify your OTP to continue resetting your password.</p>

      {/* Verification form component */}
      <AuthFormInput
        state={authState}
        setState={setAuthState}
        fields={AuthFields}
        onSubmit={handleSubmit}
        buttonTitle={"Verify OTP"}
        onResend={onResend}
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      wildcardAdmin: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      query: query,
    },
  };
};

// Assign layout for the VerifyOtp component
VerifyOtp.layout = AuthLayout;
