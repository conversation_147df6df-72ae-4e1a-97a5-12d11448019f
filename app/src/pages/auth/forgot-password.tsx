import React, { useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import { KeyPairInterface } from "@src/redux/interfaces";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { useAppDispatch } from "@src/redux/store";
import { useRouter } from "next/router";
import { adminAuthenticateApi } from "@src/apis/adminApis";
import { employeeAuthenticateApi } from "@src/apis/wildcardApis";
import { setLoader } from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import flashMessage from "@src/components/FlashMessage";

// Default value for the authentication state
const defaultValue: KeyPairInterface = {
  email: "",
};

// Field definitions for the authentication form
const authFields: GlobalInputFieldType[] = [
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
  },
];

// Component for Forgot Password page
export default function ForgotPassword({ subdomain }: any) {
  const [authState, setAuthState] = useState<KeyPairInterface>(defaultValue); // State to store form input values
  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Function to handle form submission
  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      subdomain === "admin"
        ? await adminAuthenticateApi.AdminForgotPasswordApi(authState)
        : await employeeAuthenticateApi.EmployeeForgotPasswordApi(authState);
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error"); // Show flash message based on API response
    if (success) {
      router.replace(
        `${APP_ROUTE.VERIFY_OTP}?token=${btoa(authState.email.trim())}`, // Redirect to OTP verification page
      );
    }
  };

  return (
    <>
      <h4>Forgot Password!</h4>
      <p>Please enter the email to reset your password</p>

      {/* Authentication form component */}
      <AuthFormInput
        state={authState}
        setState={setAuthState}
        fields={authFields}
        onSubmit={handleSubmit}
        buttonTitle={"Forgot Password"}
        loginLink
        signUpLink={subdomain === "admin"} // Conditionally show sign up link for admin subdomain
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      wildcardAdmin: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
    },
  };
};

// Assign layout for the ForgotPassword component
ForgotPassword.layout = AuthLayout;
