import React, { useEffect, useState } from "react";
import { AuthLayout } from "@src/components/Layout";
import { KeyPairInterface } from "@src/redux/interfaces";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { AuthFormInput } from "@src/components/Auth/AuthFormInput";
import { useAppDispatch } from "@src/redux/store";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { adminAuthenticateApi } from "@src/apis/adminApis";
import { AdminVerifyUserAccount, setLoader } from "@src/redux/actions";
import { GetServerSideProps } from "next";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import flashMessage from "@src/components/FlashMessage";

// Default value for the verification OTP
const defaultValue: KeyPairInterface = {
  otp: "",
};

// Field definition for the OTP input
const AuthFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "onlynumber",
    dataType: "onlynumber",
    strict: true,
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

type VerifyAccountProps = {
  query: {
    token: string;
  };
};

// Component for verifying user account
export default function VerifyAccount({ query }: VerifyAccountProps) {
  const [authState, setAuthState] = useState<KeyPairInterface>(defaultValue); // State to store form input values
  const [email, setEmail] = useState<string | undefined>(undefined); // State to store decoded email from token
  const dispatch = useAppDispatch(); // Dispatch function from Redux store
  const router = useRouter(); // Next.js router instance

  // Decode email from token and set state when component mounts
  useEffect(() => {
    try {
      var token = (query.token ?? "") as string;
      setEmail(atob(token));
    } catch (e) {
      router.replace(`${APP_ROUTE.LOGIN}`);
    }
  }, [router, query.token]);

  // Function to handle form submission for verifying user account
  const handleSubmit = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    await dispatch(AdminVerifyUserAccount({ ...authState, email: email })); // API call to verify user account
    await dispatch(setLoader(false)); // Hide loader after API response
  };

  // Function to handle OTP resend
  const onResend = async () => {
    await dispatch(setLoader(true)); // Show loader while waiting for API response
    const { success, ...response } =
      await adminAuthenticateApi.AdminResendAccountVerifyOtp({ email: email }); // API call to resend OTP
    await dispatch(setLoader(false)); // Hide loader after API response
    flashMessage(response.message, success ? "success" : "error"); // Show flash message based on API response
    return success;
  };

  return (
    <>
      <h4>Verify Account!</h4>
      <p>Please enter the OTP to verify your account</p>

      {/* Verification form component */}
      <AuthFormInput
        state={authState}
        setState={setAuthState}
        fields={AuthFields}
        onSubmit={handleSubmit}
        buttonTitle={"Verify OTP"}
        onResend={onResend}
      />
    </>
  );
}

// Server-side props function to extract subdomain from request
export const getServerSideProps: GetServerSideProps = async ({
  req,
  res,
  query,
}) => {
  const { redirectUrl, validate, subdomain } = await extractSubdomainAndDomain(
    req,
    {
      admin: true,
    },
  );
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {
      subdomain: subdomain,
      query: query,
    },
  };
};

// Assign layout for the VerifyAccount component
VerifyAccount.layout = AuthLayout;
