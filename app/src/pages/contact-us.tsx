import { PublicLayout } from "@src/components/Layout";
import { ContactUsFormSection, PageTitle } from "@src/components/PublicPage";
import { extractSubdomainAndDomain } from "@src/helper/domain_helper";
import { GetServerSideProps } from "next";

export default function ContactUs() {
  return (
    <>
      <PageTitle title="Contact us" />
      <ContactUsFormSection />
      <section className="map">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d13718.366604863537!2d76.76827699999998!3d30.72987825!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2sin!4v1717413872476!5m2!1sen!2sin"
          width="100%"
          height="650"
          style={{ border: 0 }}
          allowFullScreen={false}
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"></iframe>
      </section>
    </>
  );
}

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
  const { redirectUrl, validate } = await extractSubdomainAndDomain(req, {
    www: true,
  });
  if (!validate) {
    return {
      redirect: {
        destination: redirectUrl,
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};

ContactUs.layout = PublicLayout;
