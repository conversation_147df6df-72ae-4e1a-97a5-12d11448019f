import { useEffect } from "react";

export const SecurityBlock: React.FC = () => {
  const handleContextMenu = (event: MouseEvent) => {
    event.preventDefault(); // Prevent the right-click menu from appearing
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    // Detect F12, Ctrl+Shift+I, and Ctrl+U for blocking Inspect Mode
    if (
      event.key === "F12" ||
      (event.ctrlKey && event.shiftKey && event.key === "I") ||
      (event.ctrlKey && event.key === "U")
    ) {
      event.preventDefault(); // Prevent the default action
      alert("Inspect mode is disabled on this page."); // Optional alert message
    }
  };

  useEffect(() => {
    document.addEventListener("contextmenu", handleContextMenu); // Prevent right-click
    document.addEventListener("keydown", handleKeyDown); // Detect key presses

    // Cleanup function to remove event listeners
    return () => {
      document.removeEventListener("contextmenu", handleContextMenu);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return null; // This component doesn't render anything
};
