import { useEffect, useState } from "react";

export const PreventScreenshot: React.FC = () => {
  const [showOverlay, setShowOverlay] = useState(false);

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Check for PrintScreen key (keyCode 44) or combinations for taking screenshots
      if (event.key === "PrintScreen") {
        event.preventDefault();
        showBlackOverlay();
      }
    };

    const handleMouseDown = () => {
      // Show overlay if the user tries to capture the screen with mouse
      showBlackOverlay();
    };

    const showBlackOverlay = () => {
      setShowOverlay(true);
      setTimeout(() => {
        setShowOverlay(false); // Hide overlay after 2 seconds
      }, 2000);
    };

    // Attach the event listeners
    document.addEventListener("keydown", handleKeyPress);
    document.addEventListener("mousedown", handleMouseDown);

    // Cleanup function to remove event listeners on component unmount
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
      document.removeEventListener("mousedown", handleMouseDown);
    };
  }, []);

  return (
    <>
      {showOverlay && (
        <div style={overlayStyle}>
          <h1 style={{ color: "white" }}>Screenshot Attempt Detected!</h1>
        </div>
      )}
    </>
  );
};

const overlayStyle: React.CSSProperties = {
  position: "fixed",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  backgroundColor: "black",
  color: "white",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  zIndex: 9999, // Ensure the overlay is on top
};
