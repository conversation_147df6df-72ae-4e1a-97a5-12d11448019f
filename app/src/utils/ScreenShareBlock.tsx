import { useRef, useState } from "react";

export const ScreenShareBlock: React.FC = () => {
  const [isScreenShared, setIsScreenShared] = useState(false);
  const overlayRef = useRef<HTMLDivElement | null>(null);

  const checkScreenSharing = async () => {
    try {
      // Request access to screen sharing
      const mediaStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true, // optional, if you want audio too
      });

      // If mediaStream is obtained, the user is sharing their screen
      if (mediaStream) {
        setIsScreenShared(true);

        // Listen for track ended event to detect when screen sharing stops
        mediaStream.getTracks().forEach((track) => {
          track.addEventListener("ended", () => {
            setIsScreenShared(false);
          });
        });
      }
    } catch (error) {
      console.error("Error accessing display media.", error);
      setIsScreenShared(false);
    }
  };

  return (
    <>
      {isScreenShared && (
        <div
          ref={overlayRef}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "black",
            zIndex: 9999,
          }}
        />
      )}
    </>
  );
};
