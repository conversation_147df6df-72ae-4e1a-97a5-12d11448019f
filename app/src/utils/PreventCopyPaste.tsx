import { useEffect } from "react";

export const PreventCopyPaste: React.FC = () => {
  useEffect(() => {
    const preventDefault = (event: ClipboardEvent) => {
      event.preventDefault();
      // Optionally, you can provide feedback to the user
      // alert("Copy and paste actions are disabled on this page.");
    };

    // Add event listeners for copy, cut, and paste
    document.addEventListener("copy", preventDefault);
    document.addEventListener("cut", preventDefault);
    document.addEventListener("paste", preventDefault);

    // Cleanup function to remove event listeners on component unmount
    return () => {
      document.removeEventListener("copy", preventDefault);
      document.removeEventListener("cut", preventDefault);
      document.removeEventListener("paste", preventDefault);
    };
  }, []);

  return null; // This component doesn't render anything
};
