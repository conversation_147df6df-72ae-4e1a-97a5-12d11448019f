import { BASE_DOMAIN } from "@src/constants";

// Parse the date string to a Date object
function strftime(this: string, format: string): string {
  // Parse the date string to a Date object
  const date = new Date(this);

  // Zero padding function
  const zeroPad = (num: number, length: number): string =>
    String(num).padStart(length, "0");

  const hours = date.getHours();
  const ampm = hours < 12 ? "AM" : "PM";

  // Replacement map
  const replacements: { [key: string]: string | number } = {
    "%Y": date.getFullYear(),
    "%m": zeroPad(date.getMonth() + 1, 2), // Months are 0-indexed
    "%d": zeroPad(date.getDate(), 2),
    "%H": zeroPad(hours, 2),
    "%I": zeroPad(hours % 12 || 12, 2),
    "%M": zeroPad(date.getMinutes(), 2),
    "%S": zeroPad(date.getSeconds(), 2),
    "%a": date.toLocaleDateString("en-US", { weekday: "short" }),
    "%A": date.toLocaleDateString("en-US", { weekday: "long" }),
    "%b": date.toLocaleDateString("en-US", { month: "short" }),
    "%B": date.toLocaleDateString("en-US", { month: "long" }),
    "%p": ampm,
    "%Z": Intl.DateTimeFormat().resolvedOptions().timeZone,
  };

  // Format string replacement
  return format.replace(/%[YmdHIMSabABpZ]/g, (match) =>
    replacements[match] !== undefined ? replacements[match]!.toString() : match,
  );
}

// Function to generate a subdomain link
const subdomainLink = function (this: string, subdomain: string): string {
  // Get the protocol from the window location (e.g., http:// or https://)
  const protocol = window.location.protocol.slice(0, -1);
  // Construct and return the subdomain link using the provided subdomain and base domain
  return `${protocol}://${subdomain}.${BASE_DOMAIN}${this}`;
};

export const titleize = function (this: string): string {
  return this.split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

// Extend the String interface to include the subdomainLink function
declare global {
  interface String {
    subdomainLink(subdomain: string): string;
    strftime(format: string, defaultTimeZone?: string): string;
    titleize(): string;
  }
}

// Add the subdomainLink function to the String prototype
String.prototype.subdomainLink = subdomainLink;
// Add the strftime function to the String prototype
String.prototype.strftime = strftime;
// function to titleize
String.prototype.titleize = titleize;
