/* Full-screen Loader Styles */
section.loader .fullScreenLoader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
}

section.loader .loader {
    border: 3px solid #f3f3f3;
    /* Light grey */
    border-top: 3px solid #6ac0b4;
    /* Blue */
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

section.loader .content {
    padding: 50px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

section.loader .fullScreenLoader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
}

section.loader .blurBackground {
    filter: blur(5px);
}

section.loader .ant-spin-dot-item {
    background-color: white;
}

section.loader .ant-spin-nested-loading .ant-spin-blur {
    clear: both;
    opacity: 0.5;
    user-select: none;
    pointer-events: none;
    width: 100%;
    display: flex;
}

section.loader .ant-spin-nested-loading > div > .ant-spin {
    position: static;
    top: 0;
    inset-inline-start: 0;
    z-index: 4;
    display: flex;
    width: 100%;
    height: 100%;
    max-height: 400px;
}

section.loader .ant-spin-nested-loading {
    position: absolute;
    text-align: center;
    display: flex;
    margin: 0 auto;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    justify-content: center;
}
