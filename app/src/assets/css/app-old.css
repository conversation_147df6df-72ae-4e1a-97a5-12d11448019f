:root,
[data-bs-theme="light"] {
    /*theme variable*/
    --theme-primary-color: #4864e1;
    --theme-light-text-color: #8c8c8c;
    --theme-text-color: #262626;
    --theme-heading-color: #000102;

    --theme-font-family: "Poppins", sans-serif !important;
}

@font-face {
    font-family: "Material Icons";
    font-style: normal;
    font-weight: 400;
    src: url(https://example.com/MaterialIcons-Regular.eot);
    /* For IE6-8 */
    src:
        local("Material Icons"),
        local("MaterialIcons-Regular"),
        url(https://example.com/MaterialIcons-Regular.woff2) format("woff2"),
        url(https://example.com/MaterialIcons-Regular.woff) format("woff"),
        url(https://example.com/MaterialIcons-Regular.ttf) format("truetype");
}

.material-symbols-outlined {
    font-variation-settings:
        "FILL" 0,
        "wght" 400,
        "GRAD" 0,
        "opsz" 24;
}

*,
:after,
:before {
    box-sizing: border-box;
}

.stretched-link:after {
    bottom: 0;
    content: "";
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.vr {
    align-self: stretch;
    background-color: currentcolor;
    display: inline-block;
    min-height: 1em;
    opacity: 0.25;
    width: 1px;
}

.avatar {
    height: 40px;
    width: 40px;
}

.avatar-lg {
    height: 64px;
    width: 64px;
}

.avatar-title {
    align-items: center;
    color: #3b7ddd;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%;
}

.btn .feather {
    height: 14px;
    width: 14px;
}

.btn-danger,
.btn-danger.disabled,
.btn-danger.focus,
.btn-danger.hover:not(:disabled):not(.disabled),
.btn-danger:disabled,
.btn-danger:focus,
.btn-danger:hover:not(:disabled):not(.disabled),
.btn-dark,
.btn-dark.disabled,
.btn-dark.focus,
.btn-dark.hover:not(:disabled):not(.disabled),
.btn-dark:disabled,
.btn-dark:focus,
.btn-dark:hover:not(:disabled):not(.disabled),
.btn-info,
.btn-info.disabled,
.btn-info.focus,
.btn-info.hover:not(:disabled):not(.disabled),
.btn-info:disabled,
.btn-info:focus,
.btn-info:hover:not(:disabled):not(.disabled),
.btn-light,
.btn-light.disabled,
.btn-light.focus,
.btn-light.hover:not(:disabled):not(.disabled),
.btn-light:disabled,
.btn-light:focus,
.btn-light:hover:not(:disabled):not(.disabled),
.btn-primary,
.btn-primary.disabled,
.btn-primary.focus,
.btn-primary.hover:not(:disabled):not(.disabled),
.btn-primary:disabled,
.btn-primary:focus,
.btn-primary:hover:not(:disabled):not(.disabled),
.btn-secondary,
.btn-secondary.disabled,
.btn-secondary.focus,
.btn-secondary.hover:not(:disabled):not(.disabled),
.btn-secondary:disabled,
.btn-secondary:focus,
.btn-secondary:hover:not(:disabled):not(.disabled),
.btn-success,
.btn-success.disabled,
.btn-success.focus,
.btn-success.hover:not(:disabled):not(.disabled),
.btn-success:disabled,
.btn-success:focus,
.btn-success:hover:not(:disabled):not(.disabled),
.btn-warning,
.btn-warning.disabled,
.btn-warning.focus,
.btn-warning.hover:not(:disabled):not(.disabled),
.btn-warning:disabled,
.btn-warning:focus,
.btn-warning:hover:not(:disabled):not(.disabled),
.show > .btn-danger.dropdown-toggle,
.show > .btn-dark.dropdown-toggle,
.show > .btn-info.dropdown-toggle,
.show > .btn-light.dropdown-toggle,
.show > .btn-primary.dropdown-toggle,
.show > .btn-secondary.dropdown-toggle,
.show > .btn-success.dropdown-toggle,
.show > .btn-warning.dropdown-toggle {
    color: #fff;
}

.btn-facebook {
    --bs-btn-color: #fff;
    --bs-btn-bg: #3b5998;
    --bs-btn-border-color: #3b5998;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #324c81;
    --bs-btn-hover-border-color: #2f477a;
    --bs-btn-focus-shadow-rgb: 88, 114, 167;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #2f477a;
    --bs-btn-active-border-color: #2c4372;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #3b5998;
    --bs-btn-disabled-border-color: #3b5998;
}

.btn-facebook,
.btn-facebook.disabled,
.btn-facebook.focus,
.btn-facebook.hover:not(:disabled):not(.disabled),
.btn-facebook:disabled,
.btn-facebook:focus,
.btn-facebook:hover:not(:disabled):not(.disabled),
.show > .btn-facebook.dropdown-toggle {
    color: #fff;
}

.btn-twitter {
    --bs-btn-color: #000;
    --bs-btn-bg: #1da1f2;
    --bs-btn-border-color: #1da1f2;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #3faff4;
    --bs-btn-hover-border-color: #34aaf3;
    --bs-btn-focus-shadow-rgb: 25, 137, 206;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #4ab4f5;
    --bs-btn-active-border-color: #34aaf3;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #1da1f2;
    --bs-btn-disabled-border-color: #1da1f2;
}

.btn-twitter,
.btn-twitter.disabled,
.btn-twitter.focus,
.btn-twitter.hover:not(:disabled):not(.disabled),
.btn-twitter:disabled,
.btn-twitter:focus,
.btn-twitter:hover:not(:disabled):not(.disabled),
.show > .btn-twitter.dropdown-toggle {
    color: #fff;
}

.btn-google {
    --bs-btn-color: #fff;
    --bs-btn-bg: #dc4e41;
    --bs-btn-border-color: #dc4e41;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #bb4237;
    --bs-btn-hover-border-color: #b03e34;
    --bs-btn-focus-shadow-rgb: 225, 105, 94;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #b03e34;
    --bs-btn-active-border-color: #a53b31;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #dc4e41;
    --bs-btn-disabled-border-color: #dc4e41;
}

.btn-google,
.btn-google.disabled,
.btn-google.focus,
.btn-google.hover:not(:disabled):not(.disabled),
.btn-google:disabled,
.btn-google:focus,
.btn-google:hover:not(:disabled):not(.disabled),
.show > .btn-google.dropdown-toggle {
    color: #fff;
}

.btn-youtube {
    --bs-btn-color: #fff;
    --bs-btn-bg: red;
    --bs-btn-border-color: red;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #d90000;
    --bs-btn-hover-border-color: #c00;
    --bs-btn-focus-shadow-rgb: 255, 38, 38;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #c00;
    --bs-btn-active-border-color: #bf0000;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: red;
    --bs-btn-disabled-border-color: red;
}

.btn-youtube,
.btn-youtube.disabled,
.btn-youtube.focus,
.btn-youtube.hover:not(:disabled):not(.disabled),
.btn-youtube:disabled,
.btn-youtube:focus,
.btn-youtube:hover:not(:disabled):not(.disabled),
.show > .btn-youtube.dropdown-toggle {
    color: #fff;
}

.btn-vimeo {
    --bs-btn-color: #000;
    --bs-btn-bg: #1ab7ea;
    --bs-btn-border-color: #1ab7ea;
    --bs-btn-hover-color: #000;
    --bs-btn-hover-bg: #3cc2ed;
    --bs-btn-hover-border-color: #31beec;
    --bs-btn-focus-shadow-rgb: 22, 156, 199;
    --bs-btn-active-color: #000;
    --bs-btn-active-bg: #48c5ee;
    --bs-btn-active-border-color: #31beec;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #000;
    --bs-btn-disabled-bg: #1ab7ea;
    --bs-btn-disabled-border-color: #1ab7ea;
}

.btn-vimeo,
.btn-vimeo.disabled,
.btn-vimeo.focus,
.btn-vimeo.hover:not(:disabled):not(.disabled),
.btn-vimeo:disabled,
.btn-vimeo:focus,
.btn-vimeo:hover:not(:disabled):not(.disabled),
.show > .btn-vimeo.dropdown-toggle {
    color: #fff;
}

.btn-dribbble {
    --bs-btn-color: #fff;
    --bs-btn-bg: #ea4c89;
    --bs-btn-border-color: #ea4c89;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #c74174;
    --bs-btn-hover-border-color: #bb3d6e;
    --bs-btn-focus-shadow-rgb: 237, 103, 155;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #bb3d6e;
    --bs-btn-active-border-color: #b03967;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #ea4c89;
    --bs-btn-disabled-border-color: #ea4c89;
}

.btn-dribbble,
.btn-dribbble.disabled,
.btn-dribbble.focus,
.btn-dribbble.hover:not(:disabled):not(.disabled),
.btn-dribbble:disabled,
.btn-dribbble:focus,
.btn-dribbble:hover:not(:disabled):not(.disabled),
.show > .btn-dribbble.dropdown-toggle {
    color: #fff;
}

.btn-github {
    --bs-btn-color: #fff;
    --bs-btn-bg: #181717;
    --bs-btn-border-color: #181717;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #141414;
    --bs-btn-hover-border-color: #131212;
    --bs-btn-focus-shadow-rgb: 59, 58, 58;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #131212;
    --bs-btn-active-border-color: #121111;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #181717;
    --bs-btn-disabled-border-color: #181717;
}

.btn-github,
.btn-github.disabled,
.btn-github.focus,
.btn-github.hover:not(:disabled):not(.disabled),
.btn-github:disabled,
.btn-github:focus,
.btn-github:hover:not(:disabled):not(.disabled),
.show > .btn-github.dropdown-toggle {
    color: #fff;
}

.btn-instagram {
    --bs-btn-color: #fff;
    --bs-btn-bg: #e4405f;
    --bs-btn-border-color: #e4405f;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #c23651;
    --bs-btn-hover-border-color: #b6334c;
    --bs-btn-focus-shadow-rgb: 232, 93, 119;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #b6334c;
    --bs-btn-active-border-color: #ab3047;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #e4405f;
    --bs-btn-disabled-border-color: #e4405f;
}

.btn-instagram,
.btn-instagram.disabled,
.btn-instagram.focus,
.btn-instagram.hover:not(:disabled):not(.disabled),
.btn-instagram:disabled,
.btn-instagram:focus,
.btn-instagram:hover:not(:disabled):not(.disabled),
.show > .btn-instagram.dropdown-toggle {
    color: #fff;
}

.btn-pinterest {
    --bs-btn-color: #fff;
    --bs-btn-bg: #bd081c;
    --bs-btn-border-color: #bd081c;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #a10718;
    --bs-btn-hover-border-color: #970616;
    --bs-btn-focus-shadow-rgb: 199, 45, 62;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #970616;
    --bs-btn-active-border-color: #8e0615;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #bd081c;
    --bs-btn-disabled-border-color: #bd081c;
}

.btn-pinterest,
.btn-pinterest.disabled,
.btn-pinterest.focus,
.btn-pinterest.hover:not(:disabled):not(.disabled),
.btn-pinterest:disabled,
.btn-pinterest:focus,
.btn-pinterest:hover:not(:disabled):not(.disabled),
.show > .btn-pinterest.dropdown-toggle {
    color: #fff;
}

.btn-flickr {
    --bs-btn-color: #fff;
    --bs-btn-bg: #0063dc;
    --bs-btn-border-color: #0063dc;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #0054bb;
    --bs-btn-hover-border-color: #004fb0;
    --bs-btn-focus-shadow-rgb: 38, 122, 225;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #004fb0;
    --bs-btn-active-border-color: #004aa5;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #0063dc;
    --bs-btn-disabled-border-color: #0063dc;
}

.btn-flickr,
.btn-flickr.disabled,
.btn-flickr.focus,
.btn-flickr.hover:not(:disabled):not(.disabled),
.btn-flickr:disabled,
.btn-flickr:focus,
.btn-flickr:hover:not(:disabled):not(.disabled),
.show > .btn-flickr.dropdown-toggle {
    color: #fff;
}

.btn-bitbucket {
    --bs-btn-color: #fff;
    --bs-btn-bg: #0052cc;
    --bs-btn-border-color: #0052cc;
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #0046ad;
    --bs-btn-hover-border-color: #0042a3;
    --bs-btn-focus-shadow-rgb: 38, 108, 212;
    --bs-btn-active-color: #fff;
    --bs-btn-active-bg: #0042a3;
    --bs-btn-active-border-color: #003e99;
    --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    --bs-btn-disabled-color: #fff;
    --bs-btn-disabled-bg: #0052cc;
    --bs-btn-disabled-border-color: #0052cc;
}

.btn-bitbucket,
.btn-bitbucket.disabled,
.btn-bitbucket.focus,
.btn-bitbucket.hover:not(:disabled):not(.disabled),
.btn-bitbucket:disabled,
.btn-bitbucket:focus,
.btn-bitbucket:hover:not(:disabled):not(.disabled),
.show > .btn-bitbucket.dropdown-toggle {
    color: #fff;
}

.btn-light,
.btn-light.disabled,
.btn-light.focus,
.btn-light.hover:not(:disabled):not(.disabled),
.btn-light:disabled,
.btn-light:focus,
.btn-light:hover:not(:disabled):not(.disabled),
.btn-white,
.btn-white.disabled,
.btn-white.focus,
.btn-white.hover:not(:disabled):not(.disabled),
.btn-white:disabled,
.btn-white:focus,
.btn-white:hover:not(:disabled):not(.disabled),
.show > .btn-light.dropdown-toggle,
.show > .btn-white.dropdown-toggle {
    color: #343a40;
}

.card {
    box-shadow: 0 0 0.875rem 0 rgba(33, 37, 41, 0.05);
    margin-bottom: 24px;
}

.card-header {
    border-bottom-width: 1px;
}

.card-title {
    color: #939ba2;
    font-size: 0.925rem;
    font-weight: 600;
}

.card-subtitle {
    font-weight: 400;
}

.card-img,
.card-img-bottom,
.card-img-top {
    height: auto;
    max-width: 100%;
}

@media (-ms-high-contrast: none) {
    .card-img,
    .card-img-bottom,
    .card-img-top {
        height: 100%;
    }
}

.card > .table > tbody tr:last-child td:first-child,
.card > .table > tbody tr:last-child th:first-child {
    border-bottom-left-radius: 0.25rem;
}

.card > .table > tbody tr:last-child td:last-child,
.card > .table > tbody tr:last-child th:last-child {
    border-bottom-right-radius: 0.25rem;
}

.chart {
    margin: auto;
    min-height: 300px;
    position: relative;
    width: 100%;
}

.chart-xs {
    min-height: 200px;
}

.chart-sm {
    min-height: 252px;
}

.chart-lg {
    min-height: 350px;
}

.chart-xl {
    min-height: 500px;
}

.chart canvas {
    max-width: 100%;
}

.content {
    direction: ltr;
    flex: 1;
    max-width: 100vw;
    padding: 1.5rem 1.5rem 0.75rem !important;
    width: 100vw;
}

@media (min-width: 768px) {
    .content {
        max-width: auto;
        width: auto;
    }
}

@media (min-width: 992px) {
    .content {
        padding: 1.5rem 1.5rem 1.5rem;
    }
}

.navbar-nav .dropdown-menu {
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.05);
}
/* 
.dropdown .dropdown-menu.show {
    animation-duration: 0.25s;
    animation-fill-mode: forwards;
    animation-iteration-count: 1;
    animation-name: dropdownAnimation;
    animation-timing-function: ease;
}

@keyframes dropdownAnimation {
    0% {
        opacity: 0;
        transform: translateY(-8px);
    }

    to {
        opacity: 1;
        transform: translate(0);
    }
}

.dropdown-toggle:after {
    border: solid;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 2px;
    transform: rotate(45deg);
}

.dropdown-item {
    transition:
        background 0.1s ease-in-out,
        color 0.1s ease-in-out;
}

.dropdown-menu {
    top: auto;
}

.dropdown-menu-lg {
    min-width: 20rem;
}

.dropdown .list-group .list-group-item {
    border-width: 0 0 1px;
    margin-bottom: 0;
}

.dropdown .list-group .list-group-item:first-child,
.dropdown .list-group .list-group-item:last-child {
    border-radius: 0;
}

.dropdown .list-group .list-group-item:hover {
    background: #f8f9fa;
}

.dropdown-menu-header {
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 0.75rem;
    text-align: center;
}

.dropdown-menu-footer {
    display: block;
    font-size: 0.75rem;
    padding: 0.5rem;
    text-align: center;
}

.feather {
    stroke-width: 2;
    height: 18px;
    width: 18px;
}

.feather-sm {
    height: 14px;
    width: 14px;
}

.feather-lg {
    height: 36px;
    width: 36px;
} */

footer.footer {
    background: #fff;
    direction: ltr;
    padding: 1rem 0.875rem;
    box-shadow: 2rem 0 2rem 0 rgba(33, 37, 41, 0.1);
}

footer.footer ul {
    margin-bottom: 0;
}

@media (max-width: 767.98px) {
    footer.footer {
        width: 100vw;
    }
}

.hamburger,
.hamburger:after,
.hamburger:before {
    background: #495057;
    border-radius: 1px;
    content: "";
    cursor: pointer;
    display: block;
    height: 3px;
    transition:
        background 0.1s ease-in-out,
        color 0.1s ease-in-out;
    width: 24px;
}

.hamburger {
    position: relative;
}

.hamburger:before {
    position: absolute;
    top: -7.5px;
    width: 24px;
}

.hamburger:after {
    bottom: -7.5px;
    position: absolute;
    width: 16px;
}

.sidebar-toggle:hover .hamburger,
.sidebar-toggle:hover .hamburger:after,
.sidebar-toggle:hover .hamburger:before {
    background: #3b7ddd;
}

.hamburger-right,
.hamburger-right:after,
.hamburger-right:before {
    right: 0;
}

a.list-group-item {
    text-decoration: none;
}

.main {
    background: #f3f5fc;
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    min-width: 0;
    overflow: hidden;
    transition:
        margin-left 0.35s ease-in-out,
        left 0.35s ease-in-out,
        margin-right 0.35s ease-in-out,
        right 0.35s ease-in-out;
    width: 100%;
}

.navbar {
    border-bottom: 0;
    box-shadow: 0 0 2rem 0 rgba(33, 37, 41, 0.1);
}

@media (max-width: 767.98px) {
    .navbar {
        width: 100vw;
    }
}

.navbar .avatar {
    margin-bottom: -15px;
    margin-top: -15px;
}

.navbar-nav {
    direction: ltr;
}

.navbar-align {
    margin-left: auto;
}

.navbar-bg {
    background: #fff;
}

.navbar-brand {
    color: #f8f9fa;
    display: block;
    font-size: 1.15rem;
    font-weight: 400;
    padding: 0.875rem 0;
}

.navbar-brand .feather,
.navbar-brand svg {
    color: #3b7ddd;
    height: 24px;
    margin-left: -0.15rem;
    margin-right: 0.375rem;
    margin-top: -0.375rem;
    width: 24px;
}

.nav-flag,
.nav-icon {
    color: #6c757d;
    display: block;
    font-size: 1.5rem;
    line-height: 1.4;
    padding: 0.1rem 0.8rem;
    transition:
        background 0.1s ease-in-out,
        color 0.1s ease-in-out;
}

.nav-flag:after,
.nav-icon:after {
    display: none !important;
}

.nav-flag.active,
.nav-flag:hover,
.nav-icon.active,
.nav-icon:hover {
    color: #3b7ddd;
}

.nav-flag .feather,
.nav-flag svg,
.nav-icon .feather,
.nav-icon svg {
    height: 20px;
    width: 20px;
}

.nav-item .indicator {
    background: #4864e1;
    border-radius: 50%;
    box-shadow: 0 0.1rem 0.2rem rgba(0, 0, 0, 0.05);
    color: #fff;
    display: block;
    font-size: 0.675rem;
    height: 18px;
    padding: 1px;
    position: absolute;
    right: -14px;
    text-align: center;
    top: -6px;
    transition: top 0.1s ease-out;
    width: 18px;
}

.nav-item:hover .indicator {
    top: -6px;
}

.nav-item a:focus {
    outline: 0;
}

@media (-ms-high-contrast: none), screen and (-ms-high-contrast: active) {
    .navbar .avatar {
        max-height: 47px;
    }
}

@media (max-width: 575.98px) {
    .navbar {
        padding: 0.75rem;
    }

    .nav-icon {
        padding: 0.1rem 0.75rem;
    }

    .dropdown,
    .dropleft,
    .dropright,
    .dropup {
        position: inherit;
    }

    .navbar-expand .navbar-nav .dropdown-menu-lg {
        min-width: 100%;
    }

    .nav-item .nav-link:after {
        display: none;
    }
}

.nav-flag img {
    border-radius: 50%;
    height: 20px;
    -o-object-fit: cover;
    object-fit: cover;
    width: 20px;
}

.navbar input {
    direction: ltr;
}

#root,
body,
html {
    height: 100%;
}

body {
    opacity: 1 !important;
    overflow-y: scroll;
}

@media (-ms-high-contrast: none), screen and (-ms-high-contrast: active) {
    html {
        overflow-x: hidden;
    }
}

.sidebar {
    direction: ltr;
    max-width: 280px;
    min-width: 280px;
}

.sidebar,
.sidebar-content {
    background: #ffffff;
    transition:
        margin-left 0.35s ease-in-out,
        left 0.35s ease-in-out,
        margin-right 0.35s ease-in-out,
        right 0.35s ease-in-out;
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.sidebar-nav {
    flex-grow: 1;
    list-style: none;
    margin-bottom: 0;
    padding-left: 0;
    padding-right: 15px;
    margin-top: 32px;
}

.sidebar-nav li {
    margin: 0 0 5px;
}

.sidebar-nav li:last-child {
    margin: 0;
}

.sidebar-link,
a.sidebar-link {
    background: #fff;
    border-left: 3px solid transparent;
    cursor: pointer;
    display: block;
    font-weight: 400;
    padding: 0.625rem 1.625rem;
    position: relative;
    text-decoration: none;
    transition: background 0.1s ease-in-out;
    color: #262626;
    display: flex;
    padding: 15px 20px;
    border-radius: 0 50px 50px 0;
}

.sidebar-link i,
.sidebar-link svg,
a.sidebar-link i,
a.sidebar-link svg {
    color: rgba(233, 236, 239, 0.5);
    margin-right: 0.75rem;
}

.sidebar-link:focus {
    outline: 0;
}

.sidebar-link:hover {
    background: #4864e1;
    border-left-color: transparent;
}

.sidebar-link:hover,
.sidebar-link:hover i,
.sidebar-link:hover svg {
    color: #fff;
}

/* to align icon at center */
.sidebar-item > .sidebar-link {
    align-items: center;
}

.sidebar-item.active .sidebar-link:hover,
.sidebar-item.active > .sidebar-link {
    background: #f00;
    border-left-color: #ffbc0f;
    color: #fff;
    display: flex;
    padding: 15px 20px;
}

.sidebar-item.active .sidebar-link:hover i,
.sidebar-item.active .sidebar-link:hover svg,
.sidebar-item.active > .sidebar-link i,
.sidebar-item.active > .sidebar-link svg {
    color: #e9ecef;
}

.sidebar-brand {
    color: #f8f9fa;
    background-color: #f2f5ff;
    display: block;
    font-size: 1.15rem;
    font-weight: 600;
    padding: 1rem 1.5rem;
}

.sidebar-brand:hover {
    color: #f8f9fa;
    text-decoration: none;
}

.sidebar-brand:focus {
    outline: 0;
}

.sidebar-toggle {
    cursor: pointer;
    display: flex;
    height: 26px;
    width: 26px;
}

.sidebar.collapsed {
    margin-left: -260px;
}

@media (min-width: 1px) and (max-width: 991.98px) {
    .sidebar {
        margin-left: -260px;
    }

    .sidebar.collapsed {
        margin-left: 0;
    }
}

.sidebar-toggle {
    margin-right: 1rem;
}

.sidebar-header {
    background: transparent;
    color: #ced4da;
    font-size: 0.75rem;
    padding: 1.5rem 1.5rem 0.375rem;
}

.sidebar-badge {
    position: absolute;
    right: 15px;
    top: 14px;
    z-index: 1;
}

.sidebar-cta-content {
    background: #2b3947;
    border-radius: 0.3rem;
    color: #e9ecef;
    margin: 1.75rem;
    padding: 1.5rem;
}

.stat {
    align-items: center;
    background: #d3e2f7;
    border-radius: 50%;
    display: flex;
    height: 40px;
    justify-content: center;
    width: 40px;
}

.stat svg {
    stroke-width: 1.5;
    color: #3b7ddd !important;
    height: 18px;
    width: 18px;
}

.wrapper {
    align-items: stretch;
    background: #222e3c;
    display: flex;
    width: 100%;
}

.jvm-zoomin,
.jvm-zoomout,
image,
text {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.jvm-container {
    height: 100%;
    overflow: hidden;
    position: relative;
    touch-action: none;
    width: 100%;
}

.jvm-tooltip {
    background-color: #5c5cff;
    border-radius: 3px;
    box-shadow: 1px 2px 12px rgba(0, 0, 0, 0.2);
    color: #fff;
    display: none;
    font-family: var(--theme-font-family);
    font-size: smaller;
    padding: 3px 5px;
    position: absolute;
    white-space: nowrap;
}

.jvm-tooltip.active {
    display: block;
}

.jvm-zoom-btn {
    background-color: #292929;
    border-radius: 3px;
    box-sizing: border-box;
    color: #fff;
    cursor: pointer;
    height: 15px;
    left: 10px;
    line-height: 10px;
    padding: 3px;
    position: absolute;
    width: 15px;
}

.jvm-zoom-btn.jvm-zoomout {
    top: 30px;
}

.jvm-zoom-btn.jvm-zoomin {
    top: 10px;
}

.jvm-series-container {
    position: absolute;
    right: 15px;
}

.jvm-series-container.jvm-series-h {
    bottom: 15px;
}

.jvm-series-container.jvm-series-v {
    top: 15px;
}

.jvm-series-container .jvm-legend {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    float: left;
    margin-left: 0.75rem;
    padding: 0.6rem;
}

.jvm-series-container .jvm-legend .jvm-legend-title {
    border-bottom: 1px solid #e5e7eb;
    line-height: 1;
    margin-bottom: 0.575rem;
    padding-bottom: 0.5rem;
    text-align: left;
}

.jvm-series-container .jvm-legend .jvm-legend-inner {
    overflow: hidden;
}

.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {
    min-width: 40px;
    overflow: hidden;
}

.jvm-series-container
    .jvm-legend
    .jvm-legend-inner
    .jvm-legend-tick:not(:first-child) {
    margin-top: 0.575rem;
}

.jvm-series-container
    .jvm-legend
    .jvm-legend-inner
    .jvm-legend-tick
    .jvm-legend-tick-sample {
    border-radius: 4px;
    float: left;
    height: 16px;
    margin-right: 0.65rem;
    width: 16px;
}

.jvm-series-container
    .jvm-legend
    .jvm-legend-inner
    .jvm-legend-tick
    .jvm-legend-tick-text {
    float: left;
    font-size: 14px;
    text-align: center;
}

.jvm-line[animation="true"] {
    animation: jvm-line-animation 10s linear infinite forwards;
}

@keyframes jvm-line-animation {
    0% {
        stroke-dashoffset: 250;
    }
}

[data-simplebar] {
    align-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    position: relative;
}

.simplebar-wrapper {
    height: inherit;
    max-height: inherit;
    max-width: inherit;
    overflow: hidden;
    width: inherit;
}

.simplebar-mask {
    direction: inherit;
    height: auto !important;
    overflow: hidden;
    width: auto !important;
    z-index: 0;
}

.simplebar-mask,
.simplebar-offset {
    bottom: 0;
    left: 0;
    margin: 0;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
}

.simplebar-offset {
    -webkit-overflow-scrolling: touch;
    box-sizing: inherit !important;
    direction: inherit !important;
    resize: none !important;
}

.simplebar-content-wrapper {
    -ms-overflow-style: none;
    box-sizing: border-box !important;
    direction: inherit;
    display: block;
    height: 100%;
    max-height: 100%;
    max-width: 100%;
    position: relative;
    scrollbar-width: none;
    width: auto;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
    display: none;
    height: 0;
    width: 0;
}

.simplebar-content:after,
.simplebar-content:before {
    content: " ";
    display: table;
}

.simplebar-placeholder {
    max-height: 100%;
    max-width: 100%;
    pointer-events: none;
    width: 100%;
}

.simplebar-height-auto-observer-wrapper {
    box-sizing: inherit !important;
    flex-basis: 0;
    flex-grow: inherit;
    flex-shrink: 0;
    float: left;
    height: 100%;
    margin: 0;
    max-height: 1px;
    max-width: 1px;
    overflow: hidden;
    padding: 0;
    pointer-events: none;
    position: relative;
    width: 100%;
    z-index: -1;
}

.simplebar-height-auto-observer {
    box-sizing: inherit;
    display: block;
    height: 1000%;
    left: 0;
    min-height: 1px;
    min-width: 1px;
    opacity: 0;
    top: 0;
    width: 1000%;
    z-index: -1;
}

.simplebar-height-auto-observer,
.simplebar-track {
    overflow: hidden;
    pointer-events: none;
    position: absolute;
}

.simplebar-track {
    bottom: 0;
    right: 0;
    z-index: 1;
}

[data-simplebar].simplebar-dragging .simplebar-content {
    pointer-events: none;
    -moz-user-select: none;
    user-select: none;
    -webkit-user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
    pointer-events: all;
}

.simplebar-scrollbar {
    left: 0;
    min-height: 10px;
    position: absolute;
    right: 0;
}

.simplebar-scrollbar:before {
    background: #000;
    border-radius: 7px;
    content: "";
    left: 2px;
    opacity: 0;
    position: absolute;
    right: 2px;
    transition: opacity 0.2s linear;
}

.simplebar-scrollbar.simplebar-visible:before {
    opacity: 0.5;
    transition: opacity 0s linear;
}

.simplebar-track.simplebar-vertical {
    top: 0;
    width: 11px;
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
    bottom: 2px;
    top: 2px;
}

.simplebar-track.simplebar-horizontal {
    height: 11px;
    left: 0;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
    height: 100%;
    left: 2px;
    right: 2px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
    height: 7px;
    left: 0;
    min-height: 0;
    min-width: 10px;
    right: auto;
    top: 2px;
    width: auto;
}

[data-simplebar-direction="rtl"] .simplebar-track.simplebar-vertical {
    left: 0;
    right: auto;
}

.hs-dummy-scrollbar-size {
    direction: rtl;
    height: 500px;
    opacity: 0;
    overflow-x: scroll;
    overflow-y: hidden;
    position: fixed;
    visibility: hidden;
    width: 500px;
}

.simplebar-hide-scrollbar {
    -ms-overflow-style: none;
    left: 0;
    overflow-y: scroll;
    position: fixed;
    scrollbar-width: none;
    visibility: hidden;
}

.flatpickr-calendar {
    animation: none;
    background: transparent;
    background: #fff;
    border: 0;
    border-radius: 5px;
    box-shadow:
        1px 0 0 #e6e6e6,
        -1px 0 0 #e6e6e6,
        0 1px 0 #e6e6e6,
        0 -1px 0 #e6e6e6,
        0 3px 13px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    direction: ltr;
    display: none;
    font-size: 14px;
    line-height: 24px;
    opacity: 0;
    padding: 0;
    position: absolute;
    text-align: center;
    touch-action: manipulation;
    visibility: hidden;
    width: 307.875px;
}

.flatpickr-calendar.inline,
.flatpickr-calendar.open {
    max-height: 640px;
    opacity: 1;
    visibility: visible;
}

.flatpickr-calendar.open {
    display: inline-block;
    z-index: 99999;
}

.flatpickr-calendar.animate.open {
    animation: fpFadeInDown 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.flatpickr-calendar.inline {
    display: block;
    position: relative;
    top: 2px;
}

.flatpickr-calendar.static {
    position: absolute;
    top: calc(100% + 2px);
}

.flatpickr-calendar.static.open {
    display: block;
    z-index: 999;
}

.flatpickr-calendar.multiMonth
    .flatpickr-days
    .dayContainer:nth-child(n + 1)
    .flatpickr-day.inRange:nth-child(7n + 7) {
    box-shadow: none !important;
}

.flatpickr-calendar.multiMonth
    .flatpickr-days
    .dayContainer:nth-child(n + 2)
    .flatpickr-day.inRange:nth-child(7n + 1) {
    box-shadow:
        -2px 0 0 #e6e6e6,
        5px 0 0 #e6e6e6;
}

.flatpickr-calendar .hasTime .dayContainer,
.flatpickr-calendar .hasWeeks .dayContainer {
    border-bottom: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.flatpickr-calendar .hasWeeks .dayContainer {
    border-left: 0;
}

.flatpickr-calendar.hasTime .flatpickr-time {
    border-top: 1px solid #e6e6e6;
    height: 40px;
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
    height: auto;
}

.flatpickr-calendar:after,
.flatpickr-calendar:before {
    border: solid transparent;
    content: "";
    display: block;
    height: 0;
    left: 22px;
    pointer-events: none;
    position: absolute;
    width: 0;
}

.flatpickr-calendar.arrowRight:after,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.rightMost:before {
    left: auto;
    right: 22px;
}

.flatpickr-calendar.arrowCenter:after,
.flatpickr-calendar.arrowCenter:before {
    left: 50%;
    right: 50%;
}

.flatpickr-calendar:before {
    border-width: 5px;
    margin: 0 -5px;
}

.flatpickr-calendar:after {
    border-width: 4px;
    margin: 0 -4px;
}

.flatpickr-calendar.arrowTop:after,
.flatpickr-calendar.arrowTop:before {
    bottom: 100%;
}

.flatpickr-calendar.arrowTop:before {
    border-bottom-color: #e6e6e6;
}

.flatpickr-calendar.arrowTop:after {
    border-bottom-color: #fff;
}

.flatpickr-calendar.arrowBottom:after,
.flatpickr-calendar.arrowBottom:before {
    top: 100%;
}

.flatpickr-calendar.arrowBottom:before {
    border-top-color: #e6e6e6;
}

.flatpickr-calendar.arrowBottom:after {
    border-top-color: #fff;
}

.flatpickr-calendar:focus {
    outline: 0;
}

.flatpickr-wrapper {
    display: inline-block;
    position: relative;
}

.flatpickr-months {
    display: flex;
}

.flatpickr-months .flatpickr-month {
    background: transparent;
    flex: 1;
    line-height: 1;
    overflow: hidden;
    position: relative;
    text-align: center;
}

.flatpickr-months .flatpickr-month,
.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    fill: rgba(0, 0, 0, 0.9);
    color: rgba(0, 0, 0, 0.9);
    height: 34px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.flatpickr-months .flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month {
    cursor: pointer;
    padding: 10px;
    position: absolute;
    text-decoration: none;
    top: 0;
    z-index: 3;
}

.flatpickr-months .flatpickr-next-month.flatpickr-disabled,
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled {
    display: none;
}

.flatpickr-months .flatpickr-next-month i,
.flatpickr-months .flatpickr-prev-month i {
    position: relative;
}

.flatpickr-months .flatpickr-next-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month {
    left: 0;
}

.flatpickr-months .flatpickr-next-month.flatpickr-next-month,
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month {
    right: 0;
}

.flatpickr-months .flatpickr-next-month:hover,
.flatpickr-months .flatpickr-prev-month:hover {
    color: #959ea9;
}

.flatpickr-months .flatpickr-next-month:hover svg,
.flatpickr-months .flatpickr-prev-month:hover svg {
    fill: #f64747;
}

.flatpickr-months .flatpickr-next-month svg,
.flatpickr-months .flatpickr-prev-month svg {
    height: 14px;
    width: 14px;
}

.flatpickr-months .flatpickr-next-month svg path,
.flatpickr-months .flatpickr-prev-month svg path {
    fill: inherit;
    transition: fill 0.1s;
}

.numInputWrapper {
    height: auto;
    position: relative;
}

.numInputWrapper input,
.numInputWrapper span {
    display: inline-block;
}

.numInputWrapper input {
    width: 100%;
}

.numInputWrapper input::-ms-clear {
    display: none;
}

.numInputWrapper input::-webkit-inner-spin-button,
.numInputWrapper input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.numInputWrapper span {
    border: 1px solid rgba(57, 57, 57, 0.15);
    box-sizing: border-box;
    cursor: pointer;
    height: 50%;
    line-height: 50%;
    opacity: 0;
    padding: 0 4px 0 2px;
    position: absolute;
    right: 0;
    width: 14px;
}

.numInputWrapper span:hover {
    background: rgba(0, 0, 0, 0.1);
}

.numInputWrapper span:active {
    background: rgba(0, 0, 0, 0.2);
}

.numInputWrapper span:after {
    content: "";
    display: block;
    position: absolute;
}

.numInputWrapper span.arrowUp {
    border-bottom: 0;
    top: 0;
}

.numInputWrapper span.arrowUp:after {
    border-bottom: 4px solid rgba(57, 57, 57, 0.6);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    top: 26%;
}

.numInputWrapper span.arrowDown {
    top: 50%;
}

.numInputWrapper span.arrowDown:after {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid rgba(57, 57, 57, 0.6);
    top: 40%;
}

.numInputWrapper span svg {
    height: auto;
    width: inherit;
}

.numInputWrapper span svg path {
    fill: rgba(0, 0, 0, 0.5);
}

.numInputWrapper:hover {
    background: rgba(0, 0, 0, 0.05);
}

.numInputWrapper:hover span {
    opacity: 1;
}

.flatpickr-current-month {
    color: inherit;
    display: inline-block;
    font-size: 135%;
    font-weight: 300;
    height: 34px;
    left: 12.5%;
    line-height: inherit;
    line-height: 1;
    padding: 7.48px 0 0;
    position: absolute;
    text-align: center;
    transform: translateZ(0);
    width: 75%;
}

.flatpickr-current-month span.cur-month {
    color: inherit;
    display: inline-block;
    font-family: var(--theme-font-family);
    font-weight: 700;
    margin-left: 0.5ch;
    padding: 0;
}

.flatpickr-current-month span.cur-month:hover {
    background: rgba(0, 0, 0, 0.05);
}

.flatpickr-current-month .numInputWrapper {
    display: inline-block;
    width: 6ch;
    width: 7ch\0;
}

.flatpickr-current-month .numInputWrapper span.arrowUp:after {
    border-bottom-color: rgba(0, 0, 0, 0.9);
}

.flatpickr-current-month .numInputWrapper span.arrowDown:after {
    border-top-color: rgba(0, 0, 0, 0.9);
}

.flatpickr-current-month input.cur-year {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
    background: transparent;
    border: 0;
    border-radius: 0;
    box-sizing: border-box;
    color: inherit;
    cursor: text;
    display: inline-block;
    font-family: var(--theme-font-family);
    font-size: inherit;
    font-weight: 300;
    height: auto;
    line-height: inherit;
    margin: 0;
    padding: 0 0 0 0.5ch;
    vertical-align: initial;
}

.flatpickr-current-month input.cur-year:focus {
    outline: 0;
}

.flatpickr-current-month input.cur-year[disabled],
.flatpickr-current-month input.cur-year[disabled]:hover {
    background: transparent;
    color: rgba(0, 0, 0, 0.5);
    font-size: 100%;
    pointer-events: none;
}

.flatpickr-current-month .flatpickr-monthDropdown-months {
    appearance: menulist;
    -webkit-appearance: menulist;
    -moz-appearance: menulist;
    background: transparent;
    border: none;
    border-radius: 0;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    color: inherit;
    cursor: pointer;
    font-family: var(--theme-font-family);
    font-size: inherit;
    font-weight: 300;
    height: auto;
    line-height: inherit;
    margin: -1px 0 0;
    outline: none;
    padding: 0 0 0 0.5ch;
    position: relative;
    vertical-align: initial;
    width: auto;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:active,
.flatpickr-current-month .flatpickr-monthDropdown-months:focus {
    outline: none;
}

.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
    background: rgba(0, 0, 0, 0.05);
}

.flatpickr-current-month
    .flatpickr-monthDropdown-months
    .flatpickr-monthDropdown-month {
    background-color: transparent;
    outline: none;
    padding: 0;
}

.flatpickr-weekdays {
    align-items: center;
    background: transparent;
    display: flex;
    height: 28px;
    overflow: hidden;
    text-align: center;
    width: 100%;
}

.flatpickr-weekdays .flatpickr-weekdaycontainer {
    display: flex;
    flex: 1;
}

span.flatpickr-weekday {
    background: transparent;
    color: rgba(0, 0, 0, 0.54);
    cursor: default;
    display: block;
    flex: 1;
    font-size: 90%;
    font-weight: bolder;
    line-height: 1;
    margin: 0;
    text-align: center;
}

.dayContainer,
.flatpickr-weeks {
    padding: 1px 0 0;
}

.flatpickr-days {
    align-items: flex-start;
    display: flex;
    overflow: hidden;
    position: relative;
    width: 307.875px;
}

.flatpickr-days:focus {
    outline: 0;
}

.dayContainer {
    box-sizing: border-box;
    display: inline-block;
    display: flex;
    flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    justify-content: space-around;
    max-width: 307.875px;
    min-width: 307.875px;
    opacity: 1;
    outline: 0;
    padding: 0;
    text-align: left;
    transform: translateZ(0);
    width: 307.875px;
}

.dayContainer + .dayContainer {
    box-shadow: -1px 0 0 #e6e6e6;
}

.flatpickr-day {
    background: none;
    border: 1px solid transparent;
    border-radius: 150px;
    box-sizing: border-box;
    color: #393939;
    cursor: pointer;
    display: inline-block;
    flex-basis: 14.2857143%;
    font-weight: 400;
    height: 39px;
    justify-content: center;
    line-height: 39px;
    margin: 0;
    max-width: 39px;
    position: relative;
    text-align: center;
    width: 14.2857143%;
}

.flatpickr-day.inRange,
.flatpickr-day.nextMonthDay.inRange,
.flatpickr-day.nextMonthDay.today.inRange,
.flatpickr-day.nextMonthDay:focus,
.flatpickr-day.nextMonthDay:hover,
.flatpickr-day.prevMonthDay.inRange,
.flatpickr-day.prevMonthDay.today.inRange,
.flatpickr-day.prevMonthDay:focus,
.flatpickr-day.prevMonthDay:hover,
.flatpickr-day.today.inRange,
.flatpickr-day:focus,
.flatpickr-day:hover {
    background: #e6e6e6;
    border-color: #e6e6e6;
    cursor: pointer;
    outline: 0;
}

.flatpickr-day.today {
    border-color: #959ea9;
}

.flatpickr-day.today:focus,
.flatpickr-day.today:hover {
    background: #959ea9;
    border-color: #959ea9;
    color: #fff;
}

.flatpickr-day.endRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.endRange.nextMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.endRange:focus,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected,
.flatpickr-day.selected.inRange,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.selected:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.startRange:focus,
.flatpickr-day.startRange:hover {
    background: #569ff7;
    border-color: #569ff7;
    box-shadow: none;
    color: #fff;
}

.flatpickr-day.endRange.startRange,
.flatpickr-day.selected.startRange,
.flatpickr-day.startRange.startRange {
    border-radius: 50px 0 0 50px;
}

.flatpickr-day.endRange.endRange,
.flatpickr-day.selected.endRange,
.flatpickr-day.startRange.endRange {
    border-radius: 0 50px 50px 0;
}

.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n + 1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n + 1)) {
    box-shadow: -10px 0 0 #569ff7;
}

.flatpickr-day.endRange.startRange.endRange,
.flatpickr-day.selected.startRange.endRange,
.flatpickr-day.startRange.startRange.endRange {
    border-radius: 50px;
}

.flatpickr-day.inRange {
    border-radius: 0;
    box-shadow:
        -5px 0 0 #e6e6e6,
        5px 0 0 #e6e6e6;
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover,
.flatpickr-day.nextMonthDay,
.flatpickr-day.notAllowed,
.flatpickr-day.notAllowed.nextMonthDay,
.flatpickr-day.notAllowed.prevMonthDay,
.flatpickr-day.prevMonthDay {
    background: transparent;
    border-color: transparent;
    color: rgba(57, 57, 57, 0.3);
    cursor: default;
}

.flatpickr-day.flatpickr-disabled,
.flatpickr-day.flatpickr-disabled:hover {
    color: rgba(57, 57, 57, 0.1);
    cursor: not-allowed;
}

.flatpickr-day.week.selected {
    border-radius: 0;
    box-shadow:
        -5px 0 0 #569ff7,
        5px 0 0 #569ff7;
}

.flatpickr-day.hidden {
    visibility: hidden;
}

.rangeMode .flatpickr-day {
    margin-top: 1px;
}

.flatpickr-weekwrapper {
    float: left;
}

.flatpickr-weekwrapper .flatpickr-weeks {
    box-shadow: 1px 0 0 #e6e6e6;
    padding: 0 12px;
}

.flatpickr-weekwrapper .flatpickr-weekday {
    float: none;
    line-height: 28px;
    width: 100%;
}

.flatpickr-weekwrapper span.flatpickr-day,
.flatpickr-weekwrapper span.flatpickr-day:hover {
    background: transparent;
    border: none;
    color: rgba(57, 57, 57, 0.3);
    cursor: default;
    display: block;
    max-width: none;
    width: 100%;
}

.flatpickr-innerContainer {
    box-sizing: border-box;
    display: block;
    display: flex;
    overflow: hidden;
}

.flatpickr-rContainer {
    box-sizing: border-box;
    display: inline-block;
    padding: 0;
}

.flatpickr-time {
    box-sizing: border-box;
    display: block;
    display: flex;
    height: 0;
    line-height: 40px;
    max-height: 40px;
    outline: 0;
    overflow: hidden;
    text-align: center;
}

.flatpickr-time:after {
    clear: both;
    content: "";
    display: table;
}

.flatpickr-time .numInputWrapper {
    flex: 1;
    float: left;
    height: 40px;
    width: 40%;
}

.flatpickr-time .numInputWrapper span.arrowUp:after {
    border-bottom-color: #393939;
}

.flatpickr-time .numInputWrapper span.arrowDown:after {
    border-top-color: #393939;
}

.flatpickr-time.hasSeconds .numInputWrapper {
    width: 26%;
}

.flatpickr-time.time24hr .numInputWrapper {
    width: 49%;
}

.flatpickr-time input {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;
    background: transparent;
    border: 0;
    border-radius: 0;
    box-shadow: none;
    box-sizing: border-box;
    color: #393939;
    font-size: 14px;
    height: inherit;
    line-height: inherit;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center;
}

.flatpickr-time input.flatpickr-hour {
    font-weight: 700;
}

.flatpickr-time input.flatpickr-minute,
.flatpickr-time input.flatpickr-second {
    font-weight: 400;
}

.flatpickr-time input:focus {
    border: 0;
    outline: 0;
}

.flatpickr-time .flatpickr-am-pm,
.flatpickr-time .flatpickr-time-separator {
    align-self: center;
    color: #393939;
    float: left;
    font-weight: 700;
    height: inherit;
    line-height: inherit;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    width: 2%;
}

.flatpickr-time .flatpickr-am-pm {
    cursor: pointer;
    font-weight: 400;
    outline: 0;
    text-align: center;
    width: 18%;
}

.flatpickr-time .flatpickr-am-pm:focus,
.flatpickr-time .flatpickr-am-pm:hover,
.flatpickr-time input:focus,
.flatpickr-time input:hover {
    background: #eee;
}

.flatpickr-input[readonly] {
    cursor: pointer;
}

@keyframes fpFadeInDown {
    0% {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }

    to {
        opacity: 1;
        transform: translateZ(0);
    }
}

.flatpickr-calendar.inline {
    background: transparent;
    box-shadow: none;
    width: 100%;
}

.flatpickr-calendar.inline .flatpickr-days {
    width: 100%;
}

.flatpickr-calendar.inline .dayContainer {
    max-width: 100%;
    min-width: 100%;
    width: 100%;
}

.flatpickr-calendar.inline .flatpickr-day {
    border-radius: 0.2rem;
    height: 45px;
    line-height: 45px;
    max-width: inherit;
}

.flatpickr-calendar.inline .flatpickr-day.today {
    border: 0;
}

.flatpickr-calendar.inline .flatpickr-day.today:before {
    border-color: rgba(0, 0, 0, 0.2) transparent #3b7ddd;
    border-style: solid;
    border-width: 0 0 7px 7px;
    bottom: 4px;
    content: "";
    display: inline-block;
    position: absolute;
    right: 4px;
}

.flatpickr-calendar.inline .flatpickr-day.today.selected:before {
    border-color: rgba(0, 0, 0, 0.2) transparent #fff;
}

.flatpickr-calendar.inline .flatpickr-day.today:hover {
    background: #e6e6e6;
    color: #000;
}

.flatpickr-calendar.inline .flatpickr-day.selected,
.flatpickr-calendar.inline .flatpickr-day.selected:focus,
.flatpickr-calendar.inline .flatpickr-day.selected:hover {
    background: #3b7ddd;
    border-radius: 0.2rem;
    color: #fff;
}

.flatpickr-calendar.inline .flatpickr-weekdays {
    height: 45px;
}

.flatpickr-calendar.inline .flatpickr-weekday {
    height: 45px;
    line-height: 45px;
}

.flatpickr-calendar.inline .flatpickr-months .flatpickr-month,
.flatpickr-calendar.inline .flatpickr-months .flatpickr-next-month,
.flatpickr-calendar.inline .flatpickr-months .flatpickr-prev-month {
    height: 45px;
}

.flatpickr-calendar.inline .flatpickr-current-month {
    height: 45px;
    line-height: 45px;
    padding-top: 0;
}

.flatpickr-calendar.inline
    .flatpickr-current-month
    .flatpickr-monthDropdown-months {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.flatpickr-calendar.inline
    .flatpickr-current-month
    .flatpickr-monthDropdown-months,
.flatpickr-calendar.inline .flatpickr-current-month input.cur-year {
    font-size: 1.09375rem;
    font-weight: 400;
}

.flatpickr-calendar.inline .flatpickr-next-month,
.flatpickr-calendar.inline .flatpickr-prev-month {
    border-radius: 0.2rem;
    width: 45px;
}

.flatpickr-calendar.inline .flatpickr-next-month:hover,
.flatpickr-calendar.inline .flatpickr-prev-month:hover {
    background: #e6e6e6;
    color: #000;
}

.simplebar-scrollbar:before {
    background: #fff;
}

.simplebar-content {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding-bottom: 0 !important;
}

[data-simplebar] {
    left: 0;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    width: 260px;
}

.btn {
    font-size: 16px !important;
    font-weight: 300 !important;
}

.select-wrapper {
    position: relative;
}

.select-arrow {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    pointer-events: none;
}

.input-wrapper {
    position: relative;
}

.search-icon {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    pointer-events: none;
}

.input-with-icon {
    padding-left: 40px; /* Adjust padding to accommodate the icon */
}

/*# sourceMappingURL=app.css.map*/

.check-icon {
    height: 14px !important;
    width: auto !important;
}
.no-underline {
    text-decoration: none;
}
.download-btn {
    border-radius: 0.2rem;
    border-color: #e5252a;
    background-color: #e5252a;
    font-size: 16px;
    font-weight: 300;
    color: #fff;
    white-space: nowrap;
    padding: 5px 15px;
}

.heading-clr {
    font-size: 1.09375rem;
}
.tlgt-btn {
    display: flex;
    align-items: center;
}
.dataTable {
    overflow: inherit !important;
}
@media screen and (max-width: 1535px) {
    .tlgt-btn {
        flex-wrap: wrap;
    }
}

@media screen and (max-width: 1199px) {
    .row-gap-mobile {
        row-gap: 15px;
    }
}

.casndi-contact {
    position: relative;
    padding-left: 50px;
}

.casndi-contact span {
    position: absolute;
    left: 0;
    top: 22px;
}
