.offer-letter-editor .ql-container {
    height: calc(30vh); /* Set desired height */
    min-height: 400px;
    border: 1px solid #ccc;
    overflow-y: auto; /* Enables scrolling for content */
    position: relative;
}

/* Make toolbar sticky */
.offer-letter-editor .ql-container .ql-toolbar {
    position: sticky;
    top: 0;
    background: white;
    z-index: 10;
    border-bottom: 1px solid #ddd;
}

.offer-letter-form-label {
    font-size: 14px;
    color: #000;
    font-weight: 500;
}

/* Style for the offer letter signature canvas section */
.offer-letter-canvas-body {
    padding: 24px 24px 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid #ececec;
    border-radius: 0 0 8px 8px;
    margin-top: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.offer-letter-canvas-body > div:first-child {
    font-weight: 500;
    /* color: #495057; */
    margin-bottom: 8px;
}

.offer-letter-canvas-body .sigCanvas {
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: #fff;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}
.offer-letter-canvas-body .sigCanvas canvas {
    width: 100%;
    height: 200px;
    border-radius: 4px;
}
.offer-letter-canvas-body .sigCanvas canvas.sigCanvas {
    border: none;
    background: #fff;
}
.signature-box-btn {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.ql-syntax {
    font-family: monospace !important;
    font-size: 0.9em !important;
    background-color: #f5f5f5 !important;
    color: #333 !important;
    padding: 1em;
    overflow-x: auto;
    border-radius: 4px;
    white-space: pre;
    line-height: 1.4;
    all: unset; /* resets most inherited styles */
    display: block;
}

/* Re-apply what you want */
.ql-syntax {
    font-family: Consolas, Monaco, "Courier New", monospace;
    background-color: #f9f9f9;
    padding: 1em;
    overflow-x: auto;
    border-radius: 5px;
    color: #444;
}

/* styles/tiptap.css */
.tiptap {
    min-height: 200px;
    padding: 1rem;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-family: system-ui, sans-serif;
    line-height: 1.5;
    background: #fff;
    color: #333;
}

.tiptap p {
    margin: 0 0 1em;
}

.tiptap strong {
    font-weight: bold;
}

.tiptap em {
    font-style: italic;
}
