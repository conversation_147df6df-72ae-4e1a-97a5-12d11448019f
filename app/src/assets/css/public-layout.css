@import url("https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

html {
    scroll-behavior: smooth;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: "Poppins", sans-serif;
    font-style: normal;
    font-size: 16px;
    font-weight: 400;
}

.ant-modal .ant-modal-body,
.ant-btn {
    font-size: 16px;
}

@media screen and (max-width: 1366px) {
    body,
    .ant-modal .ant-modal-body,
    .ant-btn {
        font-size: 14px;
    }
}

/* .btn {
    font-size: 14px;
} */

section.public-layout img {
    max-width: 100%;
}

section.public-layout ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

section.public-layout p {
    color: var(--text);
}

section.public-layout a {
    text-decoration: none;
}

/*colors*/
:root {
    --primary: #4864e1;
    --primary-hover: #203ec5;
    --secondary: #060b24;
    --secondary-dark: #0b1541;
    --text: #262626;
    --heading: #000102;
    --lightbg: #f9f9f9;
}

section.public-layout .primary {
    color: var(--primary) !important;
}

section.public-layout .secondary {
    color: var(--primary) !important;
}

section.public-layout .text {
    color: var(--text) !important;
}

section.public-layout .heading {
    color: var(--heading);
}

/*background-color*/
section.public-layout .lightbg {
    background-color: var(--lightbg);
}

section.public-layout .primarybg {
    background-color: var(--primary);
}

section.public-layout .secondarybg {
    background-color: var(--primary);
}

/*fonts*/
section.public-layout .ft38 {
    font-size: 38px;
}

section.public-layout .ft32 {
    font-size: 32px;
}

section.public-layout .ft20 {
    font-size: 20px;
}

section.public-layout .ft16 {
    font-size: 16px;
}

section.public-layout .ft14 {
    font-size: 14px;
}

/*font-weight*/
section.public-layout .medium {
    font-weight: 600;
}

section.public-layout .semibold {
    font-weight: 700;
}

section.public-layout .bold {
    font-weight: 900;
}

/*button*/
section.public-layout .btn {
    height: 42px !important;
    border-radius: 50px;
    padding: 0 20px;
    border: none;
    background: white;
    color: var(--theme-primary-color);
    transition: all 0.3s ease;
}

.main-banner .banner-content .btn:hover {
    box-shadow: inset 0 0 0px 1px #ffffff;
    color: #fff;
    background: var(--primary);
}

section.public-layout .btn-primary {
    background-color: var(--primary) !important;
    background: var(--primary) !important;
    color: #fff !important;
    transition: all 0.3s ease;
}

section.public-layout .btn-primary:hover {
    background-color: #fff !important;
    background: #fff !important;
    color: #4864e1 !important;
    box-shadow: inset 0 0 0 2px #4864e1;
}

section.public-layout .btn-border {
    background-color: #fff !important;
    background: #fff !important;
}

/***** Home Page ******/

/*header*/
section.public-layout .py-80 {
    padding-top: 80px;
    padding-bottom: 80px;
}

section.public-layout header .navbar ul.navbar-nav {
    gap: 30px;
}

section.public-layout .main-navbar li a.btn,
section.public-layout .main-navbar li a.btn:hover {
    color: #000 !important;
    display: flex;
    align-items: center;
    padding: 0 15px;
    font-weight: 500;
}

section.public-layout .main-navbar li a,
section.public-layout .main-navbar li a:hover,
section.public-layout .main-navbar li a.active {
    color: #fff !important;
    font-weight: 400;
    position: relative;
    transition: all 0.3s ease;
}

section.public-layout .main-navbar li a.active::after {
    content: "";
    width: 100%;
    left: 0;
    right: 0;
    bottom: -21px;
    position: absolute;
    height: 3px;
    background: #ffffff;
}

section.public-layout .main-navbar li a.active {
    font-weight: 500 !important;
}

/*banner*/
section.public-layout .main-banner {
    padding: 110px 0 60px 0;
    overflow: hidden;
}

section.public-layout .banner-content h1 {
    font-weight: 700;
    font-size: 46px;
}
section.public-layout .banner-content a {
    background-color: #fff;
    border-color: #fff;
    color: var(--primary);
}
section.public-layout .banner-content a:hover {
    background-color: #fff;
    border-color: #fff;
    color: var(--primary);
}

/* .banner-vector img:nth-child(2) {
    bottom: -30px;
    right: 60px;
    z-index: 1;
    content: '';
    -webkit-box-shadow: 11px 12px 13px 0px rgb(2 77 100);
    -moz-box-shadow: 11px 12px 13px 0px rgb(2 77 100);
    box-shadow: 11px 12px 13px 0px rgb(2 77 100);
} */

section.public-layout .banner-vector img:last-child {
    z-index: 0;
    position: absolute;
    top: 30px;
    left: -200px;
    opacity: 0.8;
}

/*introduction-section*/
section.public-layout .intro-sec .container h3 {
    font-size: 38px;
}

section.public-layout .pointer-wrapper {
    background: white;
    padding: 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-wrapper:hover {
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-wrapper:hover > .pointer-img {
    background: var(--primary);
    transition: all 0.3s ease;
}

section.public-layout .pointer-wrapper:hover > .pointer-img img {
    filter: invert(1) brightness(9);
}

section.public-layout .pointer-content h4 {
    font-size: 20px;
}

section.public-layout .partners img {
    height: 40px;
    object-fit: contain;
}

section.public-layout .intro-sec .container .row {
    row-gap: 24px;
}

section.public-layout .pointer-img {
    min-width: 48px;
    width: 48px;
    height: 48px;
    border-radius: 30px;
    background: #fff;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .pointer-img img {
    width: 32px;
    height: 32px;
}

section.public-layout .section-head h3 {
    font-size: 38px;
    font-weight: 700;
}

/*key-features*/
section.public-layout .feature-card {
    padding: 40px 20px 60px;
    transition: all 0.2s ease-in-out;
    height: 100%;
}

section.public-layout .feature-card .feature-content p {
    margin: 0;
}

section.public-layout .feature-card:hover {
    background: var(--primary);
    transition: all 0.2s ease-in-out;
    box-shadow: 1px 1px 12px rgba(0, 0, 0, 0.24);
}

section.public-layout .feature-card:hover > .feature-content h4,
section.public-layout .feature-card:hover > .feature-content p {
    color: #fff;
}

section.public-layout .features .container .row {
    row-gap: 20px;
}

section.public-layout .feature-card:hover > .feature-img {
    box-shadow: 0px 7px 13px 0px rgb(43 66 167);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(43 66 167);
    -moz-box-shadow: 0px 7px 13px 0px rgb(43 66 167);
}

section.public-layout .feature-img {
    min-width: 96px;
    width: 96px;
    height: 96px;
    border-radius: 50px;
    background-color: white;
    box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -webkit-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
    -moz-box-shadow: 0px 7px 13px 0px rgb(228 234 237);
}

section.public-layout .feature-img img {
    width: 48px;
    height: 48px;
}

section.public-layout .feature-content h4 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 15px;
}

section.public-layout .common-heading h5 {
    font-size: 20px;
    font-weight: 500;
}

section.public-layout .common-heading {
    margin: 0 0 50px;
}

section.public-layout .common-heading p {
    margin: 0;
}

section.public-layout .common-heading h4 {
    font-size: 38px;
    margin: 0 0 16px;
}

/*Work-process*/
section.public-layout .process-icon {
    width: 40px;
    height: 40px;
}

section.public-layout .process-icon img {
    z-index: 1;
    transition: all 0.3s ease;
}

section.public-layout .process-icon::before {
    content: "";
    position: absolute;
    width: 32px;
    height: 32px;
    background: #e7ebff;
    opacity: 0.7;
    border-radius: 30px;
    z-index: 0;
    right: -10px;
    bottom: -10px;
    transition: all 0.3s ease;
}

section.public-layout .work-process .container .row {
    row-gap: 24px;
}

section.public-layout .work-process-card {
    padding: 20px;
    border: 1px solid #e3f3ee;
    background-color: #fff;
    transition: all 0.3s ease;
    height: 100%;
}

section.public-layout .work-process-card:hover {
    background: var(--primary);
}

section.public-layout .work-process-card:hover > .process-icon img {
    filter: invert(1);
}

section.public-layout .work-process-card:hover > .process-icon::before {
    background: #637be5;
}

section.public-layout .work-process-card:hover > .process-content h4,
section.public-layout .work-process-card:hover > .process-content p {
    color: #fff;
}

section.public-layout .work-process-card p.steps {
    font-size: 80px;
    color: #f5f5f5;
    position: absolute;
    top: -10px;
    right: 25px;
    width: 85px;
    height: 40px;
    font-weight: 500;
    transition: all 0.3s ease;
}
section.public-layout .pt-80 {
    padding-top: 80px;
}

section.public-layout .work-process-card:hover > p.steps {
    color: #637be5;
}

section.public-layout .process-vector-wrap-one {
    position: relative;
    border-radius: 0 40px 40px 40px;
    border-bottom: 10px solid var(--primary);
    padding: 40px 0;
}

section.public-layout .process-vector-wrap-two {
    position: relative;
    padding: 40px 0;
}

section.public-layout .process-vector-wrap-one {
    background: #f5f7ff;
    border-radius: 0 40px 40px 40px;
    border-bottom: 10px solid var(--primary);
}

section.public-layout .process-vector-wrap-two {
    border-radius: 0 40px 40px 40px;
    background: #fff;
    border-bottom: 10px solid #d1daff;
}

section.public-layout .process-vector-wrap-one span,
section.public-layout .process-vector-wrap-two span {
    width: 70px;
    height: 70px;
    border-radius: 40px;
    position: absolute;
    background: #000;
    color: #fff;
    font-size: 24px;
    font-weight: 500;
    content: "";
}

section.public-layout .process-vector-wrap-one span {
    right: -30px;
    top: 35%;
}

section.public-layout .process-bg {
    background: #f5f7ff;
    padding: 50px 0;
}

section.public-layout .process-vector-wrap-two span {
    left: -30px;
    top: 35%;
}

section.public-layout .process-content-wrap h4 {
    font-size: 28px;
    font-weight: 600;
    color: var(--heading);
}

section.public-layout .process-content-wrap {
    padding: 0 40px;
}

/*FAQ*/
section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button,
section.public-layout .accordion-body {
    background: var(--lightbg);
    border: none;
    box-shadow: none;
}

section.public-layout .faq-accordian .accordion-button::after {
    width: 25px;
    height: 25px;
    background-size: 25px;
}

section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button {
    font-size: 18px;
    font-weight: 600;
    color: var(--heading);
}

section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button:not(.collapsed)::after {
    background-image: url("/images/accordian/accordian-close.png");
}

section.public-layout
    .faq-accordian.accordion
    .accordion-item
    .accordion-header
    .accordion-button::after {
    background-image: url("/images/accordian/accrodian-open.png");
}

/*Call to action*/
section.public-layout .cta-section {
    margin-bottom: -60px;
    position: relative;
}

section.public-layout .cta-wrapper {
    padding: 60px 40px;
    background-size: cover;
    position: relative;
    z-index: 9;
}

section.public-layout .cta-wrapper h4 {
    font-size: 38px;
    font-weight: 700;
}

/*footer*/
section.public-layout .footer-menu li a {
    padding: 0 20px;
    color: var(--heading);
}

section.public-layout .footer-menu li a:hover {
    color: var(--primary);
}

section.public-layout .social-media li a {
    width: 30px;
    height: 30px;
    background: var(--primary);
    display: flex;
    justify-content: center;
    border-radius: 4px;
    align-items: center;
    transition: all 0.2s ease-in-out;
}

section.public-layout .social-media li a:hover {
    background: var(--heading);
    transition: all 0.2s ease-in-out;
}

section.public-layout .copyright-content p {
    color: #949c9b;
}

section.public-layout .footer {
    padding: 140px 0 0;
}

section.public-layout .partners .row {
    row-gap: 40px;
}

/***************About us page*************/

section.public-layout .beief-intro {
    background: #edf0ff;
}

section.public-layout .beief-intro .container p {
    font-size: 20px;
    font-weight: 500;
}

section.public-layout .about-pointers li {
    padding-left: 30px;
    margin-bottom: 15px;
}

section.public-layout .about-pointers li::before {
    position: absolute;
    content: "";
    width: 24px;
    height: 24px;
    top: 0;
    left: 0;
    background: url("/images/home/<USER>");
}

section.public-layout .workprocess-content {
    border: 1px solid var(--primary);
    padding: 36px 20px;
    transition: all 0.3s ease;
}

section.public-layout .pointers-list ul li {
    padding-left: 30px;
    margin-bottom: 15px;
}
section.public-layout .pointers-list ul li::before {
    position: absolute;
    content: "";
    width: 24px;
    height: 24px;
    top: 0;
    left: 0;
    background: url("/images/home/<USER>") no-repeat;
    background-size: contain;
    background-position: center;
}
section.public-layout .dashboard-features-wrap .pointers-list ul {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
}

/*About Us*/

section.public-layout section.inner-banner {
    min-height: 315px;
    display: flex;
    align-items: center;
    /* background: url("/images/inner-page-pattern.png") var(--secondary); */
    background: var(--primary);
    background-size: cover;
    background-position: bottom;
    position: relative;
}

section.public-layout header.darkHeader {
    background-color: var(--primary);
}

section.public-layout header {
    transition: all 0.3s ease;
}

/*Privacy Policy*/
section.public-layout .privacy-section .content h5 {
    color: #1c1616;
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 5px;
}

section.public-layout .privacy-section .content p,
section.public-layout .privacy-section .content li {
    color: #4a5b67;
}

section.public-layout .privacy-section .content ul {
    margin: 15px 0 0;
    padding: 0 0 0 20px;
    list-style: disc;
}

section.public-layout .privacy-section .content ul li {
    margin: 0 0 15px;
}

section.public-layout .privacy-section .content ul li:last-child {
    margin: 0;
}

section.public-layout .privacy-section .content ul li strong {
    font-weight: 700;
    color: #4a5b67;
}

section.public-layout .z-9 {
    z-index: 99;
}

/* Contact Us */

section.public-layout .contact-wrap ul.contact-details .box {
    display: flex;
    gap: 25px;
    margin: 0 0 20px;
}

section.public-layout .contact-wrap ul.contact-details .box .content {
    padding: 0 !important;
}

section.public-layout .contact-wrap ul.contact-details .box .content h5 {
    font-weight: 600;
}

section.public-layout .contact-wrap ul.contact-details .box .content p {
    margin: 0;
    color: #949c9b;
}

section.public-layout .contact-wrap ul.contact-details .box:last-child {
    margin: 0;
}

section.public-layout .contact-wrap .card {
    padding: 40px;
    border-radius: 20px;
    border-color: #cddde3;
}

section.public-layout .contact-wrap .card h3 {
    font-size: 22px;
    font-weight: 600;
}

section.public-layout .contact-wrap .card p {
    margin: 0;
}

section.public-layout .contact-wrap .card form {
    margin: 25px 0 0;
}

section.public-layout .contact-wrap .card form .form-control {
    background: #f5fdff;
    border-color: #e3f1f4;
    min-height: 42px;
    box-shadow: none;
}

section.public-layout .contact-wrap .card form textarea.form-control {
    min-height: 100px;
}

section.public-layout .map {
    margin-bottom: -260px;
}

section.public-layout header .navbar ul.navbar-nav li {
    display: flex;
    align-items: center;
}

@media screen and (max-width: 1280px) {
    section.public-layout .banner-content h1 {
        font-size: 36px;
    }

    section.public-layout header .navbar ul.navbar-nav {
        gap: 20px;
    }
}

@media screen and (max-width: 1024px) {
    section.public-layout .banner-vector img:last-child {
        display: none;
    }

    section.public-layout .features .container .row {
        row-gap: 20px;
    }
}

@media screen and (max-width: 991px) {
    section.public-layout .main-banner .row {
        row-gap: 40px;
    }
    section.public-layout .section.inner-banner {
        min-height: 280px !important;
    }
    section.public-layout .navbar-toggler {
        background-color: #fff !important;
    }
    section.public-layout .key-feature-one .container .row,
    section.public-layout .key-feature-two .container .row,
    section.public-layout .contact-wrap .container .row {
        row-gap: 24px;
    }
    section.public-layout .key-feature-one .container .row,
    section.public-layout .intro-sec .container .row {
        flex-direction: column-reverse;
    }

    section.public-layout .navbar-collapse.collapse.show {
        background: #3853cb;
    }
    section.public-layout .main-navbar.navbar-nav .nav-item .nav-link {
        padding: 8px 20px;
    }
    section.public-layout .main-navbar li a.btn {
        justify-content: center;
        margin: 0 20px 10px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: 40%;
    }
    section.public-layout .process-content-wrap {
        padding: 0 20px;
    }
    section.public-layout .process-vector-wrap-one img,
    section.public-layout .process-vector-wrap-two img {
        width: 80%;
    }
    section.public-layout .process-content-wrap h4 {
        font-size: 24px;
    }
    section.public-layout .common-heading h4,
    section.public-layout .cta-wrapper h4 {
        font-size: 32px;
    }
    section.public-layout .footer-menu li a {
        padding: 0 6px;
    }
    section.public-layout .working-process .common-heading {
        margin: 0 60px 50px;
    }
    section.public-layout .cta-wrapper {
        padding: 40px;
    }
}

@media screen and (max-width: 820px) {
    section.public-layout section.intro-sec .row {
        row-gap: 40px;
    }

    section.public-layout .banner-vector img:first-child {
        position: relative;
    }

    section.public-layout .features .container .row {
        row-gap: 20px;
    }

    section.public-layout .feature-card {
        padding: 20px 20px 40px;
    }

    section.public-layout .key-feature-one-img,
    section.public-layout .key-feature-two-img {
        text-align: center;
    }

    section.public-layout .key-feature-one-img {
        margin-bottom: 40px;
    }

    section.public-layout .footer-menu li a {
        padding: 0 6px;
    }

    section.public-layout .key-feature-one .container .row {
        flex-direction: column-reverse;
    }

    section.public-layout .key-feature-one .container .row {
        row-gap: 20px;
    }
}

@media screen and (max-width: 567px) {
    section.public-layout {
        font-size: 14px;
    }

    section.public-layout .main-banner {
        padding: 40px 0;
    }

    section.public-layout .banner-content h1 {
        font-size: 30px;
    }

    section.public-layout .banner-vector img:last-child {
        display: none;
    }

    section.public-layout .features .container .row {
        row-gap: 20px;
    }

    section.public-layout .banner-vector img:first-child {
        position: relative;
    }

    section.public-layout .common-heading h4 {
        font-size: 30px;
        font-weight: 600;
    }

    section.public-layout .feature-card {
        padding: 20px 20px 40px;
    }

    section.public-layout .section-head h3 {
        font-size: 30px;
        font-weight: 700;
    }

    section.public-layout .about-content {
        margin-bottom: 30px;
    }

    section.public-layout .pointer-wrapper {
        flex-wrap: wrap;
    }

    section.public-layout .feature-img {
        min-width: 80px;
        width: 80px;
        height: 80px;
    }

    section.public-layout .feature-img img {
        width: 40px;
        height: 40px;
    }

    section.public-layout .partners-wrap .row .col-sm-12 {
        margin-bottom: 40px;
    }

    section.public-layout .py-80 {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    section.public-layout .cta-wrapper ul li:first-child {
        margin-bottom: 20px;
    }

    section.public-layout .cta-wrapper ul {
        margin-bottom: 0;
    }
    section.public-layout .flex-box {
        grid-template-columns: repeat(1, 1fr);
    }
    section.public-layout .flex-box .box::after {
        display: none;
    }
    section.public-layout section.inner-banner {
        min-height: 200px;
    }
    section.public-layout .ft32 {
        font-size: 26px;
    }
    section.public-layout .cta-wrapper h4 {
        font-size: 28px;
    }

    section.public-layout
        .faq-accordian.accordion
        .accordion-item
        .accordion-header
        .accordion-button,
    section.public-layout .beief-intro .container p {
        font-size: 16px;
    }
    section.public-layout .workprocess-content {
        z-index: 1;
    }

    section.public-layout .flex-box {
        margin-bottom: 50px;
    }

    section.public-layout .flex-box {
        gap: 90px;
    }
    section.public-layout .dashboard-features-wrap .pointers-list ul {
        grid-template-columns: repeat(1, 1fr);
    }
    section.public-layout .working-process .common-heading {
        margin: 10px;
    }
    section.public-layout .process-vector-wrap-one,
    section.public-layout .process-vector-wrap-two {
        margin: 0 20px 40px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: -25px;
        left: 20px;
    }
    section.public-layout .process-bg .container .row {
        flex-direction: column-reverse;
    }
    section.public-layout .pt-80 {
        padding-top: 40px;
    }

    section.public-layout .process-bg {
        padding: 30px 0;
    }
}

@media screen and (max-width: 320px) {
    section.public-layout .navbar-brand img {
        max-width: 220px;
        width: 100%;
    }

    section.public-layout .banner-content h1,
    section.public-layout .common-heading h4 {
        font-size: 28px;
    }

    section.public-layout
        .faq-accordian.accordion
        .accordion-item
        .accordion-header
        .accordion-button {
        font-size: 16px;
    }

    section.public-layout .cta-wrapper h4 {
        font-size: 28px;
    }

    section.public-layout .cta-wrapper {
        padding: 30px;
    }

    section.public-layout .cta-wrapper p.mb-5 {
        margin-bottom: 20px !important;
    }

    section.public-layout .py-80 {
        padding-top: 40px;
        padding-bottom: 40px;
    }

    section.public-layout .feature-content h4 {
        font-size: 18px;
        line-height: 26px;
    }
    section.public-layout .flex-box {
        margin-bottom: 50px;
    }

    section.public-layout .flex-box {
        gap: 90px;
    }
    section.public-layout .workprocess-icon {
        width: 90px;
        height: 90px;
    }

    section.public-layout .workprocess-icon::before {
        width: 140px;
        height: 140px;
    }

    section.public-layout .workprocess-icon::after {
        width: 200px;
        height: 200px;
    }
    section.public-layout .banner-content h1,
    section.public-layout .common-heading h4 {
        font-size: 24px;
    }
    section.public-layout .process-vector-wrap-one,
    section.public-layout .process-vector-wrap-two {
        margin: 0 5px 30px;
    }
    section.public-layout .process-content-wrap h4 {
        font-size: 20px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        width: 40px;
        height: 40px;
    }
    section.public-layout .process-vector-wrap-one span,
    section.public-layout .process-vector-wrap-two span {
        top: -20px;
    }

    section.public-layout .common-heading h5 {
        font-size: 18px;
    }

    section.public-layout .common-heading h4 {
        font-size: 26px;
        line-height: 36px;
    }
}

section.public-layout .mx-w60 {
    max-width: 60px !important;
}

section.public-layout img.img-responsive {
    max-width: 100% !important;
    height: auto;
}

.privacy-section strong {
    font-weight: 600;
}

.privacy-section ul {
    list-style: disc !important;
    padding-left: 18px !important;
}
