@media screen and (max-width: 1442px) {
    .brief p {
        font-size: 14px;
    }
    .candidate-list-card .row > * {
        padding-left: 12px !important;
        padding-right: 12px !important;
    }
}

@media screen and (max-width: 560px) {
    .staffManagement .candidate-list-card .collapse-staff ul {
        gap: 15px;
    }
    .staffManagement .candidate-list-card .collapse-staff ul li {
        flex-wrap: wrap;
        gap: 3px;
    }
    .staffManagement .candidate-list-card .collapse-staff ul li > div {
        width: 100%;
        min-width: 100%;
    }
    .candidate-list-card .avatar-box {
        width: 50px;
        height: 50px;
        min-width: 50px;
    }

    .candidate-list-card .avatar-box img {
        width: 100%;
        height: 100%;
    }

    span.ellipsis-text {
        width: 70px;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}
