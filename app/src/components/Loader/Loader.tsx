import React from "react";
import Image from "next/image";
// import { Spin } from "antd";
// import ProgressBar from 'react-bootstrap/ProgressBar';

type LoaderProps = {
  loaderText: string;
};

const Loader = ({ loaderText }: LoaderProps) => {
  return (
    <section className="loader">
      <div className="fullScreenLoader">
        {/******Default Loader******/}

        <div className="box py-2 px-3 rounded-3 bg-white">
          <Image width={50} height={50} src="/images/loader.gif" alt="loader" />
          {loaderText && <span>{loaderText}</span>}
        </div>

        {/******Upload File Loader******/}

        {/* <div className="box p-3 rounded-3 bg-white flex gap-2 flex-wrap custom-progress-box">
          <h5>Uploading File...</h5>
          <p>
            50% <span>File Name</span>
          </p>
          <div className="custom-progress">
            <span style={{ width: "50%" }}></span>
          </div>
        </div> */}

        {/* <Spin tip="Loading" size="large">
          <div className="content" />
        </Spin> */}
      </div>
    </section>
  );
};

export default Loader;
