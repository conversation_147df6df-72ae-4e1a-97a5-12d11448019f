import { Editor } from "@tinymce/tinymce-react";
import { useRef } from "react";

export const CustomEditor = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (val: string) => void;
}) => {
  const editorRef = useRef<any>(null);

  return (
    <Editor
      apiKey="v7s6s4v3walvesrsyrsjvhtiyllk2waam8kojnjm58ov5y9i" // Optional if running locally, required for production
      onInit={(abc: any, editor: any) => (editorRef.current = editor)}
      value={value}
      onEditorChange={(content: string) => onChange(content)}
      init={{
        height: 500,
        menubar: false,
        plugins: [
          "advlist autolink lists link image charmap preview anchor",
          "searchreplace visualblocks code fullscreen",
          "insertdatetime media table paste code help wordcount",
          "emoticons",
        ],
        toolbar: `undo redo | formatselect | bold italic underline strikethrough | forecolor backcolor | 
           alignleft aligncenter alignright alignjustify | 
           bullist numlist outdent indent | removeformat | 
           pagebreak | charmap emoticons | fullscreen preview | help`,

        // Branding off for cleaner UI
        branding: false,
      }}
    />
  );
};
