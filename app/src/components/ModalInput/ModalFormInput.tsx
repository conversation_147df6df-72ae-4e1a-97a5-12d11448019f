import React, {
  useState,
  RefObject,
  useEffect,
  useRef,
  ReactNode,
} from "react";
import type { CustomSelectDateEvent } from "@src/components/input/InputField";
import type {
  GlobalInputFieldType,
  GlobalInputFieldTypesRecord,
} from "@src/components/input/GlobalInput";
import { Form, Button } from "antd";
import { validateFieldError } from "@src/helper/validations/custom";
import { ValidateInputValue } from "@src/helper/common";
import { KeyPairInterface } from "@src/redux/interfaces";
import { AuthResendOTP } from "../Auth/AuthResendOtp";
import type { CustomSelectChangeEvent } from "../input/SelectField";
import DynamicFormField from "../input/DynamicFormField";
import Image from "next/image";

type ModalFormInput = {
  state: KeyPairInterface;
  setState: React.Dispatch<React.SetStateAction<KeyPairInterface>>;
  fields: GlobalInputFieldType[];
  onSubmit: Function;
  onClose?: Function;
  onResend?: Function;
  buttonTitle: string;
  cancelButtonTitle?: string;
  checkbox?: boolean;
  checkboxTitle?: string;
  checkboxKey?: string;
  submitRef?: RefObject<HTMLButtonElement>; // or any other HTML element type
  submitClass?: string;
  groupClasses?: KeyPairInterface;
  customButtons?: ReactNode;
  customError?: string;
  formClass?: string;
};
export const ModalFormInput: React.FC<ModalFormInput> = ({
  setState,
  fields,
  state,
  onSubmit,
  buttonTitle,
  onClose,
  onResend,
  cancelButtonTitle = "Cancel",
  checkbox = false,
  checkboxTitle = "Save",
  checkboxKey = "chekbox",
  submitRef = null,
  submitClass = "",
  groupClasses = {},
  customButtons = undefined,
  customError = undefined,
  formClass = undefined,
}) => {
  const prevFieldsRef = useRef<GlobalInputFieldType[]>(fields);
  const [error, setError] = useState<KeyPairInterface>({});

  const arrayKeyValue = (key: string, index: number) => {
    var prevValue = state[key] || [];
    prevValue = Array.isArray(prevValue) ? prevValue : [];
    for (let i = 0; i <= index; i++) {
      if (prevValue.length <= i) {
        prevValue = [...prevValue, null];
      }
    }
    return prevValue;
  };

  function hasDisabledChanged(
    prevFields: GlobalInputFieldType[],
    newFields: GlobalInputFieldType[],
  ): boolean {
    if (!Array.isArray(prevFields) || !Array.isArray(newFields)) return false;
    if (prevFields.length !== newFields.length) return false;

    const prevFieldsLookup: { [key: string]: boolean } = {};
    // Populate lookup objects
    prevFields.forEach((item) => {
      prevFieldsLookup[item.name] = !!item.required;
    });

    let changedObjects = false;
    for (const field of newFields) {
      let required = !!field.required;
      if (required !== prevFieldsLookup[field.name]) {
        setError((prev) => ({ ...prev, [field.name]: "" }));
      }
    }
    return changedObjects; // No changes detected
  }

  // validate to check same fields or not
  useEffect(() => {
    const prevFields = prevFieldsRef.current;

    if (prevFields) {
      hasDisabledChanged(prevFields, fields);
    }

    // Update the ref with the current fields
    prevFieldsRef.current = fields;
  }, [fields]);
  const handleInputChangeEvent = async (
    e: React.ChangeEvent<
      HTMLInputElement | CustomSelectChangeEvent | CustomSelectDateEvent
    >,
  ) => {
    const { error, key, value, changable, index } = ValidateInputValue(e);
    if (index != undefined) {
      var prevError = (error[key as any] || []) as string[];
      if (changable || !(e.target instanceof HTMLInputElement)) {
        let prevValue = arrayKeyValue(key, index);

        prevValue = prevValue.map((existingValue: any, valIndex: number) =>
          valIndex === index ? value : existingValue,
        );
        setState((prev: KeyPairInterface) => ({ ...prev, [key]: prevValue }));
      }

      // Handling error update
      if (!Array.isArray(prevError)) {
        prevError = [];
      }

      for (let i = 0; i <= index; i++) {
        if (prevError.length <= i) {
          prevError = [...prevError, ""];
        }
      }
      prevError = prevError.map((existingValue: any, valIndex: number) =>
        valIndex === index ? error : existingValue,
      );
      setError((prev: KeyPairInterface) => ({ ...prev, [key]: prevError }));
    } else {
      if (changable || !(e.target instanceof HTMLInputElement)) {
        setState((prev: KeyPairInterface) => ({ ...prev, [key]: value }));
      }
      setError((prev: KeyPairInterface) => ({ ...prev, [key]: error }));
    }
  };

  const handleKeyDownEvent = async (
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    const target = e.target as any;
    const targetType = target?.type ?? ("text" as string);
    if (
      (e.key === "Enter" || e.key === "enter") &&
      !["textarea"].includes(targetType)
    ) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = (e: any) => {
    const isValid = validate();
    if (isValid) {
      onSubmit();
    }
    return isValid;
  };

  const validate = () => {
    let isValid = true;
    fields.forEach((field: GlobalInputFieldType) => {
      const value: string = state[field.name] ?? "";
      const error = validateFieldError({ ...field, value });
      if (field.disabled) {
        setError((prev) => ({ ...prev, [field.name]: "" }));
      } else if (error) {
        setError((prev) => ({ ...prev, [field.name]: error.errorMsg }));
        if (error.errorMsg.length) {
          isValid = false;
        }
      }
      if (field.name === "confirm_password") {
        const password: string = state["new_password"] ?? "";
        if (password !== value) {
          setError((prev) => ({
            ...prev,
            [field.name]: "Confirm Password does not match.",
          }));
          isValid = false;
        }
      }
      if (field.name === "new_password") {
        const password: string = state["old_password"] ?? "";
        if (password === value && password.trim() != "") {
          setError((prev) => ({
            ...prev,
            [field.name]: "Old and New Password should not be the same.",
          }));
          isValid = false;
        }
      }
    });
    return isValid;
  };

  const onRemoveItem = (key: string, index: number) => {
    let prevValue = arrayKeyValue(key, index);
    prevValue = prevValue.filter((_: any, i: number) => i !== index);
    setState((prev: KeyPairInterface) => ({ ...prev, [key]: prevValue }));
  };

  const onAddItem = (key: string, index: number) => {
    let prevValue = arrayKeyValue(key, index);
    setState((prev: KeyPairInterface) => ({ ...prev, [key]: prevValue }));
  };

  const handleInputBlurEvent = (
    e: React.ChangeEvent<
      HTMLInputElement | CustomSelectChangeEvent | CustomSelectDateEvent
    >,
  ) => {
    const { error, key, value, trim, index } = ValidateInputValue(e, "blur");
    const onEmptyNull = e.target.dataset?.empty ?? false;

    if (index != undefined) {
      let prevValue = arrayKeyValue(key, index);
      prevValue = prevValue.map((existingValue: any, valIndex: number) =>
        valIndex === index ? value : existingValue,
      );

      prevValue = prevValue.map((val: any) => {
        if (
          onEmptyNull &&
          (!val || (typeof val == "string" && val.trim() === ""))
        ) {
          return null;
        } else {
          if (trim && typeof val == "string") {
            return val.trim();
          } else {
            return val;
          }
        }
      });
      setState((prev: KeyPairInterface) => ({ ...prev, [key]: prevValue }));

      var prevError = (error[key as any] || []) as string[];
      if (!Array.isArray(prevError)) {
        prevError = [];
      }

      for (let i = 0; i <= index; i++) {
        if (prevError.length <= i) {
          prevError = [...prevError, ""];
        }
      }
      prevError = prevError.map((existingValue: any, valIndex: number) =>
        valIndex === index ? error : existingValue,
      );
      setError((prev: KeyPairInterface) => ({ ...prev, [key]: prevError }));
    } else {
      if (
        onEmptyNull &&
        (!value || (typeof value == "string" && value.trim() === ""))
      ) {
        setState((prev: KeyPairInterface) => ({ ...prev, [key]: null }));
      } else {
        if (trim && typeof value == "string") {
          setState((prev: KeyPairInterface) => ({
            ...prev,
            [key]: value.trim(),
          }));
        } else {
          setState((prev: KeyPairInterface) => ({ ...prev, [key]: value }));
        }
      }
      setError((prev: KeyPairInterface) => ({ ...prev, [key]: error }));
    }
  };

  const handleCheckbox = () => {
    setState((prev: KeyPairInterface) => ({
      ...prev,
      [checkboxKey]: prev[checkboxKey] ? null : true,
    }));
  };

  const groupFieldsByGroup = (fields: GlobalInputFieldType[]) => {
    const groups: GlobalInputFieldTypesRecord = fields.reduce((acc, field) => {
      const groupName = field.groupName || "ungrouped"; // Default to 'ungrouped' if group is empty
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(field);
      return acc;
    }, {} as GlobalInputFieldTypesRecord);

    // Second pass: Sort groups and fields
    const sortedGroups = Object.entries(groups)
      .sort(([groupNameA, fieldsA], [groupNameB, fieldsB]) => {
        // Extract groupPosition for sorting groups
        const positionA = fieldsA[0]?.groupPosition ?? Number.MAX_SAFE_INTEGER;
        const positionB = fieldsB[0]?.groupPosition ?? Number.MAX_SAFE_INTEGER;
        return positionA - positionB;
      })
      .reduce((acc, [groupName, fields]) => {
        // Sort fields within each group
        const sortedFields = fields.sort(
          (a, b) =>
            (a.fieldPosition ?? Number.MAX_SAFE_INTEGER) -
            (b.fieldPosition ?? Number.MAX_SAFE_INTEGER),
        );
        acc[groupName] = sortedFields;
        return acc;
      }, {} as GlobalInputFieldTypesRecord);

    return sortedGroups;
  };

  const groupedFields = groupFieldsByGroup(fields);

  return (
    <>
      <Form className={formClass}>
        {Object.keys(groupedFields).map((groupName, groupIndex) => {
          const groupMainName =
            !groupName || groupName === "ungrouped" || groupName === ""
              ? ""
              : groupName;
          return (
            <>
              <div
                key={`${groupName ?? "has-group"}-${groupIndex}`}
                className={groupClasses && groupClasses[groupMainName ?? ""]}>
                <div
                  key={groupIndex}
                  className={`group-section row ${groupMainName !== "" ? "has-group" : ""}`}>
                  {groupMainName !== "" && (
                    <h6 className="form-group-name">{groupMainName}:</h6>
                  )}
                  {groupedFields[groupName].map((field, fieldIndex) => {
                    return (
                      <DynamicFormField
                        key={fieldIndex}
                        placeholder={`Enter ${field.label}`}
                        value={state[field.name]}
                        error={error[field.name]}
                        onChangeInput={handleInputChangeEvent}
                        onBlur={handleInputBlurEvent}
                        onKeyDown={handleKeyDownEvent}
                        onRemoveItem={onRemoveItem}
                        onAddItem={onAddItem}
                        {...field}
                      />
                    );
                  })}
                </div>
              </div>
            </>
          );
        })}

        {onResend && <AuthResendOTP onResend={onResend} />}

        {checkbox && (
          <div className="group-relative">
            <div className="same-line-privacy">
              <input
                type="checkbox"
                id="accept"
                checked={state[checkboxKey]}
                onChange={handleCheckbox}
              />
              <span>
                <Image
                  src="/images/check-green.svg"
                  width={11}
                  height={8}
                  alt=""
                />
              </span>
              <label htmlFor="accept" className="">
                {checkboxTitle}
              </label>
            </div>
            {error[checkboxKey] && (
              <p style={{ color: "red" }}>{error[checkboxKey]}</p>
            )}
          </div>
        )}
        {customError && <p className="error-message">{customError}</p>}
      </Form>

      <div className="ant-modal-footer">
        {customButtons}
        <Button
          className={`btn btn-theme mr-1 ${submitClass}`}
          onClick={handleSubmit}
          ref={submitRef}>
          {buttonTitle}
        </Button>
        {onClose && (
          <Button className="btn" key="cancel" onClick={() => onClose()}>
            {cancelButtonTitle}
          </Button>
        )}
      </div>
    </>
  );
};
