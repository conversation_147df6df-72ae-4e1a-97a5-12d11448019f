import React, { useEffect, useState } from "react";
import { OpportunityInterface } from "@src/redux/interfaces";
import { Col, Row } from "react-bootstrap";

type WildcardOpportunityDetailProps = {
  subdomain: string;
  opportunity: OpportunityInterface;
};

export function WildcardOpportunityDetail({
  opportunity,
}: WildcardOpportunityDetailProps) {
  const {
    experience,
    salary,
    department_name,
    location_names,
    designation,
    extra_fields,
  } = opportunity;

  const [extraFields, setExtraFields] = useState<Record<string, string>[]>([]);

  useEffect(() => {
    if (extra_fields && Object.keys(extra_fields).length > 0) {
      let result: any[] = [];
      Object.entries(extra_fields).forEach(([key, value]) => {
        result.push({ key: key.titleize(), value: value });
      });
      setExtraFields(result);
    } else {
      setExtraFields([]);
    }
  }, [extra_fields]);

  const formatTextAsBullets = (text: string): JSX.Element => {
    const lines = text.split("\n"); // Split text by newlines
    return (
      <ul>
        {" "}
        {/* Add padding for bullet visibility */}
        {lines.map((line, index) => (
          <li
            key={index}
            style={{ listStyleType: "disc", marginBottom: "0.5rem" }}>
            {line}
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="p-3">
      <div className="requirement">
        <h4 className="mb-4 fw-semibold heading-clr">{opportunity.title}</h4>

        <div className="content">
          <Row>
            <Col lg={6}>
              <h6 className="sub-head">Requirement:</h6>
              {opportunity.skills && (
                <div className="skills d-flex gap-3 p-3 rounded-3 mb-3">
                  <span>Skills:</span>
                  <p className="m-0 fw-medium text-black">
                    {opportunity.skills}
                  </p>
                </div>
              )}
              <ul>
                <li>
                  <span>No. of Vacancies:</span>
                  <b>{opportunity.number_of_vacancies}</b>
                </li>
                <li>
                  <span>Experience Required:</span>
                  {experience > 0 ? <b>Up to {experience} Years</b> : <b>-</b>}
                </li>
                <li>
                  <span>Salary:</span>
                  <b>{salary && salary > 0 ? salary : "-"}</b>
                </li>
                <li>
                  <span>Department:</span>
                  <b>{department_name}</b>
                </li>
                <li>
                  <span>Designation:</span>
                  <b>{designation}</b>
                </li>
                <li>
                  <span>Location:</span>
                  <b>{location_names?.join(", ")}</b>
                </li>
              </ul>

              {extraFields.length > 0 ? (
                <>
                  <div className="mt-4">
                    <h6>Extra Fields:</h6>
                  </div>
                  <ul>
                    {extraFields.map(({ key, value }) => (
                      <li key={key}>
                        <span>{key}</span>
                        <b>{value}</b>
                      </li>
                    ))}
                  </ul>
                </>
              ) : null}

              {opportunity.questions.length > 0 && (
                <div className="questions p-3 rounded-3">
                  <h6 style={{ color: "#8C8C8C" }}>
                    No of Questions: {opportunity.questions.length}
                  </h6>
                  <ul>
                    {opportunity.questions.length > 0 ? (
                      opportunity.questions.map((question, index) => (
                        <li
                          className="mt-2 m-0 mb-2 fw-medium text-black"
                          key={index}>
                          {question}
                        </li>
                      ))
                    ) : (
                      <li className="mt-2 m-0 fw-medium text-black">N/A</li>
                    )}
                  </ul>
                </div>
              )}
            </Col>
            <Col lg={6}>
              <div className="requirement">
                <div className="contact-detail">
                  <h6 className="sub-head">Contact Person Details:</h6>
                  <ul>
                    <li>
                      Name: <b>{opportunity.contact_name}</b>
                    </li>
                    <li>
                      Contact: <b>{opportunity.contact_phone}</b>
                    </li>
                    <li>
                      Email: <b>{opportunity.contact_email}</b>
                    </li>
                  </ul>
                </div>
                <hr />
                <div className="job-overview">
                  <h6 className="sub-head">Job Overview:</h6>
                  <p className="m-0">
                    {formatTextAsBullets(opportunity.description)}
                  </p>
                </div>

                <div className="key-responsibilities">
                  <h6 className="sub-head">Key Responsibilities:-</h6>
                  {formatTextAsBullets(opportunity.responsibilities)}
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
}
