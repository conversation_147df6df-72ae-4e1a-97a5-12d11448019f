import React from "react";
import { Skeleton } from "antd";
import { OpportunityInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import DialogComponents from "@src/components/DialogComponents";
import { openDialog } from "@src/redux/actions";
import { Button } from "antd";

type OpportunityDetailCardType = {
  skelton?: boolean;
  opportunity: OpportunityInterface | null;
};

export const OpportunityDetailCard: React.FC<OpportunityDetailCardType> = ({
  skelton = false,
  opportunity,
}) => {
  const dispatch = useAppDispatch();

  // Open modal to confirm status change
  const openJobInfo = (job: OpportunityInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          ClassName: "candidate-job-detail-modal",
          title: "Job Details",
          width: "50%",
          message: (
            <div className="mt-2 mb-2">
              <h3>Job Overview:</h3>
              <p dangerouslySetInnerHTML={{ __html: job.description }} />
              <h5>Key Responsibilities:-</h5>
              <p dangerouslySetInnerHTML={{ __html: job.responsibilities }} />
            </div>
          ),
        },
      }),
    );
  };

  if (skelton) {
    return (
      <div className="card m-0">
        <div className="p-3 border-bottom">
          <h6 className="m-0 fw-semibold heading-clr">
            <Skeleton active paragraph={false} />
          </h6>
        </div>
        <div className="p-3">
          <Skeleton active paragraph={{ rows: 10 }} />
        </div>
      </div>
    );
  }

  if (!opportunity) {
    return <></>;
  }

  const { experience, salary, department_name, location_names } = opportunity;

  return (
    <div className="card m-0">
      <div className="p-3 border-bottom">
        <h6 className="m-0 fw-semibold heading-clr">Job Details</h6>
      </div>
      <div className="p-3">
        <div className="requirement">
          <h4 className="mb-4 fw-semibold heading-clr">{opportunity.title}</h4>
          <h6 className="sub-head">Requirement:</h6>
          {opportunity.skills && (
            <div className="skills d-flex gap-3 p-3 rounded-3">
              <span>Skills:</span>
              <p className="m-0 fw-medium text-black">{opportunity.skills}</p>
            </div>
          )}
          <div className="content">
            <ul>
              <li>
                <span>No. of Vacancies:</span>
                <b>{opportunity.number_of_vacancies}</b>
              </li>
              <li>
                <span>Experience Required:</span>
                {experience && experience > 0 ? (
                  <b>Up to {experience} Years</b>
                ) : (
                  <b>-</b>
                )}
              </li>
              <li>
                <span>Salary:</span>
                <b>{salary && salary > 0 ? salary : "-"}</b>
              </li>
              <li>
                <span>Department:</span>
                <b>{department_name}</b>
              </li>
              <li>
                <span>Location:</span>
                <b>{location_names?.join(", ")}</b>
              </li>
            </ul>
          </div>

          <div className="d-flex justify-content-end">
            <Button
              className="btn btn-primary border-0 mt-5"
              onClick={() => openJobInfo(opportunity)}>
              Job Detail&apos;s{" "}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
