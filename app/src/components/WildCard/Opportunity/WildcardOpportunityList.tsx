import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { OpportunityInterface } from "@src/redux/interfaces";
import {
  openDialog,
  setLoader,
  updateOpportunityStatus,
} from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { DropdownLink, SwitchWithTooltip } from "@src/helper/actions";
import Image from "next/image";

type WildcardOpportunityListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  opportunityPagePermssions: string[];
  addNewButton?: React.ReactNode;
};

export function WildcardOpportunityList({
  fetchData,
  subdomain,
  opportunityPagePermssions,
  addNewButton,
}: WildcardOpportunityListProps) {
  const dispatch = useAppDispatch();

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.opportunity,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  // Update opportunity type status (active/inactive)
  const updateStatus = async (opportunity: OpportunityInterface) => {
    await dispatch(setLoader(true));
    await dispatch(
      updateOpportunityStatus(
        opportunity.id,
        opportunity.status == 1 ? 0 : 1,
        async (success, response) => {
          await dispatch(setLoader(false));
          flashMessage(response.message, success ? "success" : "error");
        },
      ),
    );
    await dispatch(setLoader(false));
  };

  // Open modal to confirm status change
  const openStatusChangeModal = (opportunity: OpportunityInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Job Status Update Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to update the status of the Job? This action
              may affect your current running functionality.
            </div>
          ),
          onConfirm: () => updateStatus(opportunity),
        },
      }),
    );
  };

  const currentEmployeeCreated = (id: number) => {
    if (!currentEmployee) {
      return false;
    }

    return currentEmployee.id == id;
  };

  const hasRows = rows && rows.length > 0;
  const hasEditPermission = opportunityPagePermssions.includes("edit");
  const isAdmin = ["Super Admin", "Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  return (
    <>
      <div className="candidate-list-wrap p-3">
        <div className="row">
          {hasRows ? (
            rows.map((opportunity: OpportunityInterface) => (
              <div
                className="col-md-6 col-lg-6 col-xl-6 col-sm-12 mb-3"
                key={opportunity.id}>
                <div className="candidate-list-card bg-white border rounded-3 overflow-hidden">
                  <div className="p-3 pb-4">
                    <div className="d-flex justify-content-between align-items-start flex-wrap flex-lg-nowrap position-relative">
                      <div className="candidate-des d-flex gap-3 flex-wrap flex-lg-nowrap">
                        <div className="brief">
                          <h4 className="heading-clr mb-3">
                            {opportunity.title ?? "Title not available"}
                          </h4>
                          <p className="text-clr m-0">
                            {opportunity.description ??
                              "No description available"}
                          </p>
                          <ul>
                            <li>
                              Experience:{" "}
                              <b>{opportunity.experience || "N/A"}</b>{" "}
                              <b>yrs</b>
                            </li>

                            <li>
                              Salary:{" "}
                              <b>
                                {opportunity.salary
                                  ? `INR ${opportunity.salary}`
                                  : "N/A"}
                              </b>
                            </li>
                            {hasEditPermission && (
                              <li>
                                Status:
                                <SwitchWithTooltip
                                  value={opportunity.status == 1}
                                  className="switch-theme ms-2"
                                  tooltipTitle="Not authorized to perform this action."
                                  disabled={
                                    !(
                                      isAdmin ||
                                      currentEmployeeCreated(
                                        opportunity.created_by_id ?? 0,
                                      )
                                    )
                                  }
                                  onChange={() =>
                                    openStatusChangeModal(opportunity)
                                  }
                                />
                              </li>
                            )}
                            <li className="job-part">
                              <Image
                                src={"/images/job-work.svg"}
                                width={16}
                                height={16}
                                alt=""
                              />
                              Full-time
                            </li>
                          </ul>
                        </div>
                      </div>
                      <ul className="list-inline align-items-center mb-0 d-flex">
                        <li className="list-inline-item">
                          <div className="opportunity-action no-arrow">
                            <Dropdown>
                              <Dropdown.Toggle
                                id={`opportunity-dropdown-${opportunity.id}}`}
                                className="text-decoration-none"
                                as="span"
                                type="button"
                                role="button"
                                aria-haspopup="true"
                                aria-expanded="false">
                                <span className="text-black small">
                                  <MoreVertIcon />
                                </span>
                              </Dropdown.Toggle>

                              <Dropdown.Menu
                                className="dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby={`opportunity-dropdown-${opportunity.id}}`}>
                                <DropdownLink
                                  linkHref={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${opportunity.id}`.subdomainLink(
                                    subdomain,
                                  )}>
                                  View Detail
                                </DropdownLink>

                                <DropdownLink
                                  linkHref={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${opportunity.id}/edit`.subdomainLink(
                                    subdomain,
                                  )}
                                  disabled={
                                    !(
                                      isAdmin ||
                                      currentEmployeeCreated(
                                        opportunity.created_by_id ?? 0,
                                      )
                                    )
                                  }
                                  hidden={!hasEditPermission}>
                                  Edit Detail
                                </DropdownLink>
                              </Dropdown.Menu>
                            </Dropdown>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div className="d-flex mt-auto gap-3 flex-xl-wrap flex-xxl-nowrap">
                    <a
                      href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${opportunity.id}/shortlisted`.subdomainLink(
                        subdomain,
                      )}
                      type="button"
                      className="btn btn-light w-100 rounded-0">
                      View More
                    </a>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="no-records text-center">
              No Records Found
              {addNewButton && (
                <>
                  <br />
                  {addNewButton}
                </>
              )}
            </div>
          )}
        </div>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
