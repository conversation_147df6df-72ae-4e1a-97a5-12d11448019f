import React, { useState } from "react";
import { useAppDispatch } from "@src/redux/store";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { DepartmentInterface, KeyPairInterface } from "@src/redux/interfaces";
import {
  createOpportunity,
  setLoader,
  getAllEmployeeOptions,
  getAllDepartmentOptions,
  getAllLocationOptions,
  openDialog,
} from "@src/redux/actions";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useRouter } from "next/router";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import { Button } from "react-bootstrap";
import { Button as AntdButton } from "antd";
import DialogComponents from "@src/components/DialogComponents";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { opportunityApi } from "@src/apis/wildcardApis";
import PageHeader from "@src/components/PageHeader/PageHeader";
import Dot from "dot-object";
import ExtraFieldButton from "@src/components/ExtraField/ExtraField";

type WildcardNewOpportunityProps = {
  subdomain: string;
  pageTitle: string;
  pageDescription?: string | React.ReactNode;
  buttonComponent?: React.ReactNode;
  children?: React.ReactNode;
  breadcrumb?: string[];
};

const DefaultOpportunityFields: GlobalInputFieldType[] = [
  {
    name: "title",
    label: "Title",
    type: "text",
    dataType: "nameWithHyphenAndDot",
    placeholder: "Enter Job Title",
    required: true,
    minLength: 3,
    maxLength: 100,
    tooltipTitle: "Title of the Job",
    groupName: "Job Title",
    groupPosition: 1,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "number_of_vacancies",
    label: "Number of Vacancies",
    type: "text",
    dataType: "onlynumber",
    placeholder: "Enter Number of Vacancies",
    required: true,
    strict: true,
    maxLength: 10,
    tooltipTitle: "Total Number of Vacancies",
    groupName: "Job Brief",
    groupPosition: 2,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "salary",
    label: "Salary",
    type: "text",
    dataType: "decimaltwodigit",
    placeholder: "Enter Salary",
    required: false,
    strict: true,
    maxLength: 20,
    tooltipTitle: "Salary for the Position",
    groupName: "Job Brief",
    groupPosition: 2,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },
  // {
  //   name: "maximum_salary",
  //   label: "Maximum Salary",
  //   type: "text",
  //   dataType: "decimaltwodigit",
  //   placeholder: "Enter Maximum Salary",
  //   required: true,
  //   strict: true,
  //   maxLength: 20,
  //   tooltipTitle: "Maximum Salary for the Position",
  //   groupName: "Job Brief",
  //   groupPosition: 2,
  //   fieldPosition: 4,
  //   className: "col-sm-12 col-md-4",
  // },
  {
    name: "experience",
    label: "Experience",
    type: "text",
    dataType: "decimalsingledigit",
    placeholder: "Enter Experience",
    required: false,
    strict: true,
    maxLength: 4,
    tooltipTitle: "Experience Required",
    groupName: "Job Brief",
    groupPosition: 2,
    fieldPosition: 5,
    className: "col-sm-12 col-md-4",
  },
  // {
  //   name: "maximum_experience",
  //   label: "Maximum Experience",
  //   type: "text",
  //   dataType: "decimalsingledigit",
  //   placeholder: "Enter Maximum Experience",
  //   required: true,
  //   strict: true,
  //   maxLength: 20,
  //   tooltipTitle: "Maximum Experience Required",
  //   groupName: "Job Brief",
  //   groupPosition: 2,
  //   fieldPosition: 6,
  //   className: "col-sm-12 col-md-4",
  // },
  {
    name: "designation",
    label: "Designation",
    type: "text",
    dataType: "dotWithOneAlphanumeric",
    placeholder: "Enter designation",
    required: true,
    minLength: 3,
    maxLength: 100,
    tooltipTitle: "Designation of the Job",
    groupName: "Job Brief",
    groupPosition: 2,
    fieldPosition: 7,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "responsibilities",
    label: "Roles & Responsibilities",
    type: "textarea",
    dataType: "string",
    placeholder: "Enter Roles & Responsibilities",
    required: true,
    maxLength: 2000,
    tooltipTitle: "Roles & Responsibilities",
    groupName: "Job Brief",
    groupPosition: 9,
    rows: 10,
  },

  {
    name: "questions",
    label: "Question",
    type: "textarea",
    dataType: "text",
    placeholder: "Enter Questions",
    maxLength: 255,
    tooltipTitle: "Questions Related to the Job",
    groupName: "Question to Ask",
    groupPosition: 6,
    multipleFields: true,
    newFieldlabel: "Add New Question",
    rows: 1,
  },
];

export function WildcardNewOpportunity(props: WildcardNewOpportunityProps) {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [state, setState] = useState<KeyPairInterface>({});
  const [extraFields, setExtraFields] = useState<GlobalInputFieldType[]>([]);

  const departmentPermissions =
    useEmployeeSelectedPagePermissions("departments");
  const [loading, setLoading] = useState<boolean>(false);
  const departmentOptions = useSelector(
    (state: RootState) => state.department.options,
  );
  const locationOptions = useSelector(
    (state: RootState) => state.location.options,
  );

  const employeeOptions = useSelector(
    (state: RootState) => state.employee.options,
  );

  const onSubmit = async () => {
    await dispatch(setLoader(true));
    const dot = new Dot(".");
    const newState = dot.object({ ...state, extra_fields: {} });
    await dispatch(
      createOpportunity(newState, async (success, response) => {
        await dispatch(setLoader(false));
        flashMessage(response.message, success ? "success" : "error");
        if (success) {
          router.push(APP_ROUTE.OPPORTUNITY_MANAGEMENT);
        }
      }),
    );
    await dispatch(setLoader(false));
  };

  const fetchDepartmentOptions = async (search: string, show_id?: number) => {
    await setLoading(true);
    await dispatch(
      getAllDepartmentOptions({ search: search, showId: show_id }),
    );
    await setLoading(false);
  };

  const fetchLocationOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllLocationOptions({ search: search }));
    await setLoading(false);
  };
  const fetchEmployeeOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllEmployeeOptions({ search: search }));
    await setLoading(false);
  };

  const openNewDepartmentModal = async () => {
    const wait = (ms: number) =>
      new Promise((resolve) => setTimeout(resolve, ms));
    await dispatch(setLoader(true));
    await wait(500);
    await dispatch(setLoader(false));
    await dispatch(
      openDialog({
        config: DialogComponents.ADD_NEW_DEPARTMENT,
        options: {
          onDepartmentCreate: (data: DepartmentInterface) => {
            setState((prev) => ({ ...prev, department_id: data.id }));
            fetchDepartmentOptions("", data.id);
          },
        },
      }),
    );
  };

  const AddDepartmentButton = (): React.ReactNode => {
    if (departmentPermissions.includes("write")) {
      return (
        <>
          <Button
            className={`btn btn-theme w-100 mt-1`}
            onClick={openNewDepartmentModal}>
            Add New Department
          </Button>
        </>
      );
    }
    return undefined;
  };

  const generateJobDescription = () => {
    const { title, designation, experience } = state;
    let missingFields = [];
    if (!title || title.trim() === "") missingFields.push("Job Title");
    if (!designation || designation.trim() === "")
      missingFields.push("Designation");
    if (!experience || experience.trim() === "")
      missingFields.push("Experience");

    if (missingFields.length > 0) {
      // Construct a dynamic message for the missing fields
      const missingFieldsMessage = `Before generating the job description, please add the following field(s): ${missingFields.join(", ")}.`;
      dispatch(
        openDialog({
          config: DialogComponents.SUCCESS_MODAL,
          options: {
            title: "Fields Required",
            message: <div className="mt-2 mb-2">{missingFieldsMessage}</div>,
          },
        }),
      );
    } else {
      generateJobDescriptionFromApi();
    }
  };

  const generateJobDescriptionFromApi = async () => {
    dispatch(setLoader(true));
    const { title, designation, experience } = state;
    const { success, data, message } =
      await opportunityApi.generateJobDescription({
        title,
        designation,
        experience,
      });
    dispatch(setLoader(false));
    if (success) {
      const jobDescription = data.job_description;
      const responsibilities = data?.role_responsibilities?.join("\n") ?? "";
      console.log(
        responsibilities,
        "responsibilities",
        data.role_responsibilities,
      );
      setState((prev) => ({
        ...prev,
        description: jobDescription,
        responsibilities: responsibilities,
      }));
    } else {
      flashMessage(message, "error");
    }
  };

  const deleteExtraField = (name: string) => {
    const newFields = extraFields.filter((field) => field.name != name);
    setExtraFields(newFields);
    setState((prevState) =>
      Object.fromEntries(
        Object.entries(prevState).filter(([key]) => key !== name),
      ),
    );
  };

  const onFieldCreate = (field: GlobalInputFieldType, value: string) => {
    setExtraFields((prev) => [...prev, field]);
    setState((prev) => ({ ...prev, [field.name]: value }));
  };

  const NewOpportunityFields: GlobalInputFieldType[] = [
    ...DefaultOpportunityFields,
    {
      name: "description",
      label: "Job Description",
      type: "textarea",
      dataType: "string",
      placeholder: "Enter Job Description",
      required: true,
      maxLength: 2000,
      tooltipTitle: "Description of the Job",
      groupName: "Job Brief",
      fieldPosition: 9,
      groupPosition: 2,
      rows: 10,
      customLabelButton: (
        <AntdButton
          className="generate-description btn-theme ms-auto"
          onClick={generateJobDescription}>
          Generate Description & Responsibilites
        </AntdButton>
      ),
    },
    {
      name: "department_id",
      label: "Department",
      type: "select",
      dataType: "select",
      placeholder: "Select Department",
      required: true,
      showSearch: true,
      options: departmentOptions,
      onSearch: fetchDepartmentOptions,
      loading: loading,
      tooltipTitle: "Select the Department",
      groupName: "Job Brief",
      groupPosition: 2,
      fieldPosition: 1,
      className: "col-sm-12 col-md-4",
      selectCustomButton: <AddDepartmentButton />,
    },
    {
      name: "location",
      label: "Location",
      type: "select",
      dataType: "select",
      placeholder: "Select Location",
      required: true,
      showSearch: true,
      options: locationOptions,
      onSearch: fetchLocationOptions,
      loading: loading,
      tooltipTitle: "Select the Location",
      groupName: "Job Brief",
      groupPosition: 2,
      fieldPosition: 7,
      className: "col-sm-12 col-md-4",
      mode: "multiple",
    },
    {
      name: "contact_person_id",
      label: "Contact Person",
      type: "select",
      dataType: "select",
      placeholder: "Select Contact Person",
      required: true,
      showSearch: true,
      options: employeeOptions,
      onSearch: fetchEmployeeOptions,
      loading: loading,
      tooltipTitle: "Select the Contact Person",
      groupName: "Contact Person Detail",
      className: "col-sm-12 col-md-4",
      groupPosition: 4,
    },
    ...extraFields,
  ];

  return (
    <>
      <PageHeader
        pageTitle={props.pageTitle}
        breadcrumb={props.breadcrumb}
        pageDescription={props.pageDescription}
        buttonComponent={
          <ExtraFieldButton
            deleteExtraField={deleteExtraField}
            fields={NewOpportunityFields}
            onFieldCreate={onFieldCreate}
          />
        }>
        <div className="box-content">
          <ModalFormInput
            buttonTitle="Add" // Title for the submit button
            fields={NewOpportunityFields} // Fields for the form
            setState={setState} // Function to update form state
            state={state} // Current form state
            onSubmit={onSubmit} // Function to handle form submission
          />
        </div>
      </PageHeader>
    </>
  );
}
