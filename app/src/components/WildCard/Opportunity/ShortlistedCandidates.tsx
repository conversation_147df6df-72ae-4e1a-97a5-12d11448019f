import React, { useEffect, useState } from "react";
import {
  CandidateInterviewInterface,
  OpportunityInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { openDialog, setLoader } from "@src/redux/actions";
import { opportunityApi } from "@src/apis/wildcardApis";
import { Pagination } from "antd";
import { InterviewBasicCardView } from "../CandidateInterview";
import DialogComponents from "@src/components/DialogComponents";
import flashMessage from "@src/components/FlashMessage";
import { Button } from "react-bootstrap";
import { Tooltip } from "antd";

interface ShortlistedCandidateInterface {
  rows: CandidateInterviewInterface[];
  count: number;
  page: number;
  limit: number;
  has_shortlist?: boolean;
}

const defaultShortlistedCandidateState: ShortlistedCandidateInterface = {
  rows: [],
  count: 0,
  page: 1,
  limit: 10,
  has_shortlist: false,
};

type ShortlistedCandidateType = {
  readonly opportunity: OpportunityInterface;
};

export const ShortlistedCandidate: React.FC<ShortlistedCandidateType> = ({
  opportunity,
}) => {
  const dispatch = useAppDispatch();
  const [{ count, page, limit, rows }, setState] =
    useState<ShortlistedCandidateInterface>(defaultShortlistedCandidateState);
  const [has_shortlist, setHasShortlist] = useState<boolean>(false);

  const [AIDrivenCandedates, setAIDrivenCandedates] = useState<any[]>([]);
  const [AiRecommendedClicked, setAiRecommendedClicked] =
    useState<boolean>(false);

  useEffect(() => {
    fetchData(1, 10);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const fetchData = async (page: number, limit: number) => {
    dispatch(setLoader(true));
    const { success, ...response } = await opportunityApi.shortlistedCandidates(
      opportunity.id,
      {
        page,
        limit,
      },
    );
    if (success) {
      setState(response.data);
      console.log("Shortlisted Candidates: ", response.meta?.has_shortlist);
      setHasShortlist(response.meta?.has_shortlist ?? false);
    }
    dispatch(setLoader(false));
  };

  const updateOfferLetterStatus = (
    currentInterview: CandidateInterviewInterface,
  ) => {
    const updatedRows = rows.map((interview: CandidateInterviewInterface) => {
      if (currentInterview.id == interview.id) {
        return { ...interview, ...currentInterview };
      }
      return interview;
    });
    console.log(updatedRows, "intervieupdatedRows");
    setState((prev) => ({ ...prev, rows: updatedRows }));
  };

  const openOfferLetterModal = async (
    interview: CandidateInterviewInterface,
  ) => {
    dispatch(
      openDialog({
        config: DialogComponents.SEND_OFFER_LETTER,
        options: {
          interview: interview,
          onSubmit: updateOfferLetterStatus,
        },
      }),
    );
  };

  const hasJobs = rows.length > 0;

  const HandleAIDrivenCall = async () => {
    await dispatch(setLoader(true));
    const { success, message, ...response } =
      await opportunityApi.getAIDrivenCandidate(opportunity.id);
    await dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      setAIDrivenCandedates(response.data);
      setAiRecommendedClicked(true);
    }
  };

  return (
    <div className="card h-100">
      <div className="p-3 border-bottom d-flex justify-content-between align-items-center">
        <h6 className="m-0 fw-semibold heading-clr">Shortlisted Candidates</h6>
        <Tooltip
          placement="top"
          title="AI Recommended candidates are those who have been recommended by AI based on their interview and skills.">
          <span className="inline-block">
            <Button
              onClick={() => HandleAIDrivenCall()}
              variant=""
              disabled={!has_shortlist || AiRecommendedClicked}
              className="no-underline text-white cursor-pointer disabled:cursor-not-allowed">
              AI Recommended
            </Button>
          </span>
        </Tooltip>
      </div>
      <div className="p-3">
        <div className="shortlisted-candidate-detail">
          <ul>
            {hasJobs ? (
              rows.map(
                (interview: CandidateInterviewInterface, key: number) => {
                  const isAIDrivenCandidate = AIDrivenCandedates.some(
                    (candidate) => candidate.id === interview.candidate_id,
                  );

                  return (
                    <InterviewBasicCardView
                      interview={interview}
                      openOfferLetterModal={openOfferLetterModal}
                      key={key}
                      isAIDrivenCandidate={isAIDrivenCandidate}
                    />
                  );
                },
              )
            ) : (
              <li>No Shortlisted Candidate found</li>
            )}
          </ul>
        </div>

        <Pagination
          className="mt-4"
          current={page}
          total={count}
          pageSize={limit}
          hideOnSinglePage
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};
