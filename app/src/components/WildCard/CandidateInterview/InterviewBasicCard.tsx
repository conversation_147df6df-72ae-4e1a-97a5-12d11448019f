import { APP_ROUTE } from "@src/constants";
import { CandidateInterviewInterface } from "@src/redux/interfaces";
import Image from "next/image";
import Link from "next/link";
import { Button } from "react-bootstrap";

interface InterviewBasicCardViewProps {
  interview: CandidateInterviewInterface;
  openOfferLetterModal: (interview: CandidateInterviewInterface) => void;
  isAIDrivenCandidate?: boolean;
}

export const InterviewBasicCardView: React.FC<InterviewBasicCardViewProps> = ({
  interview,
  openOfferLetterModal,
  isAIDrivenCandidate = false,
}) => {
  return (
    <li className="interview-card">
      {isAIDrivenCandidate && <span className="corner-ribbon" />}
      <span className="profile-img">
        <Image
          src={"/images/auth/undraw_profile.svg"}
          className="img-profile rounded-circle"
          width={70}
          height={70}
          alt=""
        />
      </span>
      <div className="content-box">
        <div className="data">
          <h5>
            {interview.candidate_name}{" "}
            {interview.offer_sent && <span>Offer Sent</span>}
          </h5>
          <p>{interview.candidate_designation}</p>
          {interview.status_name == "Finalize" && (
            <span className="finalize">
              <Image
                src="/images/thumb_up.svg"
                alt="thumb_up"
                height={21}
                width={16}
              />
              finalize
            </span>
          )}
        </div>
        <div className="buttons-group">
          <Link href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}`}>
            <Button className="btn btn-outline-primary">Interview Track</Button>
          </Link>
          {!interview.offer_sent && interview.status_name == "Finalize" && (
            <Button
              className="btn btn-primary border-0"
              onClick={() => openOfferLetterModal(interview)}>
              Send Offer Letter
            </Button>
          )}
        </div>
      </div>
    </li>
  );
};
