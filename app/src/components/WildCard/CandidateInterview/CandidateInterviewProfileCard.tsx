import { APP_ROUTE } from "@src/constants";
import { encrypt } from "@src/helper/encryption";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { CandidateBasicInterface } from "@src/redux/interfaces";
import Image from "next/image";
import Link from "next/link";

type CandidateInterviewProfileCardType = {
  candidate: CandidateBasicInterface;
};

export const CandidateInterviewProfileCard: React.FC<
  CandidateInterviewProfileCardType
> = ({ candidate }) => {
  const candidatePermissions = useEmployeeSelectedPagePermissions("candidates");

  return (
    <div className="schedule-interview-candidate-head">
      <span>
        <Image
          src={"/images/auth/undraw_profile.svg"}
          className="img-profile rounded-circle mr-2"
          width={44}
          height={44}
          alt=""
        />
      </span>
      <div className="content-box">
        <h5>
          {candidate.name}
          <span className="ml-auto job-id">
            Candidate ID:{" "}
            {candidatePermissions.includes("read") ? (
              <Link
                href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}>
                #CAN-{candidate.id.toString().padStart(4, "0")}
              </Link>
            ) : (
              `#CAN-${candidate.id.toString().padStart(4, "0")}`
            )}
          </span>
        </h5>
        <p className="m-0">{candidate.email}</p>
        {candidate.contact && <p className="m-0">{candidate.contact}</p>}
      </div>
    </div>
  );
};
