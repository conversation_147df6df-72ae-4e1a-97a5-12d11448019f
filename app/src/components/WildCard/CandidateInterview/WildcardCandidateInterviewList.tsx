import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  CandidateInterviewInterface,
  PageDetailInterface,
} from "@src/redux/interfaces";
import { APP_ROUTE } from "@src/constants";
import Link from "next/link";
import Image from "next/image";

type WildcardCandidateInterviewListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  currentPagePermissions: string[];
  pageDetail: PageDetailInterface;
};

export function WildcardCandidateInterviewList({
  subdomain,
  fetchData,
}: WildcardCandidateInterviewListProps) {
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.candidateInterview,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;

  return (
    <>
      <div className="candidate-list-wrap p-3 schedule-interview-list">
        <div className="row row-gap-3 candidate-row-list">
          {hasRows ? (
            rows.map(
              (interview: CandidateInterviewInterface, index: number) => {
                const scheduleInterview =
                  interview.schedule_interviews &&
                  interview.schedule_interviews[0];
                return (
                  <div
                    key={index}
                    className="col-12 col-md-12 col-lg-12 col-xl-6 candidate-column">
                    <div className="candidate-list-card bg-white p-3 border rounded-3">
                      <div className="d-flex time-slot gap-2 mb-3 flex-wrap justify-content-between">
                        <span>
                          {" "}
                          Time Slot:{" "}
                          <b>
                            {scheduleInterview.interview_at?.strftime(
                              "%B %d, %Y %I:%M %p",
                            ) ?? "N/A"}
                          </b>
                        </span>
                        <div className="d-flex gap-2">
                          <span className="badge-round ms-auto">
                            Round: <b>0{scheduleInterview.interview_round}</b>
                          </span>
                          <span className="badge-round ms-auto">
                            Status:{" "}
                            <b
                              className={`${interview.status_name.toLowerCase()}-text-color`}>
                              {interview.status_name}
                            </b>
                          </span>
                        </div>
                      </div>
                      <div className="content-box">
                        <h4 className="heading-clr">
                          {interview.opportunity_title}
                        </h4>
                        <span>
                          Interview By:{" "}
                          <b>{scheduleInterview.interviewer_name}</b>
                        </span>
                      </div>
                      <hr className="my-3" />
                      <div className="d-flex gap-4">
                        <div className="bottom-detail">
                          <div className="d-flex gap-3 align-items-center">
                            <span>
                              <Image
                                src={"/images/auth/undraw_profile.svg"}
                                className="img-profile rounded-circle mr-2"
                                width={24}
                                height={24}
                                alt=""
                              />
                            </span>
                            <div className="content">
                              <h4>{interview.candidate_name}</h4>
                              <p className="m-0">
                                {interview.candidate_designation}
                              </p>
                            </div>
                          </div>

                          <Link
                            className="btn btn-theme btn btn-primary"
                            href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}`.subdomainLink(
                              subdomain,
                            )}>
                            View Details
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              },
            )
          ) : (
            <div className="no-records">
              <p className="text-center">No Records Found</p>
            </div>
          )}
        </div>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
