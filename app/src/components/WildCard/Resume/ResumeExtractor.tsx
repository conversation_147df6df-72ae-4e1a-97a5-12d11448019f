import React, { useState } from "react";
import { But<PERSON>, Upload } from "antd";
import { FormGroup, FormLabel } from "react-bootstrap";
import ArchiveIcon from "@mui/icons-material/Archive";
import { useAppDispatch } from "@src/redux/store";
import { setLoader, setLoaderText } from "@src/redux/actions";
import { resumeExtractionApi } from "@src/apis/wildcardApis";
import { DynamicProfile } from "./DynamicProfile";
import flashMessage from "@src/components/FlashMessage";

export const ResumeExtractorComponent = () => {
  const [uploadedResume, setUploadResumeData] = useState<any>(null);
  const [resumeResponse, setResumeResponse] = useState<any>(null);
  const dispatch = useAppDispatch();

  // Function to validate the uploaded file before submission
  const beforeUpload = async (file: File) => {
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    const allowedFileTypes = [
      "ppt",
      "pptx",
      "pdf",
      "odt",
      "ods",
      "odp",
      "odg",
      "doc",
      "docx",
    ];
    if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
      flashMessage(
        `Only ${allowedFileTypes.join(", ")} files are accepted.`,
        "error",
      );
      return Upload.LIST_IGNORE;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      flashMessage("Maximum file upload size allowed is 2 MB.", "error");
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  // Function to handle file changes
  const handleChange = async (info: any) => {
    const { status } = info.file;
    if (!status || status === "error") {
      await setUploadResumeData(null);
      return null;
    }
    const file = info.fileList[info.fileList.length - 1];
    if (file) {
      setUploadResumeData(file.originFileObj);
    } else {
      setUploadResumeData(null);
    }
  };

  const onUploadProgress = async (progressEvent: any) => {
    const percentCompleted = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total,
    );
    await dispatch(setLoaderText(`Uploading ${percentCompleted}% `));
    if (percentCompleted === 100) {
      await dispatch(setLoaderText(`Extracting Details....`));
    }
  };

  // Function to upload the resume file and extract details
  const uploadResume = async () => {
    if (
      !uploadedResume ||
      (uploadedResume !== undefined &&
        uploadedResume !== null &&
        Object.keys(uploadedResume).length === 0) ||
      uploadedResume === undefined ||
      uploadedResume === null
    ) {
      flashMessage("Please upload the file.", "error");
      return;
    }

    setResumeResponse(null);
    const formData = new FormData();
    formData.append("file", uploadedResume);

    await dispatch(setLoader(true));
    const { success, ...response } =
      await resumeExtractionApi.EmployeeResumeExtraction(
        formData,
        onUploadProgress,
      );
    await dispatch(setLoader(false));
    await dispatch(setLoaderText(""));

    if (success) {
      setUploadResumeData(null);
      setResumeResponse(response.data);
    } else {
      flashMessage(response.message, "error");
    }
  };

  return (
    <>
      <div className="card resume-card p-5 pb-0">
        <div className="row">
          <div className="col-xl-4 col-md-6 mb-1">
            <FormGroup className="mb-3">
              <FormLabel style={{ color: "black", fontWeight: "bold" }}>
                Upload Resume<span style={{ color: "red" }}>*</span>
              </FormLabel>
              <Upload.Dragger
                name="file"
                multiple={false}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                onRemove={() => setUploadResumeData(null)}
                accept=".ppt,.pptx,.pdf,.odt,.ods,.odp,.odg,.doc,.docx"
                maxCount={1}
                fileList={uploadedResume ? [uploadedResume] : []}>
                <p className="ant-upload-drag-icon">
                  <ArchiveIcon fontSize="large" color="primary" />
                </p>
                <p className="ant-upload-text">
                  Click or drag file to this area to upload
                </p>
                <p className="ant-upload-text">
                  Maximum file upload size allowed is 2 MB.
                </p>
              </Upload.Dragger>

              <Button
                key="upload"
                className="btn btn-theme font-14 mt-2"
                onClick={uploadResume}
                disabled={!uploadedResume}>
                Upload
              </Button>
            </FormGroup>
          </div>

          <div className="col-xl-8 col-md-6">
            <FormGroup className="mb-3">
              <FormLabel style={{ color: "black", fontWeight: "bold" }}>
                Resume Details
              </FormLabel>
              <div className="resume-detail-overflow">
                {resumeResponse && <DynamicProfile data={resumeResponse} />}
              </div>
            </FormGroup>
          </div>
        </div>
      </div>
    </>
  );
};
