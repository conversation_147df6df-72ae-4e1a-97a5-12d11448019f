import React from "react";

interface DynamicData {
  [key: string]: any;
}

interface DynamicProfileProps {
  data: DynamicData;
}

// Function to format keys from snake_case to Title Case
const formatKey = (key: string): string => {
  return key
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

// Function to render data based on its type
const renderData = (key: string, value: any): JSX.Element => {
  if (Array.isArray(value)) {
    return (
      <div key={key} className="mt-2">
        <h5>{formatKey(key)}</h5>
        {value.map((item, index) => (
          <div key={index} className="item-array">
            {typeof item === "object" ? (
              <div className="item-object" key={index}>
                {renderObject(item)}
              </div>
            ) : (
              <p className="item-paragraph" key={index}>
                {item}
              </p>
            )}
          </div>
        ))}
      </div>
    );
  } else if (typeof value === "object" && value !== null) {
    return (
      <div key={key} className="mt-2">
        <h5>{formatKey(key)}</h5>
        {renderObject(value)}
      </div>
    );
  } else if (typeof value === "boolean") {
    return (
      <div key={key}>
        <strong>{formatKey(key)}:</strong> {value ? "Yes" : "No"}
      </div>
    );
  } else {
    return (
      <div key={key}>
        <strong>{formatKey(key)}:</strong> {value}
      </div>
    );
  }
};

// Function to render an object by iterating through its keys
const renderObject = (data: DynamicData): JSX.Element[] => {
  if (!data) return [<></>];
  return Object.keys(data).map((key) => renderData(key, data[key]));
};

// The main component to render the dynamic profile
export const DynamicProfile: React.FC<DynamicProfileProps> = ({ data }) => {
  try {
    return <div>{renderObject(data)}</div>;
  } catch (error) {
    console.error("Error rendering DynamicProfile:", error);
    return <div>Error rendering profile</div>;
  }
};
