import React from "react";
import { Pagination, Switch } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { DepartmentInterface } from "@src/redux/interfaces";
import {
  openDialog,
  setLoader,
  updateDepartmentDetail,
} from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { departmentApi } from "@src/apis/wildcardApis";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import flashMessage from "@src/components/FlashMessage";
import { Tooltip } from "antd";
import Link from "next/link";

type WildcardDepartmentListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  currentPagePermissions: string[];
  addNewButton?: React.ReactNode;
};

export function WildcardDepartmentList({
  fetchData,
  currentPagePermissions,
  addNewButton,
}: WildcardDepartmentListProps) {
  const dispatch = useAppDispatch();

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.department,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  // Update department status (active/inactive)
  const updateDepartmentStatus = async (department: DepartmentInterface) => {
    await dispatch(setLoader(true));
    const payload = { status: department.status == 1 ? 0 : 1 };
    const { success, ...response } = await departmentApi.updateDepartmentStatus(
      department.id,
      payload,
    );
    if (success) {
      reflectDepartmentDetail(department, response.data);
    }
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");
  };

  // Reflect updated department details
  const reflectDepartmentDetail = async (
    department: DepartmentInterface,
    data: DepartmentInterface,
  ) => {
    dispatch(
      updateDepartmentDetail({
        id: department.id,
        data: { ...department, ...data },
      }),
    );
  };

  // Open modal to confirm status change
  const openStatusChangeModal = (department: DepartmentInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Department Status Update Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to update the status of the department? This
              action may affect your current running functionality.
            </div>
          ),
          onConfirm: () => updateDepartmentStatus(department),
        },
      }),
    );
  };

  // Open modal to edit department details
  const openViewModal = (department: DepartmentInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.VIEW_DEPARTMENT_MODAL,
        options: {
          department: {
            name: department.name,
            description:
              department.description?.length > 0
                ? department.description
                : "N/A",
          },
        },
      }),
    );
  };

  // Open modal to edit department details
  const openEditModal = (department: DepartmentInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.EDIT_DEPARTMENT_MODAL,
        options: {
          department: department,
          onDepartmentUpdate: reflectDepartmentDetail,
        },
      }),
    );
  };

  const hasRows = rows && rows.length > 0;
  const hasEditPermission = currentPagePermissions.includes("edit");
  return (
    <>
      <div
        className={`table-responsive department-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              <th className="mw-100px">Department Name</th>
              <th className="mw-100px">Department Description</th>
              <th className="mw-80px">Created By</th>
              <th className="mw-80px">Date & Time</th>
              {hasEditPermission && (
                <>
                  <th className="mw-80px">Status</th>
                  <th>Actions</th>
                </>
              )}
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((department: DepartmentInterface, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>
                    <Link href={""} onClick={() => openViewModal(department)}>
                      {" "}
                      {department.name}{" "}
                    </Link>
                  </td>
                  <td className="query-30-chars">
                    {department.description?.trim().length > 0
                      ? department.description
                      : "N/A"}
                  </td>
                  <td>{department.created_by_name}</td>
                  <td>
                    {department.created_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  {hasEditPermission && (
                    <>
                      <td>
                        {department.is_default == 1 ? (
                          <Tooltip
                            title={
                              "You cannot change the status of a default department."
                            }>
                            <Switch
                              value={department.status == 1}
                              className="switch-theme"
                              disabled
                            />
                          </Tooltip>
                        ) : (
                          <Switch
                            value={department.status == 1}
                            className="switch-theme"
                            onChange={() => openStatusChangeModal(department)}
                          />
                        )}
                      </td>
                      <td>
                        <div className="department-action no-arrow">
                          <Dropdown>
                            <Dropdown.Toggle
                              id={`department-dropdown-${department.id}}`}
                              className="text-decoration-none"
                              as="span"
                              type="button"
                              role="button"
                              aria-haspopup="true"
                              aria-expanded="false">
                              <span className="text-black small">
                                <MoreVertIcon />
                              </span>
                            </Dropdown.Toggle>

                            <Dropdown.Menu
                              className="dropdown-menu-right shadow animated--grow-in"
                              aria-labelledby={`department-dropdown-${department.id}}`}>
                              <Dropdown.Item
                                eventKey="1"
                                role="button"
                                onClick={() => openViewModal(department)}>
                                View Detail
                              </Dropdown.Item>
                              {department.is_default == 1 ? (
                                <Tooltip
                                  title={
                                    "You cannot edit a default department."
                                  }>
                                  <Dropdown.Item eventKey="1" role="button">
                                    Edit Detail
                                  </Dropdown.Item>
                                </Tooltip>
                              ) : (
                                <Dropdown.Item
                                  eventKey="1"
                                  role="button"
                                  onClick={() => openEditModal(department)}>
                                  Edit Detail
                                </Dropdown.Item>
                              )}
                            </Dropdown.Menu>
                          </Dropdown>
                        </div>
                      </td>
                    </>
                  )}
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={hasEditPermission ? 7 : 5} className="text-center">
                  No Records Found
                  {addNewButton && (
                    <>
                      <br />
                      {addNewButton}
                    </>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
