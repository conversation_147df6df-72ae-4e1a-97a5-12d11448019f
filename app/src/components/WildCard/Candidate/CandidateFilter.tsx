import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  getAllCandidateStatusOptions,
  getAllCandidateQualificationOptions,
  getAllOpportunityOptions,
} from "@src/redux/actions";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import Image from "next/image";

type CandidateFilterProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void; // Function to call when the confirmation button is clicked
  close: Function; // Function to close the modal
};

const DefaultOpportunityFields: GlobalInputFieldType[] = [
  {
    name: "experience",
    label: "Experience",
    type: "text",
    dataType: "onlynumber",
    placeholder: "Enter Experience",
    strict: true,
    maxLength: 20,
    onEmptyNull: true,
  },
];

// Candidate Filter Component
export const CandidateFilter = ({
  filters,
  close,
  onConfirm,
}: CandidateFilterProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [loading, setLoading] = useState<boolean>(false);
  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );

  const {
    statusOptions: candidateStatusOptions,
    qualificationOptions: candidateQualificationOptions,
  } = useSelector((state: RootState) => state.candidate);

  useEffect(() => {
    fetchCandidateStatusOptions();
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const fetchOpportunitiesOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? filters.opportunity_id : showId;
    await setLoading(true);
    await dispatch(getAllOpportunityOptions({ search, showId }));
    await setLoading(false);
  };

  const fetchQualificationsOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? filters.qualification_id : showId;
    await setLoading(true);
    await dispatch(getAllCandidateQualificationOptions({ search, showId }));
    await setLoading(false);
  };

  const fetchCandidateStatusOptions = async () => {
    await setLoading(true);
    await dispatch(getAllCandidateStatusOptions({}));
    await setLoading(false);
  };

  const FilterFields: GlobalInputFieldType[] = [
    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "qualification_id",
      label: "Qualification",
      placeholder: "Select a Qualification",
      options: candidateQualificationOptions,
      onSearch: fetchQualificationsOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    ...DefaultOpportunityFields,
    {
      name: "status_id",
      label: "Candidate Status",
      placeholder: "Select status",
      options: candidateStatusOptions,

      type: "select",
      dataType: "select",
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "resume_upload_after",
      label: "Display Results From",
      placeholder: "Select Date",
      type: "date",
      dataType: "date",
      showSearch: false,
      selectEmpty: false,
    },
    {
      name: "include_blacklist",
      label: "Include Blacklisted Candidate",
      type: "checkbox",
      dataType: "boolean",
      selectEmpty: true,
      showSearch: false,
      loading: loading,
      className: "custom-check-box",
      customNode: (
        <span>
          <Image src="/images/check-green.svg" width={11} height={8} alt="" />
        </span>
      ),
    },
  ];

  const onSubmit = (state: KeyPairInterface) => {
    onConfirm(state);
    close();
  };

  return (
    <>
      <ModalFormInput
        state={state}
        setState={setState}
        fields={FilterFields}
        onSubmit={() => onSubmit(state)}
        onClose={() => onSubmit({})}
        buttonTitle="Apply Filters"
        cancelButtonTitle="Clear Filters"
        checkbox={true}
        checkboxKey="save_history"
        checkboxTitle="Save History"
      />
    </>
  );
};
