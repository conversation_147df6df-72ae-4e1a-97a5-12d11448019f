import React from "react";
import { Download as DownloadIcon } from "@mui/icons-material";
import { getFileNameFromPresignedUrl } from "@src/helper/common";
import { Button, ListGroup } from "react-bootstrap";
import Link from "next/link";
import { CandidateInterface } from "@src/redux/interfaces";
import Image from "next/image";
import { downloadFile } from "@src/helper/downloadFile";

type CandidateCardWithLinksProps = {
  candidate: CandidateInterface;
};

export const CandidateCardWithLinks = ({
  candidate,
}: CandidateCardWithLinksProps): JSX.Element => {
  return (
    <>
      <div className="card card-border">
        <div className="card-body">
          <div className="profile-box">
            <div className="profile-header">
              <div className="profile-img">
                <Image
                  src={"/images/auth/undraw_profile.svg"}
                  alt="Profile"
                  className="h-full w-full object-fit-cover"
                  width={70}
                  height={70}
                />
              </div>
              <div>
                <h6 className="mb-0">{candidate?.name}</h6>
                <span className="text-mute">{candidate?.designation}</span>
                <br></br>
                <small>Experience: {candidate?.total_experience} years</small>
              </div>
            </div>
            <div className="pb-0 mb-3 flex-wrap">
              <p className="mb-0 text-light-clr">Resume</p>
              <p className="mb-0 heading-clr">
                {candidate?.resume_url &&
                  getFileNameFromPresignedUrl(candidate?.resume_url)}
              </p>
            </div>
            <Button
              onClick={() =>
                candidate?.resume_url && downloadFile(candidate?.resume_url)
              }
              variant="danger"
              disabled={!candidate?.resume_url || candidate?.resume_url == ""}
              className="no-underline text-white download-btn mx-auto d-inline-flex align-items-center justify-content-center gap-2">
              <DownloadIcon className="material-icons" />
              Download
            </Button>
            <div className="candi-other-links">
              <div className="justify-content-center d-flex gap-2 align-items-center">
                {candidate?.website ||
                candidate?.github ||
                candidate?.linkedin ? (
                  <label className="text-light-clr">Social-Links:</label>
                ) : null}
                <ListGroup horizontal className="list-inline mb-0">
                  {candidate?.website ? (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate?.website} target="_blank">
                        <Image
                          src="/images/website.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  ) : null}
                  {candidate?.github ? (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate?.github} target="_blank">
                        <Image
                          src="/images/behance.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  ) : null}
                  {candidate?.linkedin ? (
                    <ListGroup.Item className="list-inline-item">
                      <Link href={candidate?.linkedin} target="_blank">
                        <Image
                          src="/images/dribbble.svg"
                          width={30}
                          height={20}
                          alt="icon"
                        />
                      </Link>
                    </ListGroup.Item>
                  ) : null}
                </ListGroup>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
