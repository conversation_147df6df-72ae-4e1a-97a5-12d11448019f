import React from "react";
import { CandidateSkillInterface } from "@src/redux/interfaces";

interface CandidateSkillsProps {
  skills: CandidateSkillInterface[];
}

export const CandidateSkills: React.FC<CandidateSkillsProps> = ({ skills }) => {
  if (!skills || skills.length == 0) {
    return null;
  }
  const technicalSkills = skills.filter(
    (skill) => skill.skill_type === "Technical",
  );
  const softSkills = skills.filter((skill) => skill.skill_type === "Soft");

  const groupBySubSkillType = (skills: CandidateSkillInterface[]) => {
    return skills.reduce<Record<string, CandidateSkillInterface[]>>(
      (acc, skill) => {
        const type = skill.sub_skill_type ?? "Uncategorized";
        if (!acc[type]) {
          acc[type] = [];
        }
        acc[type].push(skill);
        return acc;
      },
      {},
    );
  };

  const groupedSkills = groupBySubSkillType(technicalSkills);

  return (
    <div className="mb-3 mb-xl-4">
      <h6 className="text-light-clr mb-1">Skills</h6>
      {technicalSkills.length > 0 && (
        <div className="mb-3">
          <h6 className="mt-2 mb-2 primary-clr">Technical Skills</h6>
          <ul className="list-inline technical-skill">
            {/* {technicalSkills.map((skill) => (
              <li className="list-inline-item" key={skill.id}>
                {skill.name} ({skill.sub_skill_type ?? ''})
              </li>
            ))} */}

            {Object.entries(groupedSkills).map(([subSkillType, skills]) => (
              <li key={subSkillType} className="list-group-item">
                <strong>{subSkillType} :</strong>
                <ul className="list-inline">
                  {skills.map((skill) => (
                    <li className="list-inline-item m-1" key={skill.id}>
                      {skill.name}
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      )}

      {softSkills.length > 0 && (
        <div className="mb-3 mt-5">
          <h6 className="mt-2 mb-2 primary-clr">Soft Skills</h6>
          <ul className="list-inline soft-skill">
            {softSkills.map((skill) => (
              <li className="list-inline-item" key={skill.id}>
                {skill.name}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
