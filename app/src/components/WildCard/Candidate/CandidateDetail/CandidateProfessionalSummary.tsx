import React from "react";
import { CandidateInterface } from "@src/redux/interfaces";

interface CandidateProfessionalSummaryProps {
  candidate: CandidateInterface;
}

export const CandidateProfessionalSummary: React.FC<
  CandidateProfessionalSummaryProps
> = ({ candidate }) => (
  <div className="mb-3 mb-xl-4 border-bottom pb-3 professional-summary">
    <h6 className="text-light-clr mb-3">Professional Summary:</h6>
    <p className="mb-2">
      <span className="primary-clr">Designation:</span> {candidate.designation}
    </p>
    <p className="mb-2">
      <span className="primary-clr">Fresher:</span>{" "}
      {candidate.is_fresher ? "Yes" : "No"}
    </p>
    {!candidate.is_fresher && (
      <p className="mb-2">
        <span className="primary-clr">Total Experience:</span>{" "}
        {candidate.total_experience
          ? `${candidate.total_experience} years`
          : "N/A"}{" "}
      </p>
    )}
    {!candidate.is_fresher && (
      <p className="mb-2">
        <span className="primary-clr">Total Gap:</span>{" "}
        {candidate.total_gap && candidate.total_gap > 0
          ? `${candidate.total_gap} years`
          : "N/A"}
      </p>
    )}
    {/* <p className="mb-0">
      <span className="primary-clr">Current Status:</span>{" "}
      {candidate.status_name}
    </p> */}
    {candidate.opportunity_id && (
      <p className="mb-2">
        <span className="primary-clr">Job:</span> {candidate.opportunity_title}
      </p>
    )}
    <p className="mb-2">
      <span className="primary-clr">Uploaded by:</span>{" "}
      {candidate.created_by_name}
    </p>
    <p className="mb-0">
      <span className="primary-clr">Upload Date:</span>{" "}
      {(candidate.created_at ?? "").strftime("%B %d, %Y %I:%M %p", "UTC")}
    </p>
  </div>
);
