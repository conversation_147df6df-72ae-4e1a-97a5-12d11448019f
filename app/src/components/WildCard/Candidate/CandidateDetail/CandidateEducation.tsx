import React from "react";
import { CandidateEducationsInterface } from "@src/redux/interfaces";

interface CandidateEducationProps {
  educations: CandidateEducationsInterface[];
}

export const CandidateEducation: React.FC<CandidateEducationProps> = ({
  educations,
}) => {
  if (!educations || educations.length == 0) {
    return null;
  }

  return (
    <div className="mb-3 mb-xl-4 candi-experience">
      <h6 className="text-light-clr mb-1">Education:</h6>
      {educations.map((edu) => (
        <div className="mb-3" key={edu.id}>
          <ul className="list-inline candidate-experience">
            <li className="list-inline-item">
              <small className="primary-clr">{edu.university}</small>
            </li>
            {edu.end_date && (
              <>
                <li className="list-inline-item candidate-experience-divider ps-2" />
                <li className="list-inline-item position-relative">
                  <small className="text-light-clr">
                    {"Year "}
                    {edu?.start_date ? (
                      <>
                        {edu.start_date.substring(0, 4)}
                        {edu?.end_date
                          ? `-${edu.end_date.substring(0, 4)}`
                          : ""}
                      </>
                    ) : (
                      edu?.end_date?.substring(0, 4)
                    )}
                  </small>
                </li>
              </>
            )}
          </ul>
          <p className="mb-0">{edu.degree}</p>
        </div>
      ))}
    </div>
  );
};
