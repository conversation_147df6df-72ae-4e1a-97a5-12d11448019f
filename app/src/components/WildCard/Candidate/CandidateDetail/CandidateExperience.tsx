import React from "react";
import { CandidateExperienceInterface } from "@src/redux/interfaces";

interface CandidateExperienceProps {
  experiences: CandidateExperienceInterface[];
}

export const CandidateExperience: React.FC<CandidateExperienceProps> = ({
  experiences,
}) => {
  if (!experiences || experiences.length == 0) {
    return null;
  }

  const currentExperiences = experiences.filter(
    (exp) => exp.is_current_experience,
  );
  const pastExperiences = experiences.filter(
    (exp) => !exp.is_current_experience,
  );

  // This function is to coverting the normal date format into YYYY-MMM.
  const dateFormat = (date?: string) => {
    if (date) {
      // converting the string date into Date object
      const dateObject = new Date(date);

      // Extracting the year from the dateObject
      const year = dateObject.getFullYear();

      // Extracting the month from the dateObject
      const month = dateObject.toLocaleString("default", { month: "short" });

      // Combining into the single string.
      const formatDate = `${year}-${month}`;

      return formatDate;
    } else {
      return null;
    }
  };

  return (
    <>
      <div className="mb-3 mb-xl-4 border-bottom pb-3">
        <h6 className="text-light-clr mb-1">Experience:</h6>
        {currentExperiences.length > 0 && (
          <div>
            {currentExperiences.map((exp, index) => (
              <div className="candi-experience" key={index}>
                <div className="mb-3">
                  <ul className="list-inline candidate-experience">
                    <li className="list-inline-item">
                      <small className="primary-clr">
                        {" "}
                        {exp.position} at {exp.employer}
                      </small>
                    </li>
                    {exp.start_date ? (
                      <>
                        <li className="list-inline-item candidate-experience-divider ps-2" />
                        <li className="list-inline-item position-relative">
                          <small className="text-light-clr">
                            {dateFormat(exp?.start_date)} to{" "}
                            {dateFormat(exp?.end_date) ?? "Present"}
                          </small>
                          {!exp.end_date && (
                            <span className="currently-working">
                              Currently Working
                            </span>
                          )}
                        </li>
                      </>
                    ) : (
                      !exp.end_date && (
                        <li className="list-inline-item position-relative">
                          <span className="currently-working">
                            Currently Working
                          </span>
                        </li>
                      )
                    )}
                  </ul>
                  <ul className="candidate-responsibilities">
                    {exp.responsibilities.map((resp, index) => (
                      <li key={index}>{resp}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        )}

        {pastExperiences.length > 0 && (
          <div>
            {pastExperiences.map((exp, index) => (
              <div className="candi-experience" key={index}>
                <div className="mb-3">
                  <ul className="list-inline candidate-experience">
                    <li className="list-inline-item">
                      <small className="primary-clr">
                        {" "}
                        {exp.position} at {exp.employer}
                      </small>
                    </li>
                    {exp.start_date && (
                      <>
                        <li className="list-inline-item candidate-experience-divider ps-2" />
                        <li className="list-inline-item position-relative">
                          <small className="text-light-clr">
                            {dateFormat(exp.start_date)} to{" "}
                            {dateFormat(exp.end_date)}
                          </small>
                        </li>
                      </>
                    )}
                  </ul>
                  <p className="mb-0">
                    <ul className="candidate-responsibilities">
                      {exp.responsibilities.map((resp, index) => (
                        <li key={index}>{resp}</li>
                      ))}
                    </ul>
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
};
