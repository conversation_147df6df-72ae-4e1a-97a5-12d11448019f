import React, { useCallback, useEffect, useState } from "react";
import { CandidateProfileCommentInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";

interface ProfileCommentsProps {
  id: number;
  fetchFunction: (
    id: number,
    params?: any,
  ) => Promise<{ success: boolean; data: any }>;
  className?: string;
  reload: any;
}

export const CandidateProfileComments: React.FC<ProfileCommentsProps> = ({
  fetchFunction,
  id,
  className = "",
  reload,
}) => {
  const dispatch = useAppDispatch();
  const [comments, setComments] = useState<CandidateProfileCommentInterface[]>(
    [],
  );

  const fetchData = useCallback(async () => {
    dispatch(setLoader(true));
    try {
      const { success, data } = await fetchFunction(id);
      if (success) {
        setComments(data?.rows || []); // Assuming rows contain the array of comments.
      } else {
        setComments([]); // No comments available
      }
    } catch (error) {
      console.error("Error fetching comments:", error);
      setComments([]);
    } finally {
      dispatch(setLoader(false));
    }
  }, [dispatch, fetchFunction, id]);

  useEffect(() => {
    fetchData();
  }, [fetchData, reload]);

  const hasRows = comments.length > 0;

  return (
    <div className={className}>
      {hasRows ? (
        comments.map((comment, index) => (
          <div
            key={index}
            className="mb-3 mb-xl-4 border-bottom pb-3 professional-summary">
            <p className="mb-2">
              <span className="primary-clr">Comment By: </span>
              {comment.employee_name || "N/A"}
            </p>
            <p className="mb-2">
              <span className="primary-clr">Date: </span>
              {comment.created_at
                ? new Intl.DateTimeFormat("en-US", {
                    dateStyle: "medium",
                    timeStyle: "short",
                  }).format(new Date(comment.created_at))
                : "N/A"}
            </p>
            <p className="mb-2">{comment.comment || "N/A"}</p>
          </div>
        ))
      ) : (
        <div className="text-center">
          <h6>No Comments Available</h6>
        </div>
      )}
    </div>
  );
};
