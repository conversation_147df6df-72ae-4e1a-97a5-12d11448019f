import React from "react";
import { CandidateQualificationInterface } from "@src/redux/interfaces";

interface CandidateQualificationsProps {
  qualifications: CandidateQualificationInterface[];
}

export const CandidateQualifications: React.FC<
  CandidateQualificationsProps
> = ({ qualifications }) => {
  if (!qualifications || qualifications.length == 0) {
    return null;
  }

  return (
    <div className="col-md-6 col-lg-6 col-sm-12">
      <h3 className="text-theme mb-3">Qualifications</h3>
      <ul>
        {qualifications.map((qualification) => (
          <li key={qualification.id}>{qualification.name}</li>
        ))}
      </ul>
    </div>
  );
};
