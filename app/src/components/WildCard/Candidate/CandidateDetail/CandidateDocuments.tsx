import React, { useCallback, useEffect, useState } from "react";
import { CandidateDocumentInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { getFileNameFromPresignedUrl } from "@src/helper/common";
import { downloadFile } from "@src/helper/downloadFile";
import { Download as DownloadIcon } from "@mui/icons-material";
import Image from "next/image";

interface CandidateDocumentsProps {
  id: number;
  fetchFunction: (id: number, params?: any) => { success: boolean; data: any };
  className?: string;
}

export const CandidateDocuments: React.FC<CandidateDocumentsProps> = ({
  fetchFunction,
  id,
  className = "",
}) => {
  const dispatch = useAppDispatch();
  const [documents, setDocuments] = useState<any[]>([]);

  const fetchData = useCallback(
    async () => {
      await dispatch(setLoader(true));
      const { success, ...response } = await fetchFunction(id);
      if (success) {
        setDocuments(response.data);
      }
      await dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch],
  );

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const renderDynmicImage = (
    document: CandidateDocumentInterface,
  ): React.ReactElement => {
    const { content_type, document_url } = document;

    // Define a mapping of content types to icon paths
    const iconMap: { [key: string]: string } = {
      "application/pdf": "/images/mime/pdf.png",
      "application/vnd.oasis.opendocument.text": "/images/mime/odt.png",
      default: "/images/mime/doc.png",
    };

    // For image content types, use the document URL directly
    if (["image/png", "image/jpeg", "image/jpg"].includes(content_type)) {
      return (
        <Image
          src={document_url}
          alt="Document Image"
          width={40}
          height={50}
          className="candidate-document-logo"
        />
      );
    }

    // Use the icon map for document icons, defaulting if content_type is not found
    const iconSrc = iconMap[content_type] || iconMap["default"];

    // Return the corresponding image
    return (
      <Image
        src={iconSrc}
        alt={content_type.split("/")[1]}
        width={40}
        height={50}
        className="candidate-document-logo"
      />
    );
  };

  const hasRows = documents && documents.length > 0;

  return (
    <div className="row row-gap-3">
      {hasRows ? (
        <>
          {documents.map((document: CandidateDocumentInterface) => (
            <div
              className="d-flex justify-content-between candidate-document-card"
              key={document.id}>
              <div className="d-flex align-items-center w-100">
                <>{renderDynmicImage(document)}</>

                <p className="skill-info d-flex align-items-center m-4 gap-2 text-black">
                  {getFileNameFromPresignedUrl(document.document_url)}
                </p>
                <div className="edit-icons ms-auto gap-2 d-flex align-items-center">
                  <div
                    onClick={() => downloadFile(document.document_url)}
                    className="edit-icon cursor-pointer">
                    <DownloadIcon className="material-icons" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </>
      ) : (
        <>
          <div className="text-center">
            <h6>No Document Available</h6>
          </div>
        </>
      )}
    </div>
  );
};
