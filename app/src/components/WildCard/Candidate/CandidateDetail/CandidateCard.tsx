import React from "react";
import { CandidateInterface, KeyPairInterface } from "@src/redux/interfaces";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import Image from "next/image";
import {
  openDialog,
  setLoader,
  updateCandidateStatus,
} from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { Tooltip } from "antd";
import { Button } from "react-bootstrap";
import { encrypt, encryptString } from "@src/helper/encryption";
import { useAppDispatch } from "@src/redux/store";
import { downloadFile } from "@src/helper/downloadFile";
import DialogComponents from "@src/components/DialogComponents";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";
import { Info } from "@mui/icons-material";

interface CandidateCardViewProps {
  subdomain: string;
  candidate: CandidateInterface;
  openCandidateDeleteModal: (cadidate: CandidateInterface) => void;
  openCandidateWhitelistModal: (candidate: CandidateInterface) => void;
  currentPagePermissions: string[];
  currentEmployeeRole: string;
  currentEmployeeId: number;
  showShortist?: boolean;
}

export const CandidateCardView: React.FC<CandidateCardViewProps> = ({
  currentEmployeeRole,
  currentEmployeeId,
  candidate,
  openCandidateDeleteModal,
  openCandidateWhitelistModal,
  currentPagePermissions,
  showShortist = false,
}) => {
  const dispatch = useAppDispatch();
  const interviewsPermissions =
    useEmployeeSelectedPagePermissions("interviews");

  const onChangeCandidateStatus = async (status: number) => {
    await dispatch(setLoader(true));
    await dispatch(
      updateCandidateStatus(candidate.id, status, (success, response) => {
        flashMessage(response.message, success ? "success" : "error");
      }),
    );
    await dispatch(setLoader(false));
  };

  const renderScore = (score: KeyPairInterface) => {
    return (
      <>
        {Object.entries(score).map(([key, value]) => {
          if (key !== "total") {
            return (
              <div key={key} className="score-render">
                <strong>{toTitleCase(key)}</strong>: {value}%
              </div>
            );
          }
          return null;
        })}
      </>
    );
  };

  const openInterviewExistModal = (candidate: CandidateInterface) => {
    const interview = candidate.interviews && candidate.interviews[0];
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Interview already In-Progress",
          message: (
            <div className="mt-2 mb-2">
              The interview for this candidate has already been scheduled under
              the job
              <strong className="ml-1">
                {interview?.opportunity_title ?? ""}
              </strong>
              .
            </div>
          ),
        },
      }),
    );
  };

  const toTitleCase = (str: string): string => {
    // Replace underscores or camelCase with spaces and capitalize each word
    return str
      .replace(/([A-Z])/g, " $1") // Insert a space before capital letters
      .replace(/_/g, " ") // Replace underscores with spaces
      .replace(/\b\w/g, (char) => char.toUpperCase()) // Capitalize the first letter of each word
      .trim(); // Remove any leading or trailing whitespace
  };

  const hasEditPermission = currentPagePermissions.includes("edit");
  const hasDeletePermission = currentPagePermissions.includes("delete");
  const hasWriteInterviewPermission = interviewsPermissions.includes("write");
  const tooltipTitle = (
    <>
      The matching score represents the percentage alignment between a
      candidate&apos;s profile and a job&apos;s requirements.
      <br />
      You can view the matching percentage by hovering over the Matching Jobs
      Title
    </>
  );

  let interviewerOrAdminCandidate =
    (currentEmployeeRole == "Interviewer" &&
      currentEmployeeId == candidate.created_by_id) ||
    currentEmployeeRole != "Interviewer";

  let encryptedJobId: string | undefined =
    candidate.opportunity_id && candidate.opportunity_id != 0
      ? encryptString(candidate.opportunity_id.toString())
      : undefined;
  const jobParams = new URLSearchParams();
  if (encryptedJobId) {
    jobParams.append("job_id", encryptedJobId);
  }

  return (
    <>
      <div className="candidate-list-card bg-white p-3 border rounded-3">
        <div className="d-flex justify-content-between align-items-start mb-3 gap-2">
          <div className="candidate-des d-flex gap-3">
            <Image
              src={"/images/auth/undraw_profile.svg"}
              className="img-profile rounded-circle"
              width={40}
              height={40}
              alt=""
            />

            <div className="brief">
              <h4 className="heading-clr mb-2 cursor">
                {candidate.name || candidate.email}
              </h4>
              <p className="text-clr mb-0 cursor">{candidate.designation}</p>

              {candidate.blacklist == 1 && candidate.blacklist_reason && (
                <p className="text-clr mt-2">
                  <Tooltip
                    placement="top"
                    title={candidate.blacklist_reason}
                    trigger={"hover"}>
                    <span className="cursor-pointer">
                      Hover for Blacklist Reason
                    </span>
                  </Tooltip>
                </p>
              )}

              {candidate.scores && candidate.scores.total && (
                <p className="text-clr mt-2">
                  <Tooltip
                    placement="top"
                    title={renderScore(candidate.scores)}
                    trigger={"hover"}>
                    <span className="cursor-pointer">
                      Matching Score: {candidate.scores.total ?? 0}%
                    </span>
                  </Tooltip>
                  <Tooltip
                    placement="top"
                    title={tooltipTitle}
                    trigger={"hover"}>
                    <Info fontSize="small" className="ms-1 cursor-pointer" />
                  </Tooltip>
                </p>
              )}

              {candidate.job_scores &&
                Object.keys(candidate.job_scores).length > 0 && (
                  <p className="text-clr mt-2">
                    <Tooltip
                      placement="top"
                      title={renderScore(candidate.job_scores)}
                      trigger={"hover"}>
                      <span className="cursor-pointer">Matching Jobs</span>
                    </Tooltip>
                    <Tooltip
                      placement="top"
                      title={tooltipTitle}
                      trigger={"hover"}>
                      <Info fontSize="small" className="ms-1 cursor-pointer" />
                    </Tooltip>
                  </p>
                )}

              {/* <span className="badge bg-primary d-flex align-items-center justify-content-center px-2">
              {candidate.scores && candidate.scores.total && (
                <p className="text-clr mb-2">
                  Matching Score: {candidate.scores.total ?? 0}%
                </p>
              )} */}
              {/* {candidate.status_name == "Shortlisted" ? (
                <Badge
                  style={{ fontWeight: "300px", padding: "4px 10px" }}
                  className="mt-2 d-flex align-items-center justify-content-center px-2 candidatstatus">
                  <Check className="material-icons me-1 check-icon fw-thin" />{" "}
                  {candidate.status_name}
                </Badge>
              ) : null} */}
            </div>
          </div>

          <ul className="profile-more-option list-inline align-items-center mb-0 d-flex">
            {showShortist &&
              candidate.interview_status &&
              candidate.interview_job_title && (
                <Tooltip
                  placement="top"
                  title={`Interview status: ${candidate.interview_status} for the ${candidate.interview_job_title} job`}
                  trigger={"hover"}>
                  <span
                    className={`badge-round ms-auto cursor-pointer ${candidate.interview_status?.toLowerCase()}`}>
                    Status:{" "}
                    <b
                      className={`${candidate.interview_status.toLowerCase()}-text-color`}>
                      {candidate.interview_status}
                    </b>
                  </span>
                </Tooltip>
              )}
            {/* {candidate.blacklist == 0 && (
              <li className="list-inline-item">
                {candidate?.status == 0 ? (
                  <StarOutlineOutlinedIcon
                    className="material-icon cursor material-symbols-outlined"
                    onClick={() => onChangeCandidateStatus(2)}
                  />
                ) : (
                  <Star
                    className="material-icons cursor favorite material-symbols-outlined"
                    onClick={() => onChangeCandidateStatus(0)}
                  />
                )}
              </li>
            )} */}

            <li className="list-inline-item no-arrow">
              <Dropdown className="dropdown more-option">
                <Dropdown.Toggle
                  id={`candidate-dropdown-${candidate.id}}`}
                  className="text-decoration-none"
                  as="span"
                  type="button"
                  role="button"
                  aria-haspopup="true"
                  aria-expanded="false">
                  <span className="d-lg-inline text-black small">
                    <MoreVertIcon />
                  </span>
                </Dropdown.Toggle>

                <Dropdown.Menu
                  className="dropdown-menu-left shadow animated--grow-in pull-right"
                  aria-labelledby={`candidate-dropdown-${candidate.id}}`}>
                  <Link
                    href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}
                    shallow
                    className="no-decoration">
                    <Dropdown.Item
                      eventKey="1"
                      role="Link"
                      href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}
                      className="no-decoration">
                      View Detail
                    </Dropdown.Item>
                  </Link>
                  {hasEditPermission && interviewerOrAdminCandidate && (
                    <Link
                      href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/edit`}
                      shallow
                      className="no-decoration">
                      <Dropdown.Item
                        eventKey="2"
                        role="Link"
                        href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/edit`}
                        className="no-decoration">
                        Edit Detail
                      </Dropdown.Item>
                    </Link>
                  )}

                  {/* {hasEditPermission && (
                    <Link
                      href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${cryptr.encrypt(candidate.id.toString())}/interview`}
                      className="no-decoration">
                      <Dropdown.Item
                        eventKey="3"
                        role="Link"
                        href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${cryptr.encrypt(candidate.id.toString())}/interview`}
                        className="no-decoration">
                        Schedule Interview
                      </Dropdown.Item>
                    </Link>
                  )} */}

                  {(hasEditPermission || hasWriteInterviewPermission) &&
                    candidate.blacklist == 0 && (
                      <>
                        {candidate.has_interview ? (
                          <Dropdown.Item
                            eventKey="3"
                            onClick={() => openInterviewExistModal(candidate)}
                            className="no-decoration">
                            Schedule Interview
                          </Dropdown.Item>
                        ) : (
                          <Link
                            href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/interview?${jobParams.toString()}`}
                            shallow
                            area-disabled={candidate.has_interview}
                            className="no-decoration">
                            <Dropdown.Item
                              eventKey="3"
                              role="Link"
                              disabled={candidate.has_interview}
                              href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}/interview?${jobParams.toString()}`}
                              className="no-decoration">
                              Schedule Interview
                            </Dropdown.Item>
                          </Link>
                        )}
                      </>
                    )}
                  {hasEditPermission &&
                    interviewerOrAdminCandidate &&
                    (candidate.blacklist == 1 ? (
                      <Dropdown.Item
                        eventKey="4"
                        onClick={() => openCandidateWhitelistModal(candidate)}
                        className="no-decoration">
                        Whitelist
                      </Dropdown.Item>
                    ) : (
                      <Dropdown.Item
                        eventKey="4"
                        onClick={() => openCandidateDeleteModal(candidate)}
                        className="no-decoration">
                        Blacklist
                      </Dropdown.Item>
                    ))}
                </Dropdown.Menu>
              </Dropdown>
            </li>
          </ul>
        </div>
        <div className="d-flex mt-auto gap-3 tlgt-btn">
          <Button
            onClick={() => downloadFile(candidate.resume_url)}
            variant="light-border"
            disabled={!candidate.resume_url || candidate.resume_url == ""}
            className="btn w-100">
            <span>
              <Image
                src={"/images/icons/pdf-icon.svg"}
                className="mr-2"
                width={20}
                height={20}
                alt=""
              />
            </span>
            Download CV
          </Button>
          <Link
            href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(candidate.id.toString())}`}
            className="btn btn-light w-100">
            View Profile
          </Link>
        </div>
      </div>
    </>
  );
};
