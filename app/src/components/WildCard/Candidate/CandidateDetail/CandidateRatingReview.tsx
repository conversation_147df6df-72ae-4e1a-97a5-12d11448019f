import React, { useCallback, useEffect, useState } from "react";
import { FeedbackInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { Pagination } from "antd";
import { Card, CardBody } from "react-bootstrap";
import { StarRating } from "@src/components/Common/Rating";

interface CandidateRatingReviewProps {
  id: number;
  fetchFunction: (id: number, params: any) => { success: boolean; data: any };
  className?: string;
  showJob?: boolean;
}

interface FeedbackState {
  currentPage: number;
  limit: number;
  rows: FeedbackInterface[];
  count: number;
}

export const CandidateRatingReview: React.FC<CandidateRatingReviewProps> = ({
  fetchFunction,
  id,
  className = "",
  showJob = false,
}) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<FeedbackState>({
    currentPage: 1,
    limit: 19,
    rows: [],
    count: 0,
  });

  const fetchData = useCallback(
    async (currentPage: number, limit: number) => {
      await dispatch(setLoader(true));
      const { success, ...response } = await fetchFunction(id, {
        page: currentPage,
        limit: limit,
      });
      if (success) {
        const { page, rows, count, limit } = response.data;
        setState({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        });
      }
      await dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch, state.currentPage, state.limit],
  );

  useEffect(() => {
    fetchData(state.currentPage, state.limit);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const handlePageChange = async (page: number) => {
    await fetchData(page, state.limit);
  };

  const hasRows = state.rows && state.rows.length > 0;

  return (
    <div className="row">
      {hasRows ? (
        <>
          {state.rows.map((feedback: FeedbackInterface) => (
            <Card
              className={`mb-3 candidate-feedback ${className}`}
              key={feedback.id}>
              <CardBody>
                {showJob && (
                  <div className="mb-2">
                    <span>
                      <span className="text-placeholder">Job Title: </span>{" "}
                      {feedback.job_title}
                    </span>
                  </div>
                )}

                <div className="d-flex justify-content-between mb-2">
                  <span className="interview-round">
                    Round:{" "}
                    {feedback.interview_round.toString().padStart(2, "0")}
                  </span>

                  <span className="text-placeholder">
                    Dated: {feedback.created_at.strftime("%d %b, %Y")}
                  </span>
                </div>

                <div className="d-flex justify-content-between mb-2">
                  <StarRating rating={feedback.rating} />

                  <span>
                    <span className="text-placeholder">Interviewer: </span>{" "}
                    {feedback.created_by_name}
                  </span>
                </div>

                <div>
                  <p className="taing-feedback">{feedback.feedback}</p>
                </div>
              </CardBody>
            </Card>
          ))}
        </>
      ) : (
        <>
          <div className="text-center">
            <h6>No Rating Available</h6>
          </div>
        </>
      )}

      <Pagination
        className="mt-4"
        current={state.currentPage}
        total={state.count}
        pageSize={state.limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </div>
  );
};
