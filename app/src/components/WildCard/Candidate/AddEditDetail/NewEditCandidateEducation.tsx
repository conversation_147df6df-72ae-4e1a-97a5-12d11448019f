import React from "react";
import {
  CandidateNewEducationInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { Button } from "react-bootstrap";
import SchoolIcon from "@mui/icons-material/School";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";

type NewEditCandidateEducationType = {
  education_informations: Array<CandidateNewEducationInterface>;
  setState: React.Dispatch<React.SetStateAction<KeyPairInterface>>; // Function to update state
};

export const NewEditCandidateEducation: React.FC<
  NewEditCandidateEducationType
> = ({ education_informations, setState }) => {
  const dispatch = useAppDispatch();

  const buildDuration = (
    education: CandidateNewEducationInterface,
  ): JSX.Element => {
    return (
      <>
        {education.start_date} - {education.end_date}
      </>
    );
  };

  const openNewModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_EDUCATION_MODAL,
        options: {
          title: "Education Detail",
          education: {},
          onCandidateEducationUpdate: onCandidateEducationAdd,
        },
      }),
    );
  };

  const openEditModal = (
    education: CandidateNewEducationInterface,
    index: number,
  ) => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_EDUCATION_MODAL,
        options: {
          title: "Education Detail",
          education: education,
          onCandidateEducationUpdate: (
            education: CandidateNewEducationInterface,
          ) => onCandidateEducationUpdate(education, index),
        },
      }),
    );
  };

  const onCandidateEducationUpdate = (
    education: CandidateNewEducationInterface,
    index: number,
  ) => {
    const educationState = education_informations.map((item, i) =>
      i === index ? { ...item, ...education } : item,
    );
    setState((prevState) => ({
      ...prevState,
      education_informations: educationState,
    }));
  };

  const onCandidateEducationAdd = (
    education: CandidateNewEducationInterface,
  ) => {
    setState((prevState) => ({
      ...prevState,
      education_informations: [education, ...education_informations],
    }));
  };

  const onCandidateEducationDelete = (index: number) => {
    setState((prevState) => ({
      ...prevState,
      education_informations: prevState.education_informations.filter(
        (_: any, i: number) => i !== index,
      ),
    }));
  };

  const openDeleteModal = (index: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Education Detail Delete Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to delete education detail?
            </div>
          ),
          onConfirm: () => onCandidateEducationDelete(index),
        },
      }),
    );
  };

  return (
    <>
      <div className="card p-3">
        <div className="d-flex gap-3 flex-wrap align-items-center">
          <h6 className="m-0">Education:</h6>
          {education_informations.length > 0 && (
            <Button
              className="add-more ms-auto"
              // variant="custom-link"
              onClick={openNewModal}>
              + Add More
            </Button>
          )}
        </div>

        {education_informations.length == 0 ? (
          <>
            <div className="w-100 experience-row">
              <p className="add-more-detail">
                Effortlessly manage candidate education details by clicking the
                &quot;Add Candidate Education&quot; button. This feature enables
                you to seamlessly add new educational qualifications to the
                candidate profile.
              </p>

              <div className="text-center">
                <Button
                  onClick={openNewModal}
                  className="add-more"
                  // variant="custom-link"
                >
                  + Add Candidate Education
                </Button>
              </div>
            </div>
          </>
        ) : (
          <>
            {education_informations.map(
              (
                education_information: CandidateNewEducationInterface,
                index: number,
              ) => {
                return (
                  <>
                    <div className="education-row mt-3 sm-0">
                      <div className="box" key={index}>
                        <div className="type-icon">
                          <SchoolIcon
                            className="round-icon icon-primary"
                            fontSize="large"
                          />
                        </div>
                        <div className="content">
                          <div className="d-flex gap-2 flex-wrap flex-lg-nowrap">
                            <div className="head w-100">
                              <strong>{education_information.degree} </strong>
                              {education_information.grade && (
                                <>with {education_information.grade}%</>
                              )}

                              <p className="education-info m-0 ml-1">
                                {education_information.university}
                                <span className="divider" />
                                <span>
                                  {buildDuration(education_information)}
                                </span>
                              </p>
                            </div>

                            <div className="edit-icons ms-auto d-flex  gap-2">
                              <CreateIcon
                                className="edit-icon cursor-pointer"
                                fontSize="large"
                                onClick={() =>
                                  openEditModal(education_information, index)
                                }
                              />
                              <DeleteIcon
                                className="delete-icon cursor-pointer"
                                fontSize="large"
                                onClick={() => openDeleteModal(index)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                );
              },
            )}
          </>
        )}
      </div>
    </>
  );
};
