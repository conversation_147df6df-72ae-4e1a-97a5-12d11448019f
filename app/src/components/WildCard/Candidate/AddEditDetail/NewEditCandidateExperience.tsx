import React from "react";
import {
  CandidateNewExperienceInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { Button } from "react-bootstrap";
import WorkIcon from "@mui/icons-material/Work";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";

type NewEditCandidateExperienceType = {
  experiences: Array<CandidateNewExperienceInterface>;
  setState: React.Dispatch<React.SetStateAction<KeyPairInterface>>; // Function to update state
};

export const NewEditCandidateExperience: React.FC<
  NewEditCandidateExperienceType
> = ({ experiences, setState }) => {
  const dispatch = useAppDispatch();

  const calculateExperience = (
    experience: CandidateNewExperienceInterface,
  ): string => {
    if (!experience.start_date) {
      return "N/A";
    }
    const startDate = new Date(experience.start_date);
    const endDate = experience.is_current_experience
      ? new Date()
      : new Date(experience.end_date!);

    let totalMonths =
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth());
    if (endDate.getDate() < startDate.getDate()) {
      totalMonths--;
    }

    const years = Math.floor(totalMonths / 12);
    const months = totalMonths % 12;

    if (months === 0) {
      return `${years} ${years === 1 ? "year" : "years"}`;
    } else {
      return `${years} ${years === 1 ? "year" : "years"} ${months} ${months === 1 ? "month" : "months"}`;
    }
  };

  const buildDuration = (experience: CandidateNewExperienceInterface) => {
    return `${experience.start_date} - ${experience.is_current_experience ? "Present" : experience.end_date}`;
  };

  const openNewModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_EXPERIENCE_MODAL,
        options: {
          title: "Experience Detail",
          experience: {},
          onCandidateExperienceUpdate: onCandidateExperienceAdd,
        },
      }),
    );
  };

  const openEditModal = (
    experience: CandidateNewExperienceInterface,
    index: number,
  ) => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_EXPERIENCE_MODAL,
        options: {
          title: "Experience Detail",
          experience: experience,
          onCandidateExperienceUpdate: (
            experience: CandidateNewExperienceInterface,
          ) => onCandidateExperienceUpdate(experience, index),
        },
      }),
    );
  };

  const onCandidateExperienceUpdate = (
    experience: CandidateNewExperienceInterface,
    index: number,
  ) => {
    const experienceState = experiences.map((item, i) =>
      i === index ? { ...item, ...experience } : item,
    );
    setState((prevState) => ({ ...prevState, experiences: experienceState }));
  };

  const onCandidateExperienceAdd = (
    experience: CandidateNewExperienceInterface,
  ) => {
    setState((prevState) => ({
      ...prevState,
      experiences: [experience, ...experiences],
    }));
  };

  const onCandidateExperienceDelete = (index: number) => {
    setState((prevState) => ({
      ...prevState,
      experiences: prevState.experiences.filter(
        (_: any, i: number) => i !== index,
      ),
    }));
  };

  const openDeleteModal = (index: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Experience Detail Delete Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to delete experience detail?
            </div>
          ),
          onConfirm: () => onCandidateExperienceDelete(index),
        },
      }),
    );
  };

  return (
    <>
      <div className="card p-3">
        <div className="d-flex gap-3 flex-wrap align-items-center">
          <h6 className="m-0">Experiences:</h6>
          {experiences.length > 0 && (
            <Button
              className="add-more ms-auto"
              // variant="custom-link"
              onClick={openNewModal}>
              + Add More
            </Button>
          )}
        </div>

        {experiences.length == 0 ? (
          <>
            <div className="w-100 experience-row">
              <p className="add-more-detail">
                Effortlessly manage candidate experience details by clicking the
                &quot;Add Candidate Experience&quot; button. This feature
                enables you to seamlessly add new professional experiences to
                the candidate profile.
              </p>

              <div className="text-center">
                <Button
                  onClick={openNewModal}
                  className="add-more"
                  // variant="custom-link"
                >
                  + Add Candidate Experience
                </Button>
              </div>
            </div>
          </>
        ) : (
          <>
            {experiences.map(
              (experience: CandidateNewExperienceInterface, index: number) => {
                return (
                  <>
                    <div className="education-row mt-3 sm-0">
                      <div className="box" key={index}>
                        <div className="type-icon">
                          <WorkIcon
                            className="round-icon icon-primary"
                            fontSize="large"
                          />
                        </div>
                        <div className="content">
                          <div className="d-flex gap-2 flex-wrap flex-lg-nowrap">
                            <div className="d-flex gap-3 flex-wrap">
                              <div className="head w-100">
                                <strong>
                                  {experience.employer}
                                  {experience.is_current_experience && (
                                    <span className="currently-working">
                                      Currently Working
                                    </span>
                                  )}
                                </strong>
                                <p className="education-info m-0 ml-1">
                                  Position: <span>{experience.position}</span>
                                  <span className="divider" />
                                  Duration:{" "}
                                  <span>{buildDuration(experience)}</span>
                                  <span className="divider" />
                                  Experience:{" "}
                                  <span>{calculateExperience(experience)}</span>
                                </p>
                              </div>

                              {experience.responsibilities &&
                                experience.responsibilities.length > 0 && (
                                  <>
                                    <div className="head w-100">
                                      <strong>Responsibilities:</strong>
                                      <ul>
                                        {experience.responsibilities.map(
                                          (responsibility, index) => (
                                            <li key={index}>
                                              {responsibility}
                                            </li>
                                          ),
                                        )}
                                      </ul>
                                    </div>
                                  </>
                                )}
                            </div>

                            <div className="edit-icons ms-auto d-flex  gap-2">
                              <CreateIcon
                                className="edit-icon cursor-pointer"
                                fontSize="large"
                                onClick={() => openEditModal(experience, index)}
                              />
                              <DeleteIcon
                                className="delete-icon cursor-pointer ml-1"
                                fontSize="large"
                                onClick={() => openDeleteModal(index)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                );
              },
            )}
          </>
        )}
      </div>
    </>
  );
};
