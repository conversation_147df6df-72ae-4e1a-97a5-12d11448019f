import React from "react";
import {
  CandidateNewSkillInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { Button } from "react-bootstrap";
import CreateIcon from "@mui/icons-material/Create";
import DeleteIcon from "@mui/icons-material/Delete";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";

type NewEditCandidateSkillType = {
  skills: Array<CandidateNewSkillInterface>;
  setState: React.Dispatch<React.SetStateAction<KeyPairInterface>>; // Function to update state
};

export const NewEditCandidateSkill: React.FC<NewEditCandidateSkillType> = ({
  skills,
  setState,
}) => {
  const dispatch = useAppDispatch();

  const openNewModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_SKILL_MODAL,
        options: {
          title: "Skill Detail",
          skill: {},
          onCandidateSkillUpdate: onCandidateSkillAdd,
        },
      }),
    );
  };

  const openEditModal = (skill: CandidateNewSkillInterface, index: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CANDIDATE_EDIT_SKILL_MODAL,
        options: {
          title: "Skill Detail",
          skill: skill,
          onCandidateSkillUpdate: (skill: CandidateNewSkillInterface) =>
            onCandidateSkillUpdate(skill, index),
        },
      }),
    );
  };

  const onCandidateSkillUpdate = (
    skill: CandidateNewSkillInterface,
    index: number,
  ) => {
    const skillState = skills.map((item, i) =>
      i === index ? { ...item, ...skill } : item,
    );
    setState((prevState) => ({
      ...prevState,
      skills: skillState,
    }));
  };

  const onCandidateSkillAdd = (skill: CandidateNewSkillInterface) => {
    setState((prevState) => ({
      ...prevState,
      skills: [skill, ...skills],
    }));
  };

  const onCandidateSkillDelete = (index: number) => {
    setState((prevState) => ({
      ...prevState,
      skills: prevState.skills.filter((_: any, i: number) => i !== index),
    }));
  };

  const openDeleteModal = (index: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Skill Detail Delete Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to delete selected skill?
            </div>
          ),
          onConfirm: () => onCandidateSkillDelete(index),
        },
      }),
    );
  };

  return (
    <>
      <div className="card p-3">
        <div className="d-flex gap-3 flex-wrap align-items-center">
          <h6 className="m-0">Skills:</h6>
          {skills.length > 0 && (
            <Button
              className="add-more ms-auto"
              // variant="custom-link"
              onClick={openNewModal}>
              + Add More
            </Button>
          )}
        </div>

        {skills.length == 0 ? (
          <>
            <div className="w-100 experience-row">
              <p className="add-more-detail">
                Effortlessly manage candidate skills by clicking the &quot;Add
                Candidate Skill&quot; button. This feature enables you to
                seamlessly add new skills to the candidate profile.
              </p>

              <div className="text-center">
                <Button
                  onClick={openNewModal}
                  className="add-more"
                  // variant="custom-link"
                >
                  + Add Candidate Skill
                </Button>
              </div>
            </div>
          </>
        ) : (
          <>
            {skills.map((skill: CandidateNewSkillInterface, index: number) => {
              return (
                <>
                  <div
                    className="d-flex justify-content-between skill-row"
                    key={index}>
                    <div className="d-flex align-items-center w-100">
                      <p className="skill-info d-flex align-items-center m-0 gap-2">
                        <span className="skill-name">{skill.name}</span>
                        <span className="divider" />
                        <span className="skill-type">{skill.skill_type}</span>
                        {skill.skill_type == "Technical" && (
                          <>
                            <span className="divider" />
                            <span className="skill-type">
                              {skill.sub_skill_type}
                            </span>
                          </>
                        )}
                      </p>
                      <div className="edit-icons ms-auto gap-2 d-flex align-items-center">
                        <CreateIcon
                          className="edit-icon cursor-pointer"
                          fontSize="large"
                          onClick={() => openEditModal(skill, index)}
                        />
                        <DeleteIcon
                          className="delete-icon cursor-pointer"
                          fontSize="large"
                          onClick={() => openDeleteModal(index)}
                        />
                      </div>
                    </div>
                  </div>
                </>
              );
            })}
          </>
        )}
      </div>
    </>
  );
};
