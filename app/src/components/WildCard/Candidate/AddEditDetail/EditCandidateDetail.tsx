import React, { useEffect, useState, RefObject } from "react";
import {
  CandidateInterface,
  CandidateNewEducationInterface,
  CandidateNewExperienceInterface,
  CandidateNewSkillInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { NewEditCandidateExperience } from "./NewEditCandidateExperience";
import { NewEditCandidateEducation } from "./NewEditCandidateEducation";
import { NewEditCandidateSkill } from "./NewEditCandidateSkill";
import { YesNoOptions } from "@src/helper/selectOptions";

const SocialLinkField = {
  type: "text",
  dataType: "websiteurl",
  groupName: "Social Links",
  groupPosition: 1,
  className: "col-sm-12 col-md-4",
  maxLength: 50,
};

const FormFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Name",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 80,
    placeholder: "Candidate Name",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    placeholder: "Candidate Email",
    required: false,
    disabled: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "contact",
    label: "Phone",
    type: "mobilenumber",
    dataType: "mobilenumber",
    maxLength: 15,
    placeholder: "Candidate Phone Number",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "is_fresher",
    label: "Fresher",
    type: "select",
    dataType: "select",
    placeholder: "Select Fresher status",
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 4,
    options: YesNoOptions,
    className: "col-sm-12 col-md-4",
  },
  // add Total Experience Status
  {
    name: "designation",
    label: "Designation",
    type: "text",
    dataType: "dotWithOneAlphanumeric",
    maxLength: 50,
    placeholder: "Candidate Designation",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 6,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "website",
    label: "Website",
    placeholder: "Website Link",
    ...SocialLinkField,
  },
  {
    name: "github",
    label: "Github",
    placeholder: "Github Link",
    ...SocialLinkField,
  },
  {
    name: "linkedin",
    label: "Linkedin",
    placeholder: "Linkedin Link",
    ...SocialLinkField,
  },
];

type EditCandidateDetailType = {
  onSubmit: (data: any) => void;
  subdomain: string;
  candidate: CandidateInterface;
  submitRef?: RefObject<HTMLButtonElement>; // or any other HTML element type
};

export const EditCandidateDetail: React.FC<EditCandidateDetailType> = ({
  submitRef,
  onSubmit,
  candidate,
}) => {
  const [state, setState] = useState<KeyPairInterface>({});

  useEffect(() => {
    let experiences = [],
      education_informations = [],
      skills = [];
    if (candidate.skills && candidate.skills.length > 0) {
      for (const { name, skill_type, sub_skill_type } of candidate.skills) {
        skills.push({ name, skill_type, sub_skill_type });
      }
    }
    if (candidate.experiences && candidate.experiences.length > 0) {
      for (const {
        employer,
        position,
        start_date,
        end_date,
        responsibilities,
        is_current_experience,
      } of candidate.experiences) {
        experiences.push({
          employer,
          position,
          start_date,
          end_date,
          responsibilities,
          is_current_experience,
        });
      }
    }
    if (
      candidate.educations_informations &&
      candidate.educations_informations.length > 0
    ) {
      for (const {
        degree,
        university,
        grade,
        start_date,
        end_date,
      } of candidate.educations_informations) {
        education_informations.push({
          degree,
          university,
          grade,
          start_date,
          end_date,
        });
      }
    }
    setState({
      name: candidate.name,
      email: candidate.email,
      contact: (candidate.contact ?? "").replace(/\s+/g, "").replace(/-/g, ""),
      designation: candidate.designation,
      qualification: candidate.qualification,
      is_fresher: candidate.is_fresher,
      total_experience: (candidate.total_experience ?? 0).toString(),

      linkedin: candidate.linkedin,
      github: candidate.github,
      website: candidate.website,

      education_informations: education_informations,
      experiences: experiences,
      skills: skills,
    });
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const handleSubmit = () => {
    onSubmit({
      name: state.name,
      contact: state.contact,
      designation: state.designation,
      qualification: state.qualification,
      total_experience: state.total_experience,

      linkedin: state.linkedin,
      github: state.github,
      website: state.website,

      education_informations: state.education_informations,
      experiences: state.experiences,
      skills: state.skills,
    });
  };

  const EditFormFields: GlobalInputFieldType[] = [
    ...FormFields,
    {
      name: "total_experience",
      label: "Total Experience",
      type: "text",
      dataType: "decimalsingledigit",
      maxLength: 5,
      placeholder: "Candidate Experience",
      tooltipTitle: "Total Experience in Years",
      groupName: "Basic Details",
      groupPosition: 1,
      fieldPosition: 5,
      strict: true,
      disabled: state.is_fresher,
      required: !state.is_fresher,
      className: "col-sm-12 col-md-4",
    },
  ];

  return (
    <>
      <ModalFormInput
        buttonTitle="Update Detail" // Title for the submit button
        fields={EditFormFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={handleSubmit} // Function to handle form submission
        submitRef={submitRef}
        groupClasses={{
          "Basic Details": "card p-3",
          "Social Links": "card p-3",
        }}
        submitClass={"d-none"}
      />

      {state.education_informations && (
        <NewEditCandidateEducation
          education_informations={
            state.education_informations as CandidateNewEducationInterface[]
          }
          setState={setState}
        />
      )}

      {state.experiences && (
        <NewEditCandidateExperience
          experiences={state.experiences as CandidateNewExperienceInterface[]}
          setState={setState}
        />
      )}

      {state.skills && (
        <NewEditCandidateSkill
          skills={state.skills as CandidateNewSkillInterface[]}
          setState={setState}
        />
      )}
    </>
  );
};
