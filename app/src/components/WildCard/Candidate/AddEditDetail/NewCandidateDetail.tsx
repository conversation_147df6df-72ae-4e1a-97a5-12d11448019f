import React, { useEffect, useState, RefObject } from "react";
import {
  CandidateNewEducationInterface,
  CandidateNewExperienceInterface,
  CandidateNewSkillInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { NewEditCandidateExperience } from "./NewEditCandidateExperience";
import { NewEditCandidateEducation } from "./NewEditCandidateEducation";
import { NewEditCandidateSkill } from "./NewEditCandidateSkill";
import { YesNoOptions } from "@src/helper/selectOptions";

const SocialLinkField = {
  type: "text",
  dataType: "websiteurl",
  groupName: "Social Links",
  groupPosition: 1,
  className: "col-sm-12 col-md-4",
  maxLength: 50,
};

const FormFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Name",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 80,
    placeholder: "Candidate Name",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "email",
    label: "Email",
    type: "text",
    dataType: "string",
    placeholder: "Candidate Email",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "contact",
    label: "Phone",
    type: "mobilenumber",
    dataType: "mobilenumber",
    maxLength: 15,
    placeholder: "Candidate Phone Number",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "is_fresher",
    label: "Fresher",
    type: "select",
    dataType: "select",
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 4,
    options: YesNoOptions,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "designation",
    label: "Designation",
    type: "text",
    dataType: "dotWithOneAlphanumeric",
    maxLength: 50,
    placeholder: "Candidate Designation",
    required: true,
    groupName: "Basic Details",
    groupPosition: 1,
    fieldPosition: 6,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "website",
    label: "Website",
    placeholder: "Website Link",
    ...SocialLinkField,
  },
  {
    name: "github",
    label: "Github",
    placeholder: "Github Link",
    ...SocialLinkField,
  },
  {
    name: "linkedin",
    label: "Linkedin",
    placeholder: "Linkedin Link",
    ...SocialLinkField,
  },
];

type NewCandidateDetailType = {
  onSubmit: (data: any) => void;
  subdomain: string;
  submitRef?: RefObject<HTMLButtonElement>; // or any other HTML element type
};

export const NewCandidateDetail: React.FC<NewCandidateDetailType> = ({
  submitRef,
  onSubmit,
}) => {
  const [state, setState] = useState<KeyPairInterface>({});

  useEffect(() => {
    setState({
      name: "",
      email: "",
      contact: "",
      designation: "",
      qualification: "",
      total_experience: null,
      is_fresher: false,

      linkedin: "",
      github: "",
      website: "",

      education_informations: [],
      experiences: [],
      skills: [],
    });
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const handleSubmit = () => {
    onSubmit({
      name: state.name,
      email: state.email,
      contact: state.contact,
      designation: state.designation,
      qualification: state.qualification,
      total_experience: state.total_experience,

      linkedin: state.linkedin,
      github: state.github,
      website: state.website,
      opportunity_id: state.opportunity_id,

      education_informations: state.education_informations,
      experiences: state.experiences,
      skills: state.skills,
    });
  };

  const NewFormFields: GlobalInputFieldType[] = [
    ...FormFields,
    {
      name: "total_experience",
      label: "Total Experience",
      type: "text",
      dataType: "decimalsingledigit",
      maxLength: 4,
      placeholder: "Candidate Experience",
      tooltipTitle: "Total Experience in Years",
      groupName: "Basic Details",
      groupPosition: 1,
      fieldPosition: 5,
      strict: true,
      disabled: state.is_fresher,
      required: !state.is_fresher,
      className: "col-sm-12 col-md-4",
    },
  ];

  return (
    <>
      <ModalFormInput
        buttonTitle="Add Candidate" // Title for the submit button
        fields={NewFormFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={handleSubmit} // Function to handle form submission
        submitRef={submitRef}
        groupClasses={{
          "Basic Details": "card p-3",
          "Social Links": "card p-3",
        }}
        submitClass={"d-none"}
      />

      {state.education_informations && (
        <NewEditCandidateEducation
          education_informations={
            state.education_informations as CandidateNewEducationInterface[]
          }
          setState={setState}
        />
      )}

      {state.experiences && (
        <NewEditCandidateExperience
          experiences={state.experiences as CandidateNewExperienceInterface[]}
          setState={setState}
        />
      )}

      {state.skills && (
        <NewEditCandidateSkill
          skills={state.skills as CandidateNewSkillInterface[]}
          setState={setState}
        />
      )}
    </>
  );
};
