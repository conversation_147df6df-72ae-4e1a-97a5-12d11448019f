import { CandidateBasicInterface } from "@src/redux/interfaces";
import Image from "next/image";
import { Button } from "react-bootstrap";

interface CandidateBasicCardViewProps {
  candidate: CandidateBasicInterface;
}

export const CandidateBasicCardView: React.FC<CandidateBasicCardViewProps> = ({
  candidate,
}) => {
  return (
    <li>
      <span className="profile-img">
        <Image
          src={"/images/auth/undraw_profile.svg"}
          className="img-profile rounded-circle"
          width={48}
          height={48}
          alt=""
        />
      </span>
      <div className="content-box">
        <div className="data">
          <h5>{candidate.name}</h5>
          <p>{candidate.designation}</p>
          {/* <span className="finalize">
                        <img src="/images/thumb_up.svg" alt="thumb_up" />{" "}
                        finalize
                    </span> */}
        </div>
        <div className="buttons-group">
          <Button className="btn btn-outline-primary">Interview Track</Button>
          <Button className="btn btn-primary border-0">
            Send Offer Letter
          </Button>
        </div>
      </div>
    </li>
  );
};
