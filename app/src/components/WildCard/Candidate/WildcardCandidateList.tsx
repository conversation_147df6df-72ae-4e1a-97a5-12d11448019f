import React from "react";
import { Pagination } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { CandidateInterface } from "@src/redux/interfaces";
import { CandidateCardView } from "./CandidateDetail";

type WildcardCandidateListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  sendEmail: (cadidate: CandidateInterface) => void;
  openCandidateDeleteModal: (cadidate: CandidateInterface) => void;
  openCandidateWhitelistModal: (cadidate: CandidateInterface) => void;
  currentPagePermissions: string[];
  currentEmployeeRole: string;
  currentEmployeeId: number;
};

export function WildcardCandidateList({
  fetchData,
  ...props
}: WildcardCandidateListProps) {
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.candidate,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;
  return (
    <>
      <div className="candidate-list-wrap p-3">
        <div className="row row-gap-3 candidate-row-list">
          {hasRows ? (
            rows.map((candidate: CandidateInterface, index: number) => {
              return (
                <div
                  className="col-12 col-md-12 col-xl-4 candidate-column"
                  key={candidate.id}>
                  <CandidateCardView
                    candidate={candidate}
                    {...props}
                    showShortist={true}
                  />
                </div>
              );
            })
          ) : (
            <>
              <div className="w-100">
                <p className="text-center m-0">No Record Available</p>
              </div>
            </>
          )}
        </div>
      </div>
      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
