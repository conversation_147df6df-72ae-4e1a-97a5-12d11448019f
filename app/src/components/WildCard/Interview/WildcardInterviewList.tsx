import React from "react";
import { Pa<PERSON><PERSON>, Tooltip, Card } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { InterviewInterface, PageDetailInterface } from "@src/redux/interfaces";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { encrypt } from "@src/helper/encryption";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { DropdownLink } from "@src/helper/actions";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";

type WildcardInterviewListProps = {
  fetchData: (page: number, limit: number) => void;
  subdomain: string;
  currentPagePermissions: string[];
  pageDetail: PageDetailInterface;
};

export function WildcardInterviewList({
  subdomain,
  fetchData,
  currentPagePermissions,
  pageDetail,
}: WildcardInterviewListProps) {
  const dispatch = useAppDispatch();
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");
  const candidatePermissions = useEmployeeSelectedPagePermissions("candidates");
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.interview,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const checkDisabled = (interview: InterviewInterface) => {
    try {
      const hasProvideFeedback: boolean = !!(
        interview.latest_feedback &&
        interview.latest_feedback.created_by_id == interview.created_by_id &&
        interview.latest_feedback.interview_round == interview.interview_round
      );

      const feedbackDisabled = new Date(interview.interview_at);
      return hasProvideFeedback || feedbackDisabled > new Date();
    } catch (e) {
      return false;
    }
  };

  const openFeedbackModal = (interview: InterviewInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.SAVE_INTERVIEW_FEEDBACK,
        options: {
          interview: interview,
        },
      }),
    );
  };

  const currentEmployeeCreated = (id: number) => {
    if (!currentEmployee) {
      return false;
    }

    return currentEmployee.id == id;
  };

  const hasRows = rows && rows.length > 0;
  const hasEditPermission = currentPagePermissions.includes("edit");
  const isAdmin = ["Super Admin", "Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  return (
    <>
      <div
        className={`table-responsive interview-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              <th className="mw-100px">Job</th>
              <th className="mw-100px">Candidate</th>
              <th className="mw-100px">Interviewer</th>
              <th className="mw-100px">Interview At</th>
              <th className="mw-100px">Interview Mode</th>
              <th className="mw-80px">Created By</th>
              <th className="mw-80px">Modified At</th>
              <th className="mw-80px">Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((interview: InterviewInterface, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>
                    {jobPermissions.includes("read") ? (
                      <Link
                        className=""
                        href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/${interview.opportunity_id}`}>
                        {interview.opportunity_title}
                      </Link>
                    ) : (
                      <>{interview.opportunity_title}</>
                    )}
                  </td>
                  <td>
                    {candidatePermissions.includes("read") &&
                    interview.candidate_exist ? (
                      <Link
                        className=""
                        href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(interview.candidate_id.toString())}`}>
                        {interview.candidate_name}
                      </Link>
                    ) : (
                      <Tooltip
                        placement="top"
                        color="white"
                        title={
                          <>
                            <Card title="Candidate Detail">
                              <div className="mb-1">
                                Name: {interview.candidate_name}
                              </div>
                              <div className="mb-1">
                                Email: {interview.candidate_email}
                              </div>
                            </Card>
                          </>
                        }
                        trigger={"hover"}>
                        <div className="cursor">{interview.candidate_name}</div>
                      </Tooltip>
                    )}
                  </td>
                  <td>
                    <Tooltip
                      placement="top"
                      color="white"
                      title={
                        <>
                          <Card title="Interviewer Detail">
                            <div className="mb-1">
                              Name: {interview.interviewer_name}
                            </div>
                            <div className="mb-1">
                              Email: {interview.interviewer_email}
                            </div>
                          </Card>
                        </>
                      }
                      trigger={"hover"}>
                      <div className="cursor">{interview.interviewer_name}</div>
                    </Tooltip>
                  </td>
                  <td>
                    {interview.interview_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>{interview.interview_mode_name}</td>
                  <td>{interview.created_by_name}</td>
                  <td>
                    {interview.updated_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                    <span
                      className={`interview-status ${interview.status_name.toLowerCase()}`}>
                      {interview.status_name}
                    </span>
                  </td>
                  <td>
                    <div className="interview-action no-arrow">
                      <Dropdown>
                        <Dropdown.Toggle
                          id={`interview-dropdown-${interview.id}}`}
                          className="text-decoration-none"
                          as="span"
                          type="button"
                          role="button"
                          aria-haspopup="true"
                          aria-expanded="false">
                          <span className="text-black small">
                            <MoreVertIcon />
                          </span>
                        </Dropdown.Toggle>

                        <Dropdown.Menu
                          className="dropdown-menu-right shadow animated--grow-in"
                          aria-labelledby={`interview-dropdown-${interview.id}}`}>
                          <DropdownLink
                            linkHref={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}`.subdomainLink(
                              subdomain,
                            )}>
                            View Detail
                          </DropdownLink>

                          <DropdownLink
                            linkHref={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}/histories`.subdomainLink(
                              subdomain,
                            )}>
                            View Histories
                          </DropdownLink>

                          {currentEmployee?.id === interview.interviewer_id &&
                            interview.candidate_exist && (
                              <>
                                <Dropdown.Item
                                  onClick={() => openFeedbackModal(interview)}
                                  disabled={checkDisabled(interview)}
                                  eventKey="2">
                                  Give Feedback
                                </Dropdown.Item>
                              </>
                            )}

                          {[
                            "Scheduled",
                            "Rescheduled",
                            "Awaiting Feedback",
                          ].includes(interview.status_name) &&
                            interview.candidate_exist && (
                              <DropdownLink
                                linkHref={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${interview.id}/edit`.subdomainLink(
                                  subdomain,
                                )}
                                disabled={
                                  !(
                                    isAdmin ||
                                    currentEmployeeCreated(
                                      interview.created_by_id ?? 0,
                                    )
                                  )
                                }
                                hidden={!hasEditPermission}>
                                Edit Detail
                              </DropdownLink>
                            )}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={10} className="text-center">
                  No Records Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
