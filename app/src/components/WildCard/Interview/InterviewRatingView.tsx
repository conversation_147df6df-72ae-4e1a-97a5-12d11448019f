import React from "react";
import { StarRating } from "@src/components/StarRating";
import { FeedbackInterface } from "@src/redux/interfaces";

type InterviewRatingView = {
  feedback: FeedbackInterface;
  showStatus?: boolean;
};

export const InterviewRatingView: React.FC<InterviewRatingView> = ({
  feedback,
  showStatus = true,
}) => {
  return (
    <>
      <div className="raiting mt-3">
        <div className="stars">
          <StarRating rating={feedback.rating} />
        </div>
        {showStatus && (
          <p>
            Approved Status:{" "}
            <span
              className={
                feedback.approved_status == "Qualified"
                  ? "green-highlight"
                  : "text-danger"
              }>
              {feedback.approved_status}
            </span>
          </p>
        )}
        {feedback.feedback && feedback.feedback.trim().length > 0 ? (
          <p>Feedback: {feedback.feedback}</p>
        ) : null}
      </div>
    </>
  );
};
