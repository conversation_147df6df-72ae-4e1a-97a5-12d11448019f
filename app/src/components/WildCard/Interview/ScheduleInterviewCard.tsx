import React from "react";
import {
  AuthEmployeeInterface,
  CandidateInterviewInterface,
  InterviewInterface,
} from "@src/redux/interfaces";
import { InterviewRatingView } from "./InterviewRatingView";
import { <PERSON><PERSON> } from "react-bootstrap";
import Link from "next/link";
import { useAppDispatch } from "@src/redux/store";
import DialogComponents from "@src/components/DialogComponents";
import {
  openDialog,
  setLoader,
  updateInterviewStatus,
} from "@src/redux/actions";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";
import Image from "next/image";
import { Tooltip } from "antd";
import { encryptString } from "@src/helper/encryption";

type ScheduleInterviewCard = {
  interview: InterviewInterface;
  currentEmployee: AuthEmployeeInterface;
  isLast?: boolean;
  hasEditPermission: boolean;
  currentInterview: CandidateInterviewInterface;
};

export const ScheduleInterviewCard: React.FC<ScheduleInterviewCard> = ({
  interview,
  currentEmployee,
  isLast,
  hasEditPermission,
  currentInterview,
}) => {
  const dispatch = useAppDispatch();

  const openFeedbackModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.SAVE_INTERVIEW_FEEDBACK,
        options: {
          interview: interview,
        },
      }),
    );
  };

  if (!interview) {
    return <></>;
  }

  const onUpdateCandidateInterview = async (status: number) => {
    if (interview) {
      dispatch(setLoader(true));
      dispatch(
        updateInterviewStatus(interview?.id, status, (success, response) => {
          if (success) {
            flashMessage(response.message, "success");
          }
        }),
      );
      dispatch(setLoader(false));
    }
  };

  const openStatusChangeModal = (status: string, statusId: number) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Candidate Interview Status Update",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to interview status to{" "}
              <strong>{status}</strong>?
              <br />
              This action will not reverted.
            </div>
          ),
          onConfirm: () => onUpdateCandidateInterview(statusId),
        },
      }),
    );
  };

  const isTimeGreater = new Date() > new Date(interview.interview_at);
  const validInterviwerOrRole =
    currentEmployee?.employee_role == "Interviewer"
      ? currentEmployee?.id === interview.interviewer_id
      : !!currentEmployee;

  return (
    <>
      <li className="active">
        <div className="box">
          <div className="sub-head">
            <span className="round">
              Round:{" "}
              <b>{interview.interview_round.toString().padStart(2, "0")}</b>
            </span>
            <span className={`badge ${interview.status_name}`}>
              {interview.status_name}
            </span>
          </div>
          <div className="head">
            <h4>
              <b>Date & Time:</b>
              {interview.interview_at?.strftime("%d %b, %Y | %I:%M %p ")}
              <span className={interview.interview_mode_name}>
                ({interview.interview_mode_name})
              </span>
              <p>
                Interviewer: <span>{interview.interviewer_name}</span>
              </p>
            </h4>
            {interview.interview_mode_name == "Video Call" &&
              interview.meeting_link && (
                <h4 className="mt-3">
                  <b>Meeting Link: </b>
                  <Link
                    href={`${interview.meeting_link ?? ""}`}
                    target="_blank">
                    {interview.meeting_link}
                  </Link>
                </h4>
              )}
            {interview.interview_mode_name == "Screening" &&
              interview.status != 1 && (
                <>
                  <p>
                    <span>Score: {interview.score}% &nbsp;</span>
                    <Link
                      href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${currentInterview.id}/review/${encryptString(`interview_id=${interview.id}&interview_type=screening`)}`}
                      target="_blank">
                      View Answer Sheet
                    </Link>
                  </p>
                </>
              )}
          </div>
          {interview.comment && (
            <div className="comments">
              <h4 className="mt-3">
                <b>Comment:</b>
                <div>
                  <span>{interview.comment}</span>
                </div>
              </h4>
            </div>
          )}
          {interview.feedback && (
            <InterviewRatingView feedback={interview.feedback} />
          )}

          <div className="btns-bottom">
            {hasEditPermission &&
              [1].includes(interview.status) &&
              validInterviwerOrRole &&
              interview.interview_mode_name != "Screening" && (
                <>
                  {isTimeGreater ? (
                    <>
                      <Button
                        className="btn btn-outline-success"
                        onClick={() => openStatusChangeModal("Passed", 4)}>
                        <Image
                          src="/images/check-green.svg"
                          alt="check-green"
                          width={10}
                          height={9}
                        />{" "}
                        Passed
                      </Button>
                      <Button
                        className="btn btn-outline-reject"
                        onClick={() => openStatusChangeModal("Rejected", 3)}>
                        <Image
                          src="/images/cross-red.svg"
                          alt="cross-red"
                          width={11}
                          height={10}
                        />{" "}
                        Reject
                      </Button>
                    </>
                  ) : (
                    <>
                      <Tooltip
                        placement="top"
                        title={
                          "You can update the status after the interview time."
                        }
                        trigger={"hover"}>
                        <>
                          <Button
                            className="btn btn-outline-success cursor-pointer"
                            disabled>
                            <Image
                              src="/images/check-green.svg"
                              alt="check-green"
                              width={10}
                              height={9}
                            />{" "}
                            Passed
                          </Button>
                        </>
                      </Tooltip>
                      <Tooltip
                        placement="top"
                        title={
                          "You can update the status after the interview time."
                        }
                        trigger={"hover"}>
                        <>
                          <Button
                            className="btn btn-outline-reject cursor-pointer"
                            disabled>
                            <Image
                              src="/images/cross-red.svg"
                              alt="cross-red"
                              width={11}
                              height={10}
                            />{" "}
                            Reject
                          </Button>
                        </>
                      </Tooltip>
                    </>
                  )}
                  {isLast && !interview?.feedback && (
                    <Link
                      href={`${APP_ROUTE.INTERVIEW_MANAGEMENT}/${currentInterview.id}/reschedule?round=${interview.interview_round}`}>
                      <Button className="btn btn-primary border-0">
                        Reschedule Interview
                      </Button>
                    </Link>
                  )}
                </>
              )}
            {/* <Button className="btn btn-outline-primary border-0">
              View Questions
            </Button> */}
            {currentEmployee?.id === interview.interviewer_id &&
              !interview?.feedback &&
              [1].includes(interview.status) &&
              interview.interview_mode_name != "Screening" &&
              isTimeGreater && (
                <Button
                  className={"btn btn-outline-primary border-0"}
                  onClick={openFeedbackModal}>
                  Provide Feedback
                </Button>
              )}
          </div>
        </div>
      </li>
    </>
  );
};
