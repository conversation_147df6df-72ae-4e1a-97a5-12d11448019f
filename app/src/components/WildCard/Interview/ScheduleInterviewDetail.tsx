import React, { useEffect, useState } from "react";
import { InterviewInterface, KeyPairInterface } from "@src/redux/interfaces";
import { GlobalReadOnlyFieldType } from "@src/components/input/GlobalInput";
import { ReadOnlyField } from "@src/components/input/ReadOnlyField";

const FormFields: GlobalReadOnlyFieldType[] = [
  {
    name: "candidate_name",
    label: "Candidate Name",
    groupName: "Candidate & Job Infomation",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "candidate_email",
    label: "Candidate Email",
    groupName: "Candidate & Job Infomation",
    groupPosition: 1,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "opportunity_title",
    label: "Job Title",
    groupName: "Candidate & Job Infomation",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "interviewer_name",
    label: "Interviewer",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "interview_at",
    label: "Interview Time",
    type: "datetime",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "status_name",
    label: "Interview Status",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "interview_round",
    label: "Interview Round",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 4,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "interview_mode_name",
    label: "Interview Mode",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 5,
    className: "col-sm-12 col-md-8",
  },

  {
    name: "comment",
    label: "Comment",
    groupName: "Interview Detail",
    groupPosition: 1,
    fieldPosition: 7,
  },
];

type ScheduleInterviewType = {
  readonly interview: InterviewInterface;
  subdomain: string;
};

export const ScheduleInterviewDetail: React.FC<ScheduleInterviewType> = ({
  interview,
}) => {
  const [state, setState] = useState<KeyPairInterface>({});

  useEffect(() => {
    setState({
      ...interview,
      comment: (interview.comment ?? "").length > 0 ? interview.comment : "N/A",
      interview_at:
        interview?.interview_at?.strftime("%B %d, %Y %I:%M %p") ?? "",
    });
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [interview]);

  let InterviewFormFields = [...FormFields];

  if (state.interview_mode_name == "Video_Call") {
    InterviewFormFields = [
      ...FormFields,
      {
        name: "meeting_link",
        label: "Meeting Link",
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 6,
        className: "col-sm-12 col-md-8",
      },
    ];
  }

  return (
    <>
      <ReadOnlyField fields={InterviewFormFields} state={state} />
    </>
  );
};
