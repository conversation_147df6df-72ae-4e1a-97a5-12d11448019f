import React from "react";
import { Pa<PERSON><PERSON>, Toolt<PERSON>, Card } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { InterviewInterface } from "@src/redux/interfaces";

type WildcardInterviewHistoryListProps = {
  fetchData: (page: number, limit: number) => void;
};

export function WildcardInterviewHistoryList({
  fetchData,
}: WildcardInterviewHistoryListProps) {
  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.interview.histories,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  const hasRows = rows && rows.length > 0;
  return (
    <>
      <div
        className={`table-responsive interview-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              <th className="mw-100px">Interviewer</th>
              <th className="mw-100px">Interview At</th>
              <th className="mw-100px">Interview Mode</th>
              <th className="mw-100px">Round No</th>
              <th className="mw-80px">Created By</th>
              <th className="mw-80px">Modified At</th>
              <th className="mw-80px">Status</th>
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((interview: InterviewInterface, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  <td>
                    <Tooltip
                      placement="top"
                      color="white"
                      title={
                        <>
                          <Card title="Interviewer Detail">
                            <div className="mb-1">
                              Name: {interview.interviewer_name}
                            </div>
                            <div className="mb-1">
                              Email: {interview.interviewer_email}
                            </div>
                          </Card>
                        </>
                      }
                      trigger={"hover"}>
                      <div className="cursor">{interview.interviewer_name}</div>
                    </Tooltip>
                  </td>
                  <td>
                    {interview.interview_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>{interview.interview_mode_name}</td>
                  <td>
                    {interview.interview_round.toString().padStart(2, "0")}
                  </td>
                  <td>{interview.created_by_name}</td>
                  <td>
                    {interview.updated_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                    <span
                      className={`interview-status ${interview.status_name.toLowerCase()}`}>
                      {interview.status_name}
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={9} className="text-center">
                  No Records Found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
