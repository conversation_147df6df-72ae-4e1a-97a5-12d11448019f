import React, { useEffect, useState } from "react";
import { InterviewInterface, KeyPairInterface } from "@src/redux/interfaces";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";
import { useAppDispatch } from "@src/redux/store";
import { getAllEmployeeOptions, setLoader } from "@src/redux/actions";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { interviewApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { useRouter } from "next/router";
import { InterviewModeOptions } from "@src/helper/selectOptions";

const FormFields: GlobalInputFieldType[] = [
  {
    name: "candidate_name",
    label: "Candidate Name",
    type: "text",
    dataType: "string",
    placeholder: "Candidate Name",
    required: false,
    disabled: true,
    groupName: "Candidate & Job Infomation",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-4",
  },
  {
    name: "candidate_email",
    label: "Candidate Email",
    type: "text",
    dataType: "string",
    placeholder: "Candidate Email",
    required: false,
    disabled: true,
    groupName: "Candidate & Job Infomation",
    groupPosition: 1,
    fieldPosition: 2,
    className: "col-sm-12 col-md-4",
  },

  {
    name: "opportunity_title",
    label: "Job",
    placeholder: "Select Job",
    type: "text",
    dataType: "text",
    groupName: "Candidate & Job Infomation",
    disabled: true,
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12 col-md-4",
  },
];

const InterviewStatusOption: Array<{ value: string | number; label: string }> =
  [
    { value: 0, label: "Cancelled" },
    { value: 1, label: "Scheduled" },
    { value: 2, label: "Rescheduled" },
    { value: 3, label: "Rejected" },
    { value: 4, label: "Passed" },
    { value: 5, label: "No Show" },
    { value: 6, label: "Awaiting Feedback" },
  ];

const FeedbackInterviewStatusOption: Array<{
  value: string | number;
  label: string;
  disabled?: boolean;
}> = [
  { value: 0, label: "Cancelled", disabled: true },
  { value: 1, label: "Scheduled", disabled: true },
  { value: 2, label: "Rescheduled", disabled: true },
  { value: 3, label: "Rejected" },
  { value: 4, label: "Passed" },
  { value: 5, label: "No Show", disabled: true },
  { value: 6, label: "Awaiting Feedback" },
];

const RescheduledInterviewStatusOption: Array<{
  value: string | number;
  label: string;
  disabled?: boolean;
}> = [
  { value: 0, label: "Cancelled" },
  { value: 1, label: "Scheduled", disabled: true },
  { value: 2, label: "Rescheduled" },
  { value: 3, label: "Rejected" },
  { value: 4, label: "Passed" },
  { value: 5, label: "No Show" },
  { value: 6, label: "Awaiting Feedback" },
];

type ScheduleInterviewType = {
  readonly interview: InterviewInterface;
  subdomain: string;
};

const InterviewRoundOptions: Array<{ value: string | number; label: string }> =
  Array.from({ length: 20 }, (_, index) => ({
    label: `${index + 1}`,
    value: index + 1,
  }));

export const EditScheduleInterview: React.FC<ScheduleInterviewType> = ({
  interview,
}) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [state, setState] = useState<KeyPairInterface>({});
  const [loading, setLoading] = useState<boolean>(false);
  const employeeOptions = useSelector(
    (state: RootState) => state.employee.options,
  );

  useEffect(() => {
    setState({
      candidate_name: interview.candidate_name,
      candidate_email: interview.candidate_email,
      interview_round: interview.interview_round,
      interviewer_id: interview.interviewer_id,
      interview_at: interview.interview_at,
      meeting_link: interview.meeting_link,
      opportunity_id: interview.opportunity_id,
      comment: interview.comment,
      status: interview.status,
      opportunity_title: interview.opportunity_title,
      interview_mode_id: interview.interview_mode_id,
    });
    if (
      !["Scheduled", "Rescheduled", "Awaiting Feedback"].includes(
        interview.status_name,
      )
    ) {
      router.replace(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [interview]);

  const handleSubmit = async () => {
    await dispatch(setLoader(true));
    const { success, message } = await interviewApi.updateInterview(
      interview.id,
      {
        interviewer_id: state.interviewer_id,
        interview_at: state.interview_at,
        meeting_link: state.meeting_link,
        interview_round: state.interview_round,
        opportunity_id: state.opportunity_id,
        interview_mode_id: state.interview_mode_id,
        comment: state.comment,
        status: state.status,
      },
    );
    await dispatch(setLoader(false));
    flashMessage(message, success ? "success" : "error");
    if (success) {
      router.push(APP_ROUTE.INTERVIEW_MANAGEMENT);
    }
  };

  const fetchEmployeeOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? interview.interviewer_id : showId;
    await setLoading(true);
    await dispatch(
      getAllEmployeeOptions({
        search: search,
        showId: showId,
        type: "Interviewer",
      }),
    );
    await setLoading(false);
  };

  let NewFormFields: GlobalInputFieldType[] = [
    ...FormFields,
    {
      name: "interviewer_id",
      label: "Interviewer",
      type: "select",
      dataType: "select",
      placeholder: "Select Interviewer",
      required: ![0, 3, 4, 5, 6].includes(state.status),
      showSearch: true,
      selectEmpty: true,
      options: employeeOptions,
      onSearch: fetchEmployeeOptions,
      loading: loading,
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 1,
      disabled: [0, 3, 4, 5, 6].includes(state.status),
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_at",
      label: "Interview Time",
      type: "datetime",
      dataType: "datetime",
      placeholder: "Interview Date & Time",
      required: ![0, 3, 4, 5, 6].includes(state.status),
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 2,
      className: "col-sm-12 col-md-4",
      dateFormat: "MMMM d, yyyy h:mm aa",
      disabled: [0, 3, 4, 5, 6].includes(state.status),
      minDate: new Date().toISOString(),
    },

    {
      name: "status",
      label: "Interview Status",
      type: "select",
      dataType: "select",
      placeholder: "Select Interview Status",
      required: true,
      options: interview
        ? interview.status == 6
          ? FeedbackInterviewStatusOption
          : interview.status == 2
            ? RescheduledInterviewStatusOption
            : InterviewStatusOption
        : [],
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 3,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_round",
      label: "Interview Round",
      type: "select",
      dataType: "select",
      placeholder: "Select Interview Round",
      required: ![0, 3, 4, 5, 6].includes(state.status),
      options: InterviewRoundOptions,
      groupName: "Interview Detail",
      disabled: [0, 3, 4, 5, 6].includes(state.status),
      groupPosition: 1,
      fieldPosition: 4,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "interview_mode_id",
      label: "Interview Mode",
      type: "select",
      dataType: "select",
      placeholder: "Select Mode",
      options: InterviewModeOptions,
      required: ![0, 3, 4, 5, 6].includes(state.status),
      disabled: [0, 3, 4, 5, 6].includes(state.status),
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 5,
      className: "col-sm-12 col-md-4",
    },

    {
      name: "comment",
      label: "Additional Notes",
      type: "textarea",
      dataType: "text",
      placeholder: "Add Notes",
      tooltipTitle:
        "Enter any additional content or message you wish to include in the email",
      required: [0, 3, 4, 5, 6].includes(state.status),
      groupName: "Interview Detail",
      groupPosition: 1,
      fieldPosition: 7,
    },
  ];

  if (state.interview_mode_id == 0) {
    NewFormFields = [
      ...NewFormFields,
      {
        name: "meeting_link",
        label: "Meeting Link",
        type: "text",
        dataType: "websiteurl",
        placeholder: "Enter Meeting Link",
        required: true,
        groupName: "Interview Detail",
        groupPosition: 1,
        fieldPosition: 6,
        className: "col-sm-12 col-md-12",
      },
    ];
  }

  return (
    <>
      <ModalFormInput
        buttonTitle="Save Detail" // Title for the submit button
        fields={NewFormFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={handleSubmit} // Function to handle form submission
      />
    </>
  );
};
