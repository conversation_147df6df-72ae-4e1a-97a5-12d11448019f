import React, { useEffect, useState } from "react";
import Image from "next/image";
import { Button } from "react-bootstrap";
import { wildcardBusinessesApi } from "@src/apis/wildcardApis";
import { useAppDispatch } from "@src/redux/store";
import { getDashboardStats, openDialog } from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
// import { Tooltip } from "antd";

interface BusinessType {
  id: number;
  name: string;
  email: string;
  website: string;
  subdomain: string;
  contact_number: string;
  location: string;
  email_verified: boolean;
  payment_status: number;
  status: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  plan_name: string;
}

// Payment Interface
interface PaymentType {
  id: number;
  business_id: number;
  stripe_payment_id: string;
  amount_received: number;
  currency: string;
  event_status: string; // e.g., "succeeded", "failed"
  payment_plan_id: number;
  credit: number;
  payment_intent_id: string;
  payment_method: string;
}

// Payment Plan Interface
interface PaymentPlanType {
  id: number;
  name: string;
  description: string;
  amount: string; // or number if you prefer
  currency: string;
  credit: number;
  created_at: string; // ISO date string
}

type StatsState = null | {
  business: BusinessType;
  payment?: PaymentType;
  payment_plan?: PaymentPlanType;
  expired_soon: boolean;
};

export const WildcardPaymentStats = () => {
  // State for current labels and values
  const [stats, setStats] = useState<StatsState>(null);
  const dispatch = useAppDispatch();

  useEffect(() => {
    getBusinessDetail();
  }, []);

  // Function to get stats from API and update the stats value
  const getBusinessDetail = async () => {
    const { success, data } = await wildcardBusinessesApi.currentBusinessInfo();
    if (success) {
      setStats((prev: StatsState) => ({ ...prev, ...data }));
    }
  };

  const fetchStats = async () => {
    await dispatch(getDashboardStats());
    await getBusinessDetail();
  };

  const openPlanUpgradeModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.UPGRADE_PLAN_MODAL,
        options: {
          title: "Upgrade Payment Plan",
          payment_plan: stats?.payment_plan,
          onPayment: fetchStats,
        },
      }),
    );
  };

  const renewPlanModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.UPGRADE_PLAN_MODAL,
        options: {
          title: "Renew Payment Plan",
          selected_plan: stats?.payment_plan,
          payment_plan: stats?.payment_plan,
          onPayment: fetchStats,
        },
      }),
    );
  };

  if (!stats) {
    return <></>;
  }

  return (
    <>
      <div className="card subscription-card">
        <div className="text-center position-relative z-1">
          <div className="ds-subscribe-content">
            <Image
              className="mb-2"
              src="/images/subscription-vector.svg"
              alt="icon"
              width={150}
              height={150}
            />
            <p className="mb-1 ds-plan-active d-flex align-items-center gap-2 justify-content-center">
              Current Plan:
              <span className="badge px-3">
                {(stats.payment_plan?.name ?? "Basic").toUpperCase()}
              </span>
            </p>
            <p className="text-white">
              {stats.payment_plan?.description ?? "-"}
            </p>
            <hr />

            <div className="d-inline-grid">
              {(stats.payment_plan?.name ?? "Basic").toLowerCase() ==
              "premium" ? (
                <></>
              ) : (
                // <Tooltip
                //   placement="top"
                //   title={"You already have a premium plan"}
                //   trigger={"hover"}>
                //   <Button className="btn rounded-2 mb-2" variant="primary">
                //     Upgrade Your Plan
                //   </Button>
                // </Tooltip>
                <Button
                  className="btn rounded-2 mb-2"
                  variant="primary"
                  onClick={openPlanUpgradeModal}>
                  Upgrade Your Plan
                </Button>
              )}

              {/* Renew Plan */}
              {stats.expired_soon && (
                <Button
                  className="btn rounded-2"
                  variant="danger"
                  onClick={renewPlanModal}>
                  Renew Your Plan
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
