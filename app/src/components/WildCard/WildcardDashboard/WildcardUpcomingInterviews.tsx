import Image from "next/image";
import { useEffect, useState } from "react";
import { interviewApi } from "@src/apis/wildcardApis";
import { NewInterviewInterface } from "@src/redux/interfaces";
import { APP_ROUTE } from "@src/constants";
import { useDispatch } from "react-redux";
import { setLoader } from "@src/redux/actions";
import Link from "next/link";

export const WildCardUpcomingInterviews = () => {
  const [interviews, setInterviews] = useState<NewInterviewInterface[]>([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchUpcomingInterviews = async () => {
      dispatch(setLoader(true));
      try {
        const params = {
          limit: 4,
          filter: "upcoming",
        };
        const { success, data } = await interviewApi.getInterviewsList(params);

        if (success) {
          setInterviews(data.rows || []);
        } else {
          setInterviews([]); // Reset interviews on API failure
        }
      } catch (error) {
        console.error("Error fetching interviews:", error);
        setInterviews([]);
      } finally {
        dispatch(setLoader(false));
      }
    };

    fetchUpcomingInterviews();
  }, []);

  const hasRows = interviews && interviews.length > 0;

  return (
    <>
      <div className="card">
        <div className="card-header border-bottom p-0">
          <h5 className="heading-clr p-3 mb-0">Upcoming Interviews</h5>
        </div>

        <div className="card-body">
          {hasRows ? (
            <>
              {interviews.map((interview: NewInterviewInterface) => (
                <div
                  className="ds-schedule-wrap d-flex gap-3 mb-4"
                  key={`interview-${interview.candidate_id}`}>
                  <span className="d-flex justify-content-center align-items-center">
                    <Image
                      src={"/images/calender.png"}
                      width={32}
                      height={32}
                      alt=""
                    />
                  </span>
                  <div>
                    <h5 className="heading-clr">
                      {interview.opportunity_title}
                    </h5>
                    <p className="text-clr mb-0">
                      {new Date(interview.interview_at).toLocaleString() ??
                        "N/A"}
                    </p>
                  </div>
                </div>
              ))}
              <div className="text-center">
                <Link
                  href={APP_ROUTE.INTERVIEW_MANAGEMENT}
                  className="btn btn-light w-100 rounded-0 light-theme-button mt-3">
                  View Schedule
                </Link>
              </div>
            </>
          ) : (
            <p className="text-clr mb-0 text-center pb-2">
              No Upcoming Interviews
            </p>
          )}
        </div>
      </div>
    </>
  );
};
