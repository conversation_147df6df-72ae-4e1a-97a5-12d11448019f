import React, { useEffect, useState } from "react";
import { dashboardApi } from "@src/apis/wildcardApis";
import Image from "next/image";
import { CurrentTime } from "@src/components/Common";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { WildcardDashboardStats } from "./WildcardDashboardStats";
import { WildcardPaymentStats } from "./WildcardPaymentStats";
import { WildCardUpcomingInterviews } from "./WildcardUpcomingInterviews";
import { WildcardCandidateStats } from "./WildcardCandidateStats";
import { Button } from "react-bootstrap";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { useAppDispatch } from "@src/redux/store";
import { getDashboardStats } from "@src/redux/actions";
import { useEmployeeSelectedPagePermissions } from "@src/helper/pagePermissions";

type JobStatsState = {
  job_id: number;
  job_description: string;
  posted_on: string;
  applicants: number;
  status: number;
};

type WildcardDashboardComponentProps = {
  subdomain: string;
};

export const WildcardDashboardComponent: React.FC<
  WildcardDashboardComponentProps
> = ({ subdomain }) => {
  const dispatch = useAppDispatch();
  const jobPermissions = useEmployeeSelectedPagePermissions("opportunities");

  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );
  const stats = useSelector((state: RootState) => state.dashboard.stats);

  const [jobstats, setJobStats] = useState<JobStatsState[]>([]);

  useEffect(() => {
    dispatch(getDashboardStats());
    getDashboardJobStats();
  }, [dispatch]);

  const getDashboardJobStats = async () => {
    const { success, data } = await dashboardApi.dashboardJobStats();
    if (success) {
      setJobStats(data);
    }
  };

  const isSuperAdmin = currentEmployee?.employee_role == "Super Admin";
  // adding the interviewer role to manage the total candidate role
  const Interviewer = currentEmployee?.employee_role == "Interviewer";
  const isAdmin = ["Admin", "Super Admin"].includes(
    currentEmployee?.employee_role ?? "",
  );

  return (
    <>
      <div className="container-fluid p-0">
        <div className="dashboard-pattern-top">
          <div className="d-flex justify-content-between align-items-end mb-4 row-gap-mobile flex-wrap flex-lg-nowrap relative">
            <div className="welcome-heading">
              <h1 className="text-white">Welcome!</h1>
              <p className="mb-0 text-white">
                Check Out What&apos;s Happening. Here Are Your Key Updates For
                The Day.
              </p>
            </div>
            <CurrentTime />
          </div>

          {(isSuperAdmin ||
            stats.has_job_permission ||
            stats.has_interview_permission ||
            stats.has_candidate_permission) && (
            <div className="card bg-transparent shadow-none">
              <div className="row row-gap-mobile row-gap-3">
                {(stats.has_job_permission || isSuperAdmin) && (
                  <>
                    <div className="col-xl-3 col-lg-12">
                      <div className="stat-card stat-card1 d-flex justify-content-between p-3">
                        <div className="stat-left">
                          <p className="mb-0 d-flex align-items-center text-clr">
                            {isAdmin ? (
                              <>
                                Jobs Active
                                <span className="ms-3">
                                  {stats.jobs_active}
                                </span>
                              </>
                            ) : (
                              <>
                                Total Jobs Created
                                <span className="ms-3">
                                  {stats.jobs_active}
                                </span>
                              </>
                            )}
                          </p>
                          <label className="mb-0 text-clr">
                            <strong className="me-2">
                              {stats.total_jobs_last_week}
                            </strong>
                            jobs added last week
                          </label>
                        </div>
                        <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                          <Image
                            width={50}
                            height={50}
                            src="/images/stat-icon1.svg"
                            alt="icon"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {(stats.has_candidate_permission || isSuperAdmin) &&
                  !Interviewer && (
                    <>
                      <div className="col-xl-3 col-lg-12">
                        <div className="stat-card stat-card2 d-flex justify-content-between p-3">
                          <div className="stat-left">
                            <p className="mb-0 d-flex align-items-center text-clr">
                              Total Candidates
                              <span className="ms-3">
                                {stats.total_candidates}
                              </span>
                            </p>
                            <label className="mb-0 text-clr">
                              <strong className="me-2">
                                {stats.total_candidates_last_week}
                              </strong>
                              candidates added last week
                            </label>
                          </div>
                          <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                            <Image
                              width={50}
                              height={50}
                              src="/images/stat-icon2.svg"
                              alt="icon"
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                {Interviewer && (
                  <div className="col-xl-3 col-lg-12">
                    <div className="stat-card stat-card3 d-flex justify-content-between p-3">
                      <div className="stat-left">
                        <p className="mb-0 d-flex align-items-center text-clr">
                          Total Hire
                          <span className="ms-3">{/* Manage value*/}4</span>
                        </p>
                        <label className="mb-0 text-clr">
                          <strong className="me-2">0</strong>
                          Schedule Interview
                        </label>
                      </div>
                      <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                        <Image
                          width={50}
                          height={50}
                          src="/images/stat-icon7.svg"
                          alt="icon"
                        />
                      </div>
                    </div>
                  </div>
                )}
                {(stats.has_interview_permission || isSuperAdmin) && (
                  <>
                    <div className="col-xl-3 col-lg-12">
                      <div className="stat-card stat-card3 d-flex justify-content-between p-3">
                        <div className="stat-left">
                          <p className="mb-0 d-flex align-items-center text-clr">
                            {isAdmin ? (
                              <>
                                Total Shortlisted
                                <span className="ms-3">
                                  {stats.total_shortlisted}
                                </span>
                              </>
                            ) : (
                              <>
                                Total Interviews Assigned
                                <span className="ms-3">
                                  {stats.total_interviews}
                                </span>
                              </>
                            )}
                          </p>
                          {isAdmin ? (
                            <label className="mb-0 text-clr">
                              <strong className="me-2">
                                {stats.total_interviews_last_week}
                              </strong>
                              interviewed last week
                            </label>
                          ) : (
                            <label className="mb-0 text-clr">
                              <strong className="me-2">
                                {stats.total_completed_interviews}
                              </strong>
                              Completed Interviews
                            </label>
                          )}
                        </div>
                        <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                          <Image
                            width={50}
                            height={50}
                            src="/images/stat-icon3.svg"
                            alt="icon"
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {/* add the condition to show the card to admin and super-admin */}
                {(stats.has_candidate_permission || isSuperAdmin) &&
                  !Interviewer && (
                    <div className="col-xl-3 col-lg-12">
                      <div className="stat-card stat-card5 d-flex justify-content-between p-3">
                        <div className="stat-left">
                          <p className="mb-0 d-flex align-items-center text-clr">
                            Total Credits
                            <span className="ms-3">{stats.total_credits}</span>
                          </p>
                          <label className="mb-0 text-clr">
                            <strong className="me-2">
                              {stats.remaining_credits}
                            </strong>
                            Credit Remaining
                          </label>
                        </div>
                        <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                          <Image
                            width={50}
                            height={50}
                            src="/images/stat-icon6.svg"
                            alt="icon"
                          />
                        </div>
                      </div>
                    </div>
                  )}
              </div>
            </div>
          )}
        </div>

        {!isAdmin && stats.has_interview_permission && (
          <WildcardDashboardStats />
        )}

        {(stats.has_job_permission || isSuperAdmin) && (
          <>
            <div className="row row-gap-mobile row-gap-3">
              <div
                className={
                  isSuperAdmin ? "col-lg-12 col-xl-8" : "col-lg-12 col-xl-12"
                }>
                <div className="card p-4 m-0 mb-3">
                  <div className="card-header common-heading p-0 mb-3 border-0 bg-white d-flex align-items-center gap-3">
                    <div className="w-100">
                      <h4 className="mb-2 heading-clr">Recent Jobs </h4>
                      <p className="mb-0 text-light-clr">
                        Keep a track of all your job postings in one place. You
                        can also view the date of the job posting, job title,
                        number of interviews scheduled, and job status.
                      </p>
                    </div>
                    {jobstats.length > 0 && (
                      <Link href={APP_ROUTE.OPPORTUNITY_MANAGEMENT}>
                        <Button className="btn btn-theme ms-auto btn btn-primary">
                          View All
                        </Button>
                      </Link>
                    )}
                  </div>
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th scope="col">Job ID</th>
                          <th scope="col">Job Title</th>
                          <th scope="col">Posted on</th>
                          <th scope="col">Schedules Interviews</th>
                          <th scope="col">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {jobstats.length > 0 ? (
                          jobstats.map((val: any, index: number) => (
                            <tr key={index}>
                              <th scope="row">#{val.job_id}</th>
                              <td>
                                <p className="tb-job-des">{val.job_title}</p>
                              </td>
                              <td>
                                {val.posted_on?.strftime("%B %d, %Y %I:%M %p")}
                              </td>
                              <td>{val.applicants}</td>
                              <td>
                                <span className="badge bg-success">
                                  {val.status == 1 ? "Active" : "Closed"}
                                </span>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <td colSpan={5} className="text-center m-0">
                            <div className="mt-1">
                              No Recent Jobs Found
                              {jobPermissions.includes("write") && (
                                <>
                                  <br />
                                  <Link
                                    href={`${APP_ROUTE.OPPORTUNITY_MANAGEMENT}/new`.subdomainLink(
                                      subdomain,
                                    )}>
                                    <Button className="btn btn-primary">
                                      + Add Job
                                    </Button>
                                  </Link>
                                </>
                              )}
                            </div>
                          </td>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
                {isSuperAdmin && <WildcardCandidateStats />}
              </div>
              {isSuperAdmin && (
                <>
                  <div className="col-lg-12 col-xl-4 row-gap-3">
                    <WildCardUpcomingInterviews />
                    <WildcardPaymentStats />
                    {/* <WildcardApiUsageStats /> */}
                  </div>
                </>
              )}
            </div>
          </>
        )}
      </div>
    </>
  );
};
