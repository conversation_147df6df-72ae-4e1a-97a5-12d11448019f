import React, { useEffect, useState } from "react";
import { Pie<PERSON><PERSON> } from "@mui/x-charts";
import { analyticApi } from "@src/apis/wildcardApis";
import { Skeleton } from "antd";
import { KeyPairInterface } from "@src/redux/interfaces";

type PieChartDataType = { id: number; value: number; label: string };

type JobStatsChartCardProps = {
  filters?: KeyPairInterface;
};

export function JobStatsChartCard({ filters = {} }: JobStatsChartCardProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<{
    data: Array<PieChartDataType>;
    title: string;
  }>({
    data: [{ id: 0, value: 1, label: "No Data" }],
    title: "Candidate Status Distribution Stats",
  });

  useEffect(() => {
    fetchCandidateAnalyticStats();
  }, [filters]);

  const fetchCandidateAnalyticStats = async () => {
    try {
      setLoading(true);
      const { success, data } = await analyticApi.statusDistibutionStats(filters);
      if (success) {
        setStats(data);
      }
    } catch (error) {
      console.error("Failed to fetch line chart stats:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="card">
        <div className="card-header border-bottom p-0">
          <h5 className="heading-clr p-3 mb-0">
            {state.title || "Candidate Status Distribution Stats"}
          </h5>
        </div>
        <div className="card-body">
          {loading ? (
            <Skeleton
              active
              avatar={{
                style: {
                  width: 300,
                  margin: 50,
                  height: 300,
                  aspectRatio: 1,
                  borderRadius: "50%",
                }, // Customize size and shape
              }}
              paragraph={false}
              title={false}
              style={{
                width: "100%",
                height: 300,
                maxWidth: 300,
                borderRadius: "50%",
              }}
            />
          ) : (
            <PieChart
              series={[{ data: state.data }]}
              colors={["#ef6565", "#7cbf50", "#50b6bf", "#b070e3"]}
              className="pie-chart"
              height={300}
              margin={{ top: 0, bottom: 120, left: 10, right: 10 }}
              slotProps={{
                legend: {
                  direction: "row",
                  position: { vertical: "bottom", horizontal: "middle" },
                  padding: 15,
                },
              }}
            />
          )}
        </div>
      </div>
    </>
  );
}
