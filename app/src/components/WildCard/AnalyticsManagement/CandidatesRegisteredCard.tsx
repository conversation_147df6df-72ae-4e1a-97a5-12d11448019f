import React, { useEffect } from "react";
import { <PERSON><PERSON> } from "react-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { fetchRegisteredCandidates } from "@src/redux/slices/analyticsManagement";
import { downloadFile } from "@src/helper/downloadFile";
import { DateFilterType } from "@src/redux/interfaces";
import Image from "next/image";

export function CandidatesRegisteredCard() {
  const dispatch = useDispatch();
  const {
    rows: candidates,
    count: totalCount,
    filter,
  } = useSelector(
    (state: RootState) => state.analyticsManagement.registeredCandidates,
  );

  const currentDateTime = new Date();

  useEffect(() => {
    console.log("useEffect Triggered with Current DateTime:", currentDateTime);
    console.log("Current Filter:", filter);
    const loadData = async () => {
      await callApi(currentDateTime, "week", 1, 10); // Default to week filter and first page,
    };
    loadData();
  }, []);

  const callApi = async (
    date: Date,
    filter: "week" | "month" | "year",
    page: number,
    limit: number,
  ) => {
    // dispatch(setLoader(true));
    await dispatch<any>(
      fetchRegisteredCandidates(date, filter, page, limit), // Default to week filter and first page,
    );
    // dispatch(setLoader(false));
  };

  const handleFilterChange = async (newFilter: DateFilterType) => {
    if (newFilter === filter) return;
    await callApi(currentDateTime, newFilter, 1, 10);
  };

  return (
    <div className="card Candidates_Registered m-0">
      <div className="card-header border-0 p-0">
        <h5 className="heading-clr p-3 mb-0">Candidates Registered</h5>
      </div>
      <div className="card-body pt-0">
        <div className="ds-schedule-wrap d-flex gap-3 mb-4">
          <ul className="nav nav-pills nav-fill">
            {["week", "month", "year"].map((period) => (
              <li className="nav-item" key={period}>
                <button
                  className={`nav-link ${filter === period ? "active" : ""}`}
                  onClick={() => handleFilterChange(period as DateFilterType)}>
                  {period === "week"
                    ? "This Week"
                    : period === "month"
                      ? "Current Month"
                      : "Current Year"}
                </button>
              </li>
            ))}
          </ul>
        </div>

        <span>
          Resume Reg : <strong>{totalCount}</strong>
        </span>

        {candidates.length > 0 ? (
          <ul className="listing">
            {candidates.map((candidate) => (
              <li key={candidate.id}>
                <Image
                  src={"/images/auth/undraw_profile.svg"}
                  className="img-profile rounded-circle mr-2"
                  width={48}
                  height={48}
                  alt=""
                />
                <div className="d-flex gap-1 flex-column align-items-center flex-grow-1 justify-content-center">
                  <h6 className="mb-0 w-100">{candidate.name}</h6>
                  <h5 className="m-0 w-100">{candidate.email}</h5>
                </div>
                <Button
                  type="button"
                  onClick={() => downloadFile(candidate.resume_url)}
                  disabled={
                    !candidate.resume_url || candidate.resume_url === ""
                  }>
                  Download Resume
                </Button>
              </li>
            ))}
          </ul>
        ) : (
          <div className="no-records text-center">No Records Found</div>
        )}
      </div>
    </div>
  );
}
