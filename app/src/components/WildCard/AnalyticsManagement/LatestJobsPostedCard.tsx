import React, { useCallback, useEffect, useState } from "react";
import Slider from "react-slick";
import { useAppDispatch } from "@src/redux/store";
import WorkIcon from "@mui/icons-material/Work";
import { OpportunityInterface } from "@src/redux/interfaces";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { opportunityApi } from "@src/apis/wildcardApis";

type LastestJobsPostedCardProps = {
  settings: {
    dots: boolean;
    infinite: boolean;
    speed: number;
    slidesToShow: number;
    slidesToScroll: number;
  };
  addNewButton?: React.ReactNode;
};

export function LastestJobsPostedCard({
  settings,
}: LastestJobsPostedCardProps) {
  const [loader, setLoader] = useState<boolean>(false);
  const [rows, setRows] = useState<OpportunityInterface[]>([]);
  const dispatch = useAppDispatch();

  // Fetch the Lastest jobs data with pagination
  const fetchData = useCallback(async () => {
    try {
      setLoader(true);
      const { success, ...response } =
        await opportunityApi.getOpportunitiesList({
          page: 1,
          limit: 10,
        });
      if (success) {
        const { rows } = response.data;
        setRows(rows);
      }
      setLoader(false);
    } catch (err) {
      console.error("Error fetching departments data:", err);
    } finally {
      setLoader(false);
    }
  }, [dispatch]);

  useEffect(() => {
    fetchData();
  }, []);

  const hasRows = rows && rows.length > 0;

  return (
    <>
      <div className="card m-0">
        <div className="card-header border-bottom p-3 d-flex align-items-center justify-content-between">
          <h5 className="heading-clr mb-0">Latest Jobs Posted</h5>
          <Link
            href={APP_ROUTE.OPPORTUNITY_MANAGEMENT}
            aria-disabled={!hasRows}
            className="btn btn-primary d-flex align-items-center justify-content-between px-3 group">
            View All Jobs
          </Link>
        </div>
        <div className="card-body">
          {loader && (
            <div className="text-center">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          )}
          {hasRows ? (
            <Slider {...settings}>
              {rows.map((opportunity: OpportunityInterface) => (
                <div key={opportunity.id}>
                  <div className="candidate-list-card bg-white border rounded-3 overflow-hidden">
                    <div className="p-3 pb-4">
                      <div className="d-flex justify-content-between align-items-start flex-wrap flex-lg-nowrap position-relative">
                        <div className="candidate-des d-flex gap-3 flex-wrap flex-lg-nowrap">
                          <div className="brief">
                            <h5 className="heading-clr mb-2">
                              {opportunity.designation ||
                                "Designation not available"}
                            </h5>
                            <p
                              className="text-clr m-0"
                              style={{ color: "#262626" }}>
                              {opportunity.description ||
                                "No description available"}
                            </p>
                            <ul className="p-0 mt-3">
                              <li>
                                Experience:{" "}
                                <b>{opportunity.experience || "N/A"} yrs</b>
                              </li>
                              <li>
                                Salary:{" "}
                                <b>
                                  {opportunity.salary
                                    ? `INR ${opportunity.salary}`
                                    : "N/A"}
                                </b>
                              </li>
                              <li className="job-part">
                                <WorkIcon
                                  style={{ fill: "var(--theme-primary-color)" }}
                                />
                                {opportunity.job_type || "Full-time"}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          ) : (
            <div className="no-records text-center">No Records Found</div>
          )}
        </div>
      </div>
    </>
  );
}
