import React, { useEffect, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { interviewApi } from "@src/apis/wildcardApis";
import { NewInterviewInterface } from "@src/redux/interfaces";
import { setLoader } from "@src/redux/actions";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { Image } from "antd";

export function TodayInterviewSchedule() {
  const [interviews, setInterviews] = useState<NewInterviewInterface[]>([]);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchTodayInterviews = async () => {
      dispatch(setLoader(true));
      try {
        const params = {
          page: 1,
          limit: 10,
          filter: "today",
        };
        const { success, data } = await interviewApi.getInterviewsList(params);

        if (success) {
          setInterviews(data.rows || []);
        } else {
          setInterviews([]);
        }
      } catch (error) {
        console.error("Error fetching interviews:", error);
        setInterviews([]);
      } finally {
        dispatch(setLoader(false));
      }
    };

    fetchTodayInterviews();
  }, [dispatch]);

  const hasInterviews = interviews.length > 0;

  return (
    <>
      <div className="card Interview_Schedule m-0">
        <div className="card-header border-bottom p-3 d-flex align-items-center justify-content-between">
          <h5 className="heading-clr mb-0">Today&apos;s Interview Schedule</h5>
          {hasInterviews && (
            <Link href={APP_ROUTE.INTERVIEW_MANAGEMENT} passHref>
              <Button className="btn btn-primary d-flex align-items-center justify-content-between px-3 group">
                <span>View All Schedule</span>
              </Button>
            </Link>
          )}
        </div>
        <div className="card-body">
          {hasInterviews ? (
            <ul className="listing m-0 pe-3">
              {interviews.map((interview: NewInterviewInterface) => (
                <li key={interview.candidate_id}>
                  <span>
                    <Image
                      src={"/images/auth/undraw_profile.svg"}
                      alt="Profile"
                      className="h-full w-full object-fit-cover"
                      width={70}
                      height={70}
                    />
                  </span>
                  <div className="d-flex gap-1 flex-column align-items-center flex-grow-1 justify-content-center">
                    <div className="d-flex gap-2 w-100">
                      <div className="box w-100">
                        <h6 className="">
                          {interview.candidate_name || "N/A"}
                        </h6>
                        <h5 className="m-0">
                          {interview.candidate_email || "N/A"}
                        </h5>
                      </div>
                      <div className="stauts ms-auto d-flex gap-2 align-items-center">
                        <span className="round">
                          Round:{" "}
                          <span>{interview.interview_round || "N/A"}</span>
                        </span>
                        <h6 className="m-0">
                          {interview.interview_mode_name || "N/A"}
                        </h6>
                      </div>
                    </div>
                    <div className="d-flex gap-2 w-100 bottom-status">
                      <h6>
                        Date & Time:{" "}
                        <span>
                          {interview.interview_at?.strftime(
                            "%B %d, %Y %I:%M %p",
                          ) ?? "N/A"}
                        </span>
                      </h6>
                      <h6>
                        Interviewer:{" "}
                        <span>{interview.interviewer_name || "N/A"}</span>
                      </h6>
                    </div>
                  </div>
                  {/* <Button type="button">Download Resume</Button> */}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-center text-clr">
              No interviews scheduled for today.
            </p>
          )}
        </div>
      </div>
    </>
  );
}
