import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  getAllDepartmentOptions,
  getAllOpportunityOptions,
} from "@src/redux/actions";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { ModalFormInput } from "@src/components/ModalInput/ModalFormInput";

type AnalyticsFilterProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void; // Function to call when the confirmation button is clicked
  close: Function; // Function to close the modal
};

// Analytics Filter Component
export const AnalyticsFilter = ({
  filters,
  close,
  onConfirm,
}: AnalyticsFilterProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [loading, setLoading] = useState<boolean>(false);
  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );

  const departmentOptions = useSelector(
    (state: RootState) => state.department.options,
  );

  useEffect(() => {
    fetchDepartmentOptions("");
    fetchOpportunitiesOptions("");
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const fetchOpportunitiesOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search === "" ? filters.opportunity_id : showId;
    setLoading(true);
    try {
      await dispatch(getAllOpportunityOptions({ search, showId }));
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartmentOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    setLoading(true);
    try {
      await dispatch(getAllDepartmentOptions({ search, showId }));
    } finally {
      setLoading(false);
    }
  };

  const FilterFields: GlobalInputFieldType[] = [
    {
      name: "department_id",
      label: "Department",
      placeholder: "Select Department",
      options: departmentOptions,
      onSearch: fetchDepartmentOptions,
      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,
      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "date_from",
      label: "Date From",
      placeholder: "Select Start Date",
      type: "date",
      dataType: "date",
      showSearch: false,
      selectEmpty: false,
    },
    {
      name: "date_to",
      label: "Date To",
      placeholder: "Select End Date",
      type: "date",
      dataType: "date",
      showSearch: false,
      selectEmpty: false,
    },
    {
      name: "status",
      label: "Status",
      placeholder: "Select Status",
      options: [
        { label: "All", value: "" },
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
        { label: "Pending", value: "pending" },
        { label: "Completed", value: "completed" },
      ],
      type: "select",
      dataType: "select",
      loading: false,
      selectEmpty: true,
    },
  ];

  const onSubmit = (state: KeyPairInterface) => {
    onConfirm(state);
    close();
  };

  return (
    <>
      <ModalFormInput
        state={state}
        setState={setState}
        fields={FilterFields}
        onSubmit={() => onSubmit(state)}
        onClose={() => onSubmit({})}
        buttonTitle="Apply Filters"
        cancelButtonTitle="Clear Filters"
        checkbox={true}
        checkboxKey="save_history"
        checkboxTitle="Save History"
      />
    </>
  );
};
