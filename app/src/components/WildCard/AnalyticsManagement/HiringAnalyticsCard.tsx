import React, { useState, useEffect } from "react";
import { analyticApi } from "@src/apis/wildcardApis";
import { Skeleton } from "antd";
import { LineChart } from "@mui/x-charts";
import { KeyPairInterface } from "@src/redux/interfaces";

type LineChartDataType = {
  data: number[];
  label: string[];
  title: string;
  xLabel?: string;
  yLabel?: string;
  has_data: boolean;
};

type StatsState = {
  hired_candidates_stats: LineChartDataType;
  stats_type: string;
};

const defaultStats: StatsState = {
  hired_candidates_stats: {
    data: Array(12).fill(0),
    label: Array(12).fill(""),
    title: "Hiring Analytics",
    xLabel: "Months",
    yLabel: "Values",
    has_data: false,
  },
  stats_type: "monthly",
};

type HiringAnalyticsCardProps = {
  filters?: KeyPairInterface;
};

export const HiringAnalyticsCard = ({ filters = {} }: HiringAnalyticsCardProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<StatsState>(defaultStats);

  useEffect(() => {
    fetchCandidateAnalyticStats("monthly");
  }, [filters]);

  const fetchCandidateAnalyticStats = async (type: string) => {
    try {
      setLoading(true);

      const { success, data } = await analyticApi.candidateAnalyticStats({
        filter: type,
        ...filters,
      });
      if (success) {
        setStats({
          hired_candidates_stats: data.hired_candidates_stats,
          stats_type: type,
        });
      }
    } catch (error) {
      console.error("Failed to fetch line chart stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const { hired_candidates_stats } = state;

  return (
    <div className="card m-0">
      <div className="card-header border-bottom p-0 d-flex align-items-center justify-content-between">
        <h5 className="heading-clr p-3 mb-0">
          {state.hired_candidates_stats?.title || "Hiring Analytics"}
        </h5>
      </div>
      <div className="card-body">
        {loading ? (
          <Skeleton active />
        ) : (
          <LineChart
            xAxis={[
              {
                scaleType: "point",
                data: hired_candidates_stats.label ?? [],
                label: hired_candidates_stats.xLabel,
              },
            ]}
            series={[
              {
                data: hired_candidates_stats.data ?? [],
                color: "#1976d2",
              },
            ]}
            height={300}
          />
        )}
      </div>
    </div>
  );
};
