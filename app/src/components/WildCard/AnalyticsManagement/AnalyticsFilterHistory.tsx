import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { <PERSON><PERSON>, Card, Button } from "react-bootstrap";
import CheckCircle from "@mui/icons-material/CheckCircle";

// Define interface for analytics search filters
interface AnalyticsSearchFilters {
  id?: number;
  department_id?: string | number;
  department?: string;
  opportunity_id?: string | number;
  opportunity?: string;
  date_from?: string;
  date_to?: string;
  status?: string;
  created_at?: string;
}

type AnalyticsFilterHistoryProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

export const AnalyticsFilterHistory = ({
  filters,
  close,
  onConfirm,
}: AnalyticsFilterHistoryProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [searchHistory, setSearchHistory] = useState<AnalyticsSearchFilters[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<KeyPairInterface | null>(null);

  const handleSubmit = () => {
    if (selectedFilter) {
      onConfirm(selectedFilter);
    }
    close();
  };

  const fetchFilters = async () => {
    try {
      // TODO: Replace with actual API call when analytics filter history API is available
      // const response = await analyticsApi.getAnalyticsSavedFilters({});
      // setSearchHistory(response.data);
      
      // Mock data for demonstration
      const mockHistory: AnalyticsSearchFilters[] = [];
      setSearchHistory(mockHistory);
    } catch (error) {
      console.error("Error fetching analytics filter history:", error);
      setSearchHistory([]);
    }
  };

  const applyFilter = (filter: AnalyticsSearchFilters) => {
    const filterData = {
      department_id: filter.department_id,
      opportunity_id: filter.opportunity_id,
      date_from: filter.date_from,
      date_to: filter.date_to,
      status: filter.status,
    };
    setSelectedFilter(filterData);
  };

  useEffect(() => {
    fetchFilters();
  }, []);

  return (
    <>
      {searchHistory && searchHistory.length > 0 ? (
        searchHistory.map((val: AnalyticsSearchFilters, index: number) => (
          <Card
            className="history-card shadow-none border-0 overflow-visible"
            key={index}>
            <Card.Body
              className={`box p-2 border rounded-2 w-100 ${selectedFilter === val ? "active" : null}`}>
              <div
                onClick={() => applyFilter(val)}
                className="w-full d-flex flex-wrap gap-2">
                {selectedFilter === val ? (
                  <CheckCircle
                    className="material-icons"
                    style={{ color: "#0a58ca" }}
                  />
                ) : (
                  <CheckCircle className="material-icons" />
                )}

                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Department:</span>
                  <Badge className="heading-clr">{val.department || "All"}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Job:</span>
                  <Badge className="heading-clr">{val.opportunity || "All"}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Date From:</span>
                  <Badge className="heading-clr">
                    {val.date_from || "None"}
                  </Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Date To:</span>
                  <Badge className="heading-clr">{val.date_to || "None"}</Badge>
                </div>
                <div className="d-flex options align-items-center gap-2">
                  <span className="text-light-clr">Status:</span>
                  <Badge className="heading-clr">{val.status || "All"}</Badge>
                </div>
              </div>
            </Card.Body>
          </Card>
        ))
      ) : (
        <h6>No History Available</h6>
      )}

      <div className="ant-modal-footer">
        <Button className="btn btn-theme mr-1" onClick={handleSubmit}>
          Apply Filters
        </Button>
        {close && (
          <Button
            key="cancel"
            className="btn cancel-btn"
            onClick={() => close()}>
            Cancel
          </Button>
        )}
      </div>
    </>
  );
};
