import React, { useEffect, useRef } from "react";

interface HtmlPreviewIframeProps {
  htmlContent: string;
  height?: string | number;
  width?: string | number;
}

const HtmlPreviewIframe: React.FC<HtmlPreviewIframeProps> = ({
  htmlContent,
  height = "300px",
  width = "100%",
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentDocument) return;

    const doc = iframe.contentDocument;
    doc.open();

    // Check if htmlContent contains <html> tag (case-insensitive)
    const isFullDocument = /<html[\s>]/i.test(htmlContent);

    console.log(isFullDocument, "isFullDocument");
    doc.write(htmlContent); // Use as-is

    doc.close();
  }, [htmlContent]);

  return (
    <iframe
      ref={iframeRef}
      title="HTML Preview"
      style={{ width, height, border: "1px solid #ccc" }}
      sandbox="allow-same-origin"
    />
  );
};

export default HtmlPreviewIframe;
