import React from "react";
import StarIcon from "@mui/icons-material/Star";
import { styled } from "@mui/material/styles";

// Styled component for star icon
const StyledStar = styled(StarIcon)(({ theme }) => ({
  color: theme.palette.warning.main,
}));

type StarRatingProps = {
  rating: number;
};

export const StarRating: React.FC<StarRatingProps> = ({ rating = 1 }) => {
  const clampedRating = Math.min(5, Math.max(1, rating));

  return (
    <div className="d-flex">
      {Array.from({ length: 5 }).map((_, index) => (
        <StyledStar
          key={index}
          style={{
            color: index < clampedRating ? "#fca239" : "lightgray",
            marginRight: 4,
          }}
        />
      ))}
    </div>
  );
};
