import React, { useEffect, useState, useMemo } from "react";

export const CurrentTime = () => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  useEffect(() => {
    const options_time: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };
    const updateTime = () => setCurrentTime(new Date());

    updateTime(); // Set initial time

    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  const formattedTime = useMemo(() => {
    const options_time: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };
    return currentTime.toLocaleTimeString("en-US", options_time);
  }, [currentTime]);

  return <p className="m-0">{formattedTime.replace(" at ", " | ")}</p>;
};
