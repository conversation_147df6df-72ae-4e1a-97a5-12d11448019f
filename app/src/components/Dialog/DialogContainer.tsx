import { useSelector } from "react-redux";
import { Dialog } from "./Dialog";
import { RootState } from "@src/redux/reducers";

// Define the DialogContainer component
const DialogContainer = () => {
  // Use useSelector to access the Redux store state
  const { open, ...dialog } = useSelector((state: RootState) => state.dialog);

  // Check if a dialog is open and its properties exist
  if (dialog && open) {
    // Render the Dialog component with dialog properties and open state
    return <Dialog {...dialog.properties} open={open} />;
  }

  // Return null if no dialog is open or its properties don't exist
  return null;
};

export default DialogContainer;
