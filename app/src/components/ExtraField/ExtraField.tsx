import React from "react";
import { useDispatch } from "react-redux";
import { GlobalInputFieldType } from "../input/GlobalInput";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "../DialogComponents";
import { Button } from "antd";

interface ExtraFieldButtonProps {
  fields: GlobalInputFieldType[];
  onFieldCreate: (field: GlobalInputFieldType, value: string) => void;
  deleteExtraField: (fieldId: string) => void;
  label?: string;
}

const ExtraFieldButton: React.FC<ExtraFieldButtonProps> = ({
  fields,
  onFieldCreate,
  deleteExtraField,
  label = "+ Add Extra Field",
}) => {
  const dispatch = useDispatch();

  const deleteFieldEvent = (fieldId: string) => {
    const fieldName = fieldId.split(".")[1];
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Delete Extra Field",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to delete{" "}
              <strong>{fieldName.titleize()}</strong> field?
            </div>
          ),
          onConfirm: () => {
            deleteExtraField(fieldId);
          },
        },
      }),
    );
  };

  const openExtraFieldModal = () => {
    dispatch(
      openDialog({
        config: DialogComponents.NEW_CUSTOM_FIELD_MODAL,
        options: {
          fields,
          onFieldCreate,
          deleteExtraField: deleteFieldEvent,
        },
      }),
    );
  };

  return (
    <Button onClick={openExtraFieldModal} className="btn btn-theme ms-auto">
      {label}
    </Button>
  );
};

export default ExtraFieldButton;
