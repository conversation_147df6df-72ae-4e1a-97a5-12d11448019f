import React, { useState, useEffect } from "react";
import Image from "next/image";
import flashMessage from "@src/components/FlashMessage";
import ContactEnqueryApi from "@src/apis/wildcardApis/homePageApi";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";

export const ContactUsFormSection = () => {
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [errors, setErrors] = useState({
    first_name: "",
    last_name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Name validation function
  const validateName = (name: string) => {
    const nameRegex = /^[A-Za-z][A-Za-z\s-]/; // Name should start with an alphabet
    return nameRegex.test(name);
  };

  // Email validation function
  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
    return emailRegex.test(email);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Field validation on blur
  const handleBlur = (
    e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    if (!value) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: `${name.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())} is required.`,
      }));
    } else if (
      (name === "first_name" || name === "last_name") &&
      !validateName(value)
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "Enter a valid name.",
      }));
    } else {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "",
      }));
    }
  };

  // Email validation on blur
  const handleEmailBlur = () => {
    if (!formData.email.trim()) {
      // Email field is empty
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: "Email is required.",
      }));
    } else if (!validateEmail(formData.email)) {
      // Email is invalid
      setErrors((prevErrors) => ({
        ...prevErrors,
        email: "Invalid email address.",
      }));
    } else {
      // Email is valid
      setErrors((prevErrors) => ({ ...prevErrors, email: "" }));
    }
  };

  const dispatch = useAppDispatch();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({
      first_name: "",
      last_name: "",
      email: "",
      subject: "",
      message: "",
    });

    // Validate all fields
    let valid = true;
    const newErrors: any = {};

    // Validate first_name
    if (!formData.first_name) {
      newErrors.first_name = "First Name is required.";
      valid = false;
    } else if (!validateName(formData.first_name)) {
      newErrors.first_name = "Enter a valid name.";
      valid = false;
    }

    // Validate last_name
    if (!formData.last_name) {
      newErrors.last_name = "Last Name is required.";
      valid = false;
    } else if (!validateName(formData.last_name)) {
      newErrors.last_name = "Enter a valid name.";
      valid = false;
    }
    if (!formData.email) {
      newErrors.email = "Email is required.";
      valid = false;
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email.";
      valid = false;
    }
    if (!formData.subject) {
      newErrors.subject = "Subject is required.";
      valid = false;
    }
    if (!formData.message) {
      newErrors.message = "Message is required.";
      valid = false;
    }

    if (!valid) {
      setErrors(newErrors);
      setIsSubmitting(false);
      return;
    }

    // If all fields are valid, proceed with API call
    await dispatch(setLoader(true));
    const { success, ...response } =
      await ContactEnqueryApi.ContactEnqueryApi(formData);
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");

    if (success) {
      setFormData({
        first_name: "",
        last_name: "",
        email: "",
        subject: "",
        message: "",
      });
    }
    setIsSubmitting(false);
  };

  useEffect(() => {
    setFormData({
      first_name: "",
      last_name: "",
      email: "",
      subject: "",
      message: "",
    });
  }, []);

  return (
    <>
      <section className="contact-wrap py-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-12 col-lg-6 col-sm-12">
              <div className="contact-form">
                <div className="common-heading mb-5">
                  <h5 className="primary">We will get back to you</h5>
                  <h4 className="semibold mb-4">
                    Contact Us For More <br />
                    Information
                  </h4>
                </div>
              </div>
              <ul className="contact-details">
                <li className="box">
                  <Image
                    src="/images/home/<USER>/location-icon.png"
                    alt="icon"
                    height={56}
                    width={56}
                  />
                  <div className="content">
                    <h5>Location</h5>
                    <p>
                      Dibon Building, Ground Floor, Plot No ITC-2, Sector 67
                      Mohali, Punjab (160062)
                    </p>
                  </div>
                </li>
                <li className="box">
                  <Image
                    src="/images/home/<USER>/email-icon.png"
                    alt="icon"
                    height={56}
                    width={56}
                  />
                  <div className="content">
                    <h5>Email us on:</h5>
                    <p><EMAIL></p>
                  </div>
                </li>
                <li className="box">
                  <Image
                    src="/images/home/<USER>/call-icon.png"
                    alt="icon"
                    height={56}
                    width={56}
                  />
                  <div className="content">
                    <h5>Contact us on:</h5>
                    <p>+91 8146111801, +91 9988699972</p>
                  </div>
                </li>
              </ul>
            </div>
            <div className="col-md-12 col-lg-6 col-sm-12">
              <div className="card">
                <h3>Contact Us for Assistance</h3>
                <p>
                  Need help or have questions? We&apos;re here for you! Our
                  customer-centric team is readily available to assist you.
                  Simply fill out the form below, and we&apos;ll get in touch
                  with you promptly.
                </p>
                <form onSubmit={handleSubmit}>
                  <div className="row mb-3">
                    <div className="col">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="First Name"
                        maxLength={25}
                        name="first_name"
                        value={formData.first_name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        aria-label="First name"
                        disabled={isSubmitting}
                      />
                      {errors.first_name && (
                        <div className="text-danger error-validation">
                          {errors.first_name}
                        </div>
                      )}
                    </div>
                    <div className="col">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Last Name"
                        maxLength={25}
                        name="last_name"
                        value={formData.last_name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        aria-label="Last name"
                        disabled={isSubmitting}
                      />
                      {errors.last_name && (
                        <div className="text-danger error-validation">
                          {errors.last_name}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="mb-3">
                    <input
                      type="email"
                      className="form-control"
                      placeholder="Email Address"
                      maxLength={250}
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      onBlur={handleEmailBlur}
                      aria-describedby="emailHelp"
                      disabled={isSubmitting}
                    />
                    {errors.email && (
                      <div className="text-danger error-validation">
                        {errors.email}
                      </div>
                    )}
                  </div>
                  <div className="mb-3">
                    <input
                      type="text"
                      placeholder="Subject"
                      className="form-control"
                      name="subject"
                      value={formData.subject}
                      maxLength={250}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      id="exampleInputSubject"
                      aria-describedby="emailSubject"
                      disabled={isSubmitting}
                    />
                    {errors.subject && (
                      <div className="text-danger error-validation">
                        {errors.subject}
                      </div>
                    )}
                  </div>
                  <div className="mb-3">
                    <textarea
                      placeholder="Message"
                      maxLength={500}
                      className="form-control"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      id="exampleInputMessage"
                      aria-describedby="emailMessage"
                      disabled={isSubmitting}
                    />
                    {errors.message && (
                      <div className="text-danger error-validation">
                        {errors.message}
                      </div>
                    )}
                  </div>
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isSubmitting}>
                    {isSubmitting ? "Sending..." : "Submit"}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
