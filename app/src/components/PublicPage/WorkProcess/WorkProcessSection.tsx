import Image from "next/image";
import React from "react";

export const WorkProcessSection = () => {
  return (
    <>
      <section className="working-process pt-80">
        <div className="text-center common-heading mb-5">
          <h5 className="primary">
            Recruitease Pro makes your hiring process more efficient and
            time-saving
          </h5>
          <h4 className="semibold">
            We streamline recruitment and help you find the right candidates
          </h4>
        </div>
        <div className="container mb-5">
          <div className="row align-items-center">
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6 ">
              <div className="process-vector-wrap-one text-center">
                <span className="d-flex justify-content-center align-items-center">
                  1
                </span>
                <Image
                  src="/images/home/<USER>/work-icon1.png"
                  width={443}
                  height={208}
                  alt="icon"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-content-wrap">
                <h4 className="mb-2">Job Posting</h4>
                <p className="mb-0">
                  Recruitease Pro helps you post and share job descriptions.
                  This saves time and ensures your job ads are consistent across
                  all platforms.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="process-bg mb-5">
          <div className="container">
            <div className="row align-items-center">
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-content-wrap">
                  <h4 className="mb-2">Email Outreach</h4>
                  <p className="mb-0">
                    Craft, send track, and optimize personalized emails to
                    potential candidates. This helps in maintaining a structured
                    and efficient communication process.
                  </p>
                </div>
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-vector-wrap-two text-center">
                  <span className="d-flex justify-content-center align-items-center">
                    2
                  </span>
                  <Image
                    src="/images/home/<USER>/work-icon2.png"
                    width={377}
                    height={240}
                    alt="icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container mb-5">
          <div className="row align-items-center">
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-vector-wrap-one text-center">
                <span className="d-flex justify-content-center align-items-center">
                  3
                </span>
                <Image
                  src="/images/home/<USER>/work-icon3.png"
                  width={401}
                  height={248}
                  alt="icon"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-content-wrap">
                <h4 className="mb-2">Candidate Database</h4>
                <p className="mb-0">
                  Keep a record of all candidate interactions, building a
                  comprehensive database. This allows you to nurture
                  relationships over time and streamline future recruitment
                  efforts.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="process-bg mb-5">
          <div className="container">
            <div className="row align-items-center">
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-content-wrap">
                  <h4 className="mb-2">Importing Resumes</h4>
                  <p className="mb-0">
                    Craft, send track, and optimize personalized emails to
                    potential candidates. This helps in maintaining a structured
                    and efficient communication process.
                  </p>
                </div>
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-vector-wrap-two text-center">
                  <span className="d-flex justify-content-center align-items-center">
                    4
                  </span>
                  <Image
                    src="/images/home/<USER>/work-icon4.png"
                    width={329}
                    height={200}
                    alt="icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container mb-5">
          <div className="row align-items-center">
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-vector-wrap-one text-center">
                <span className="d-flex justify-content-center align-items-center">
                  5
                </span>
                <Image
                  src="/images/home/<USER>/work-icon5.png"
                  width={273}
                  height={238}
                  alt="icon"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-content-wrap">
                <h4 className="mb-2">Parsing Resumes</h4>
                <p className="mb-0">
                  Quickly parse hundreds of resumes using pre-selected criteria.
                  This feature pulls out keywords and categorizes candidates
                  based on their match to the job description.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="process-bg mb-5">
          <div className="container">
            <div className="row align-items-center process-bg">
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-content-wrap">
                  <h4 className="mb-2">Interview Scheduling</h4>
                  <p className="mb-0">
                    Schedule and manage interviews directly within the
                    Recruitease Pro for better coordination and communication
                    with candidates.
                  </p>
                </div>
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-vector-wrap-two text-center">
                  <span className="d-flex justify-content-center align-items-center">
                    6
                  </span>
                  <Image
                    src="/images/home/<USER>/work-icon6.png"
                    width={377}
                    height={255}
                    alt="icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container mb-5">
          <div className="row align-items-center">
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-vector-wrap-one text-center">
                <span className="d-flex justify-content-center align-items-center">
                  7
                </span>
                <Image
                  src="/images/home/<USER>/work-icon7.png"
                  width={379}
                  height={225}
                  alt="icon"
                />
              </div>
            </div>
            <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
              <div className="process-content-wrap">
                <h4 className="mb-2">Tracking Candidates</h4>
                <p className="mb-0">
                  Monitor each candidate’s progress, noting who’s been contacted
                  and their current stage. This ensures a smooth hiring process
                  and a positive candidate experience.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="process-bg">
          <div className="container">
            <div className="row align-items-center process-bg">
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-content-wrap">
                  <h4 className="mb-2">Data Analysis</h4>
                  <p className="mb-0">
                    Track and report key recruitment metrics to meet your HR
                    targets. Analyze which sources are most effective and
                    identify stages where candidates tend to drop out.
                  </p>
                </div>
              </div>
              <div className="col-sm-12 col-md-6 col-lg-6 col-xl-6">
                <div className="process-vector-wrap-two text-center">
                  <span className="d-flex justify-content-center align-items-center">
                    8
                  </span>
                  <Image
                    src="/images/home/<USER>/work-icon8.png"
                    width={360}
                    height={229}
                    alt="icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
