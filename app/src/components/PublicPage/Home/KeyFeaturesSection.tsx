import React from "react";
import Image from "next/image";

export const KeyFeaturesSection = () => {
  return (
    <>
      <section className="key-feature-one primarybg py-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-12 col-lg-6 col-xl-6 col-sm-12">
              <div className="key-feature-one-img">
                <Image
                  src="/images/home/<USER>/resume-extract-img.webp"
                  alt="image"
                  width={507}
                  height={437}
                  className="img-responsive"
                />
              </div>
            </div>
            <div className="col-md-12 col-lg-6 col-xl-6 col-sm-12">
              <div className="key-feature-one-content common-heading text-white m-0">
                <h5>AI Resume Extractor</h5>
                <h4 className="semibold mb-4">
                  Use the Power of AI for Unmatched Efficiency
                </h4>
                <p className="text-white">
                  Our AI-driven Resume Extractor delves deep into each resume,
                  parsing and extracting key information such as skills,
                  experience levels, and educational background. This allows for
                  the automation of skill matching and ranking of candidates
                  based on job requirements.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="key-feature-two py-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-12 col-lg-6 col-xl-6 col-sm-12">
              <div className="key-feature-two-content common-heading m-0">
                <h5 className="primary">CRM and Dashboard</h5>
                <h4 className="semibold mb-4">
                  Stay Organized And Stay in Control
                </h4>
                <p>
                  Manage all your recruitment activities from a single
                  dashboard. Our robust CRM system tracks all interactions and
                  simplifies workflow, ensuring no detail is missed. Get
                  real-time updates and analytics to make informed decisions and
                  improve your recruitment strategy
                </p>
              </div>
            </div>
            <div className="col-md-12 col-lg-6 col-xl-6 col-sm-12">
              <div className="key-feature-two-img">
                <iframe
                  src="https://www.youtube.com/embed/hdpHNU2zKoE?si=DKUlM3LgzrKBB2aY"
                  title="Demo Video"
                  width={614}
                  height={433}
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  referrerPolicy="strict-origin-when-cross-origin"></iframe>
                {/* <Image
                  src="/images/home/<USER>/dashboard-feature-img.webp"
                  alt="image"
                  width={614}
                  height={433}
                  className="img-responsive"
                /> */}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
