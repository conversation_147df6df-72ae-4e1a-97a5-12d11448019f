import React from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";

export const IntroSection = () => {
  return (
    <>
      <section className="intro-sec lightbg py-80">
        <div className="container">
          <div className="common-heading">
            <h4 className="medium">
              Revolutionizing Recruitment with Advanced Recruitease Pro
            </h4>
            <p>
              Our Applicant Tracking System (Recruitease Pro) redefines
              recruitment by streamlining the process of job posting, applicant
              tracking, resume screening, and interviewing—all within one
              powerful platform. Designed for HR professionals and recruiters,
              our system ensures you connect with the right candidates
              efficiently and effectively.
            </p>
          </div>
          <div className="row align-items-center">
            <div className="col-md-12 col-lg-6 col-xl-6 col-sm-12">
              <div className="intro-sec-img">
                <Image
                  src="/images/home/<USER>/intro-img.webp"
                  alt="image"
                  width={553}
                  height={471}
                  className="img-responsive"
                />
              </div>
            </div>
            <div className="col-md-12 col-lg-5 col-xl-5 col-sm-12 ms-auto">
              <div className="intro-sec-content">
                <div className="intro-pointers">
                  <div className="pointer-wrapper d-flex gap-4 mb-4 align-items-center">
                    <div className="pointer-img d-flex justify-content-center align-items-center">
                      <Image
                        src="/images/home/<USER>/intro-recruit-icon.png"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>
                    <div className="pointer-content">
                      <h4 className="medium">Streamlined Recruitment</h4>
                      <p className="mb-0">
                        Makes hiring faster and easier for smoother processes.
                      </p>
                    </div>
                  </div>
                  <div className="pointer-wrapper d-flex gap-4 mb-4 align-items-center">
                    <div className="pointer-img d-flex justify-content-center align-items-center">
                      <Image
                        src="/images/home/<USER>/intro-resume-icon.png"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>
                    <div className="pointer-content">
                      <h4 className="medium">Streamlined Recruitment</h4>
                      <p className="mb-0">
                        Makes hiring faster and easier for smoother processes.
                      </p>
                    </div>
                  </div>
                  <div className="pointer-wrapper d-flex gap-4 mb-4 align-items-center">
                    <div className="pointer-img d-flex justify-content-center align-items-center">
                      <Image
                        src="/images/home/<USER>/intro-mgt-icon.png"
                        alt="icon"
                        width={32}
                        height={32}
                      />
                    </div>
                    <div className="pointer-content">
                      <h4 className="medium">Streamlined Recruitment</h4>
                      <p className="mb-0">
                        Makes hiring faster and easier for smoother processes.
                      </p>
                    </div>
                  </div>
                </div>
                <Link
                  href={APP_ROUTE.ABOUT_US}
                  className="btn btn-primary d-inline-flex align-items-center">
                  Know More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
