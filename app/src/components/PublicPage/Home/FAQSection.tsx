import React from "react";
import Link from "next/link";
import Accordion from "react-bootstrap/Accordion";
import { APP_ROUTE } from "@src/constants";

interface FAQItem {
  title: string;
  value: string;
}

const FAQList: Array<FAQItem> = [
  {
    title: "What benefits can Recruitease Pro bring to my company?",
    value:
      "Recruitease Pro can help you manage open positions, screen candidates, and track applications effectively by streamlining your recruiting process.",
  },
  {
    title: "Is Recruitease Pro suitable for businesses of all sizes?",
    value:
      "Yes, Recruitease Pro benefits businesses of all sizes, from small startups to large enterprises, by helping to improve and manage the recruitment process well.",
  },
  {
    title: "Can I integrate Recruitease Pro into my existing software?",
    value:
      "Integration of an Recruitease Pro into an existing HR system will vary depending on the software and complexity of the existing process. However, many Recruitease Pro providers offer support and services to assist with integration.",
  },
  {
    title: "Can Recruitease Pro help diversification?",
    value:
      "Yes, Recruitease Pro can assist with diversity and inclusion efforts by providing tools to track and identify candidates to ensure fairness and equity in hiring.",
  },
];

export const FAQSection: React.FC = () => {
  return (
    <>
      <section className="faq-section py-80">
        <div className="container">
          <div className="text-center common-heading mb-5">
            <h4 className="semibold">Frequently Asked Questions</h4>
            <p>
              Explore our FAQ section to learn more about how our Recruitease
              Pro simplifies HR processes to enhances your recruitment
              experience.
            </p>
          </div>
          {/*--accordian-*/}

          <Accordion
            defaultActiveKey="0"
            className="accordion faq-accordian mb-5"
            id="accordionExample">
            {FAQList.map((faq: FAQItem, index: number) => (
              <Accordion.Item
                eventKey={index.toString()}
                className="accordion-item border-0 mb-3"
                key={index}>
                <Accordion.Header>{faq.title}</Accordion.Header>
                <Accordion.Body>{faq.value}</Accordion.Body>
              </Accordion.Item>
            ))}
          </Accordion>

          <div className="text-center">
            <Link
              href={APP_ROUTE.FAQS}
              className="btn btn-primary d-inline-flex align-items-center">
              View All FAQ&apos;s
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};
