import React from "react";
import Image from "next/image";

export const FeatureSection = () => {
  return (
    <>
      <section className="features py-80">
        <div className="container">
          <div className="text-center common-heading">
            <h4 className="semibold">Key Features</h4>
            <p>
              Explore our FAQ section to learn more about how our Recruitease
              Pro simplifies HR processes to enhances your recruitment
              experience.
            </p>
          </div>
          <div className="row justify-content-center">
            <div className="col-md-12 col-sm-12 col-lg-4 col-xl-4">
              <div className="feature-card text-center lightbg rounded-5">
                <div className="feature-img d-flex justify-content-center align-items-center mb-4 mx-auto">
                  <Image
                    src="/images/home/<USER>/feature-img1.png"
                    alt="icon"
                    width={48}
                    height={48}
                    className="img-responsive"
                  />
                </div>
                <div className="feature-content">
                  <h4>Seamless User Registration</h4>
                  <p>&nbsp;Easy signup process to kickstart your recruitment</p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-sm-12 col-lg-4 col-xl-4">
              <div className="feature-card text-center lightbg rounded-5">
                <div className="feature-img d-flex justify-content-center align-items-center mb-4 mx-auto">
                  <Image
                    src="/images/home/<USER>/feature-img2.png"
                    alt="icon"
                    width={48}
                    height={48}
                    className="img-responsive"
                  />
                </div>
                <div className="feature-content">
                  <h4>Effortless Resume Upload &amp; Storage</h4>
                  <p>
                    &nbsp;Simply upload resumes, our system will handle the
                    storage and organization
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-sm-12 col-lg-4 col-xl-4">
              <div className="feature-card text-center lightbg rounded-5">
                <div className="feature-img d-flex justify-content-center align-items-center mb-4 mx-auto">
                  <Image
                    src="/images/home/<USER>/feature-img3.png"
                    alt="icon"
                    width={48}
                    height={48}
                    className="img-responsive"
                  />
                </div>
                <div className="feature-content">
                  <h4>Intelligent Keyword Search</h4>
                  <p>
                    &nbsp;Use specific keywords to sort through resumes and find
                    ideal candidates swiftly
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-sm-12 col-lg-4 col-xl-4">
              <div className="feature-card text-center lightbg rounded-5">
                <div className="feature-img d-flex justify-content-center align-items-center mb-4 mx-auto">
                  <Image
                    src="/images/home/<USER>/feature-img4.png"
                    alt="icon"
                    width={48}
                    height={48}
                    className="img-responsive"
                  />
                </div>
                <div className="feature-content">
                  <h4>Automated Requirement Setting</h4>
                  <p>
                    &nbsp;Specify the skills, location, tech stack and
                    categories to tailor your search perfectly
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-12 col-sm-12 col-lg-4 col-xl-4">
              <div className="feature-card text-center lightbg rounded-5">
                <div className="feature-img d-flex justify-content-center align-items-center mb-4 mx-auto">
                  <Image
                    src="/images/home/<USER>/feature-img5.png"
                    alt="icon"
                    width={48}
                    height={48}
                    className="img-responsive"
                  />
                </div>
                <div className="feature-content">
                  <h4>Integrated Interview Scheduling</h4>
                  <p>
                    &nbsp;Once you find your matched, schedule interviews
                    directly through the platform
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
