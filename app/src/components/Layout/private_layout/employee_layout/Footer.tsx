import { useAppDispatch } from "@src/redux/store";
import { logoutEmployee, setLoader } from "@src/redux/actions";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import { AuthEmployeeInterface } from "@src/redux/interfaces";

type EmployeeFooterProps = {
  employee: AuthEmployeeInterface;
};

export const EmployeeFooter: React.FC<EmployeeFooterProps> = ({ employee }) => {
  const currentYear = new Date().getFullYear();
  const dispatch = useAppDispatch();

  const logoutUser = async () => {
    await dispatch(setLoader(true));
    await dispatch(logoutEmployee());
    await dispatch(setLoader(false));
  };

  return (
    <>
      <footer className="footer">
        <div className="container-fluid">
          <div className="row text-muted">
            <div className="col-12 text-center">
              <p className="mb-0">
                Copyright © {currentYear}{" "}
                <Link
                  href={APP_ROUTE.HOME.subdomainLink("www")}
                  className="primary-clr">
                  Recruitease Pro
                </Link>
                &nbsp; Inc. All rights reserved. | Powered by{" "}
                <a
                  href="https://www.talentelgia.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="primary-clr">
                  Talentelgia Technologies Pvt Limited
                </a>
              </p>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};
