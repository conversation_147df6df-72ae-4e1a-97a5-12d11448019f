import React, { useEffect, useState } from "react";
import { RootState } from "@src/redux/reducers";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";
import { useAppDispatch } from "@src/redux/store";
import { APP_ROUTE } from "@src/constants";
import { EmployeeSidebar } from "./Sidebar";
import { EmployeeHeader } from "./Header";
import {
  AuthEmployeeInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { employeeVerify, logoutEmployee } from "@src/redux/actions";
import { EmployeeFooter } from "./Footer";
import flashMessage from "@src/components/FlashMessage";
import { VerifyEmployeePermissions } from "@src/helper/pagePermissions";

type EmployeeLayoutProps = {
  subdomain: string;
  children: React.ReactNode;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  ghostMode?: boolean;
};

export const EmployeeLayout: React.FC<EmployeeLayoutProps> = ({
  children,
  allowedRoles = [],
  pagePermissions = {},
  requiredPermission = [],
  ghostMode = false,
}) => {
  const [loader, setLoader] = useState<boolean>(true);
  const employee = useSelector((state: RootState) => state.auth.employee);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const verifyUserAuth = async () => {
    const { success, message: msg } = await dispatch(employeeVerify());
    if (!success) {
      flashMessage(msg, "error");
      await dispatch(logoutEmployee());
      await router.replace(APP_ROUTE.LOGIN);
    } else if (ghostMode) {
      router.push({ pathname: APP_ROUTE.DASHBOARD }, undefined, {
        shallow: true,
      });
    }
    await setLoader(false);
  };

  useEffect(() => {
    if (employee || ghostMode) {
      verifyUserAuth();
    } else {
      router.replace(APP_ROUTE.LOGIN);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [employee?.id]);

  useEffect(() => {
    if (employee) {
      const hasPermission = VerifyEmployeePermissions({
        employee,
        allowedRoles,
        pagePermissions,
        requiredPermission,
      });
      if (!hasPermission) {
        router.replace(APP_ROUTE.DASHBOARD);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [employee?.id, allowedRoles, pagePermissions, router]);

  if (loader) {
    return (
      <div>
        <div id="wrapper" />
      </div>
    );
  }

  return (
    <>
      <section className="private-layout">
        <div id="wrapper">
          <EmployeeSidebar employee={employee as AuthEmployeeInterface} />
          <div className="main">
            <EmployeeHeader employee={employee as AuthEmployeeInterface} />
            <div id="content" className="content">
              <div className="container-fluid p-0">{children}</div>
            </div>
            <EmployeeFooter employee={employee as AuthEmployeeInterface} />
          </div>
        </div>
      </section>
    </>
  );
};
