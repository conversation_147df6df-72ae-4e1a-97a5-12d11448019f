import { useAppDispatch } from "@src/redux/store";
import Dropdown from "react-bootstrap/Dropdown";
import { logoutEmployee, setLoader, toggleSideBar } from "@src/redux/actions";
import Image from "next/image";
import { AuthEmployeeInterface } from "@src/redux/interfaces";
import { Nav } from "react-bootstrap";
import Menu from "@mui/icons-material/Menu";
import { DropdownLink } from "@src/helper/actions";
import { useCallback, useEffect, useState } from "react";
import { wildcardBusinessesApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { encryptString } from "@src/helper/encryption";
import { APP_ROUTE } from "@src/constants";

type EmployeeHeaderProps = {
  employee: AuthEmployeeInterface;
};

type BusinessType = {
  id: number;
  name: string;
  subdomain: string;
};

export const EmployeeHeader: React.FC<EmployeeHeaderProps> = ({ employee }) => {
  const [businesses, setBusinesses] = useState<BusinessType[]>([]);
  const dispatch = useAppDispatch();
  const clickMenuOpen = () => {
    dispatch(toggleSideBar());
  };

  const fetchAndSetBusinesses = useCallback(
    async () => {
      dispatch(setLoader(true));
      const { success, ...response } =
        await wildcardBusinessesApi.getBusinessesList();
      if (success) {
        setBusinesses(response.data);
      } else {
        flashMessage(response.message, "error");
        // close()
      }
      dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch],
  );

  useEffect(() => {
    if (employee && employee.employee_role == "Super Admin") {
      fetchAndSetBusinesses();
    }
  }, [fetchAndSetBusinesses, employee]);

  const logoutUser = async () => {
    await dispatch(setLoader(true));
    await dispatch(logoutEmployee());
    flashMessage("Logged out successfully.", "success");
    await dispatch(setLoader(false));
  };

  const openBusiness = async (business: BusinessType) => {
    if (business.id == employee.business_id) {
      return null;
    }

    await dispatch(setLoader(true));
    const { success } = await wildcardBusinessesApi.wildcardLogin(business.id);
    await dispatch(setLoader(false));
    if (success) {
      const params = new URLSearchParams({
        login_token: encryptString(`${new Date().toTimeString()}-Super Admin`),
      });
      let url = `${APP_ROUTE.DASHBOARD.subdomainLink(business.subdomain)}?${params}`;
      window.open(url, "_blank");
    } else {
      flashMessage("Failed to open portal", "error");
    }
  };

  let businessName = employee?.business_name ?? "";
  let superAdmin = employee?.employee_role == "Super Admin";

  return (
    <>
      <Nav className="navbar navbar-expand navbar-light navbar-bg topbar static-top shadow">
        <a
          id="sidebarToggleTop"
          onClick={() => clickMenuOpen()}
          className="sidebar-toggle js-sidebar-toggle d-flex align-items-center justify-content-center rounded-5">
          <Menu className="material-icons">menu</Menu>
        </a>

        <div className="navbar-collapse collapse justify-content-between">
          {superAdmin && businesses.length > 1 && (
            <Dropdown>
              <Dropdown.Toggle id="dropdown-basic" className="p-0">
                Businesses
              </Dropdown.Toggle>
              <Dropdown.Menu>
                {businesses.map((business: BusinessType, index: number) => (
                  <Dropdown.Item
                    key={index}
                    role="button"
                    onClick={() => openBusiness(business)}
                    disabled={employee?.business_id == business?.id}>
                    {business.name}
                  </Dropdown.Item>
                ))}
              </Dropdown.Menu>
            </Dropdown>
          )}

          <h5 className="text-white m-0">{businessName}</h5>

          <ul className="navbar-nav align-items-center">
            {/* 
            <li className="nav-item dropdown">
              <a
                className="nav-icon dropdown-toggle notification-btn d-flex align-items-center justify-content-center rounded-5 me-3"
                href="#"
                id="alertsDropdown"
                data-bs-toggle="dropdown">
                <div className="position-relative">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#ffffff"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="feather feather-bell align-middle">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                  </svg>
                </div>
              </a>
              <div
                className="dropdown-menu dropdown-menu-lg dropdown-menu-end py-0"
                aria-labelledby="alertsDropdown">
                <div className="dropdown-menu-header">4 New Notifications</div>
                <div className="list-group">
                  <a href="#" className="list-group-item">
                    <div className="row g-0 align-items-center">
                      <div className="col-2">
                        <i
                          className="text-danger"
                          data-feather="alert-circle"></i>
                      </div>
                      <div className="col-10">
                        <div className="text-dark">Update completed</div>
                        <div className="text-muted small mt-1">
                          Restart server 12 to complete the update.
                        </div>
                        <div className="text-muted small mt-1">30m ago</div>
                      </div>
                    </div>
                  </a>
                  <a href="#" className="list-group-item">
                    <div className="row g-0 align-items-center">
                      <div className="col-2">
                        <i className="text-warning" data-feather="bell"></i>
                      </div>
                      <div className="col-10">
                        <div className="text-dark">Lorem ipsum</div>
                        <div className="text-muted small mt-1">
                          Aliquam ex eros, imperdiet vulputate hendrerit et.
                        </div>
                        <div className="text-muted small mt-1">2h ago</div>
                      </div>
                    </div>
                  </a>
                  <a href="#" className="list-group-item">
                    <div className="row g-0 align-items-center">
                      <div className="col-2">
                        <i className="text-primary" data-feather="home"></i>
                      </div>
                      <div className="col-10">
                        <div className="text-dark">Login from ***********</div>
                        <div className="text-muted small mt-1">5h ago</div>
                      </div>
                    </div>
                  </a>
                  <a href="#" className="list-group-item">
                    <div className="row g-0 align-items-center">
                      <div className="col-2">
                        <i
                          className="text-success"
                          data-feather="user-plus"></i>
                      </div>
                      <div className="col-10">
                        <div className="text-dark">New connection</div>
                        <div className="text-muted small mt-1">
                          Christina accepted your request.
                        </div>
                        <div className="text-muted small mt-1">14h ago</div>
                      </div>
                    </div>
                  </a>
                </div>
                <div className="dropdown-menu-footer">
                  <a href="#" className="text-muted">
                    Show all notifications
                  </a>
                </div>
              </div>
            </li>
            */}
            <li className="nav-item business-profile dropdown no-arrow">
              <Dropdown>
                <Dropdown.Toggle
                  id="userDropdown"
                  className="text-decoration-none"
                  as="span"
                  type="button"
                  role="button"
                  aria-haspopup="true"
                  aria-expanded="false">
                  <Image
                    src={"/images/auth/undraw_profile.svg"}
                    className="img-profile rounded-circle mr-2"
                    width={24}
                    height={24}
                    alt=""
                  />
                  <span className="ellipsis-text">
                    {`${employee?.first_name ?? "User"} ${employee?.last_name ?? ""}`}
                  </span>
                </Dropdown.Toggle>

                <Dropdown.Menu
                  className="dropdown-menu-right shadow animated--grow-in"
                  aria-labelledby="userDropdown">
                  <DropdownLink linkHref="/admin/update_password">
                    <i className="fas fa-key fa-sm fa-fw mr-2 text-gray-400"></i>{" "}
                    Change Password
                  </DropdownLink>
                  <Dropdown.Divider />
                  <Dropdown.Item onClick={logoutUser}>
                    <i className="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                    Logout
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </li>
          </ul>
        </div>
      </Nav>
    </>
  );
};
