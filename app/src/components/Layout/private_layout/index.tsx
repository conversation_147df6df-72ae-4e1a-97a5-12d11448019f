import React from "react";
import Head from "next/head";
import { RootState } from "@src/redux/reducers";
import { useSelector } from "react-redux";
import Loader from "@src/components/Loader/Loader";
import DialogContainer from "@src/components/Dialog/DialogContainer";
import { EmployeeLayout } from "./employee_layout";
import { AdminLayout } from "./admin_layout";
import { PagePermissionInterface } from "@src/redux/interfaces";

type PrivateLayoutProps = {
  superAdminRoute?: boolean;
  children: React.ReactNode;
  subdomain: string;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
  ghostMode?: boolean;
};

export const PrivateLayout: React.FC<PrivateLayoutProps> = (props) => {
  const { loader: loading, loaderText } = useSelector(
    (state: RootState) => state.loader,
  );
  const wildcardAdmin = props.subdomain != "admin";

  return (
    <>
      <Head>
        <title>Recruitease Pro</title>
        <meta name="description" content="Applicant tracking system" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DialogContainer />
      {loading && <Loader loaderText={loaderText} />}
      {wildcardAdmin ? (
        <EmployeeLayout {...props} />
      ) : (
        <AdminLayout {...props} />
      )}
    </>
  );
};
