import React, { useEffect, useState } from "react";
import { RootState } from "@src/redux/reducers";
import { useSelector } from "react-redux";
import { useRouter } from "next/router";
import { useAppDispatch } from "@src/redux/store";
import { APP_ROUTE } from "@src/constants";
import { AdminSidebar } from "./Sidebar";
import { AdminHeader } from "./Header";
import {
  AuthAdminInterface,
  PagePermissionInterface,
} from "@src/redux/interfaces";
import { adminVerify, logoutAdmin } from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { AdminFooter } from "./Footer";

type AdminLayoutProps = {
  superAdminRoute?: boolean;
  children: React.ReactNode;
  allowedRoles?: string[];
  pagePermissions?: PagePermissionInterface;
  requiredPermission?: string[];
};

export const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [loader, setLoader] = useState<boolean>(true);
  const admin = useSelector((state: RootState) => state.auth.admin);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const verifyUserAuth = async () => {
    const { success, message } = await dispatch(adminVerify());
    if (!success) {
      flashMessage(message, "error");
      dispatch(logoutAdmin());
      router.replace(APP_ROUTE.LOGIN);
    }
    setLoader(false);
  };

  useEffect(() => {
    if (admin) {
      verifyUserAuth();
    } else {
      router.replace(APP_ROUTE.LOGIN);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [admin?.id]);

  if (loader) {
    return (
      <div>
        <div id="wrapper" />
      </div>
    );
  }

  return (
    <>
      <section className="private-layout">
        <div id="wrapper">
          <AdminSidebar admin={admin as AuthAdminInterface} />
          <div className="main">
            <AdminHeader admin={admin as AuthAdminInterface} />
            <div id="content" className="content">
              <div className="container-fluid p-0">{children}</div>
            </div>
            <AdminFooter admin={admin as AuthAdminInterface} />
          </div>
        </div>
      </section>
    </>
  );
};
