import { useAppDispatch } from "@src/redux/store";
import Dropdown from "react-bootstrap/Dropdown";
import { logoutCandidate, setLoader, toggleSideBar } from "@src/redux/actions";
import Image from "next/image";
import { AuthCandidateInterface } from "@src/redux/interfaces";
import { Nav } from "react-bootstrap";
import Menu from "@mui/icons-material/Menu";
import { DropdownLink } from "@src/helper/actions";
import { APP_ROUTE } from "@src/constants";
import flashMessage from "@src/components/FlashMessage";

type HeaderProps = {
  candidate: AuthCandidateInterface;
};

export const Header: React.FC<HeaderProps> = ({ candidate }) => {
  const dispatch = useAppDispatch();
  const clickMenuOpen = () => {
    dispatch(toggleSideBar());
  };

  const logoutUser = async () => {
    await dispatch(setLoader(true));
    await dispatch(logoutCandidate());
    flashMessage("Logged out successfully.", "success");
    await dispatch(setLoader(false));
  };

  return (
    <>
      <Nav className="navbar navbar-expand navbar-light navbar-bg topbar static-top shadow">
        <a
          id="sidebarToggleTop"
          onClick={() => clickMenuOpen()}
          className="sidebar-toggle js-sidebar-toggle d-flex align-items-center justify-content-center rounded-5">
          <Menu className="material-icons">menu</Menu>
        </a>

        <div className="navbar-collapse collapse">
          <ul className="navbar-nav navbar-align align-items-center">
            <li className="nav-item business-profile dropdown no-arrow">
              <Dropdown>
                <Dropdown.Toggle
                  id="userDropdown"
                  className="text-decoration-none"
                  as="span"
                  type="button"
                  role="button"
                  aria-haspopup="true"
                  aria-expanded="false">
                  <Image
                    src={"/images/auth/undraw_profile.svg"}
                    className="img-profile rounded-circle mr-2"
                    width={24}
                    height={24}
                    alt=""
                  />
                  <span className="ellipsis-text">
                    {`${candidate.name ?? ""}`}
                  </span>
                </Dropdown.Toggle>

                <Dropdown.Menu
                  className="dropdown-menu-right shadow animated--grow-in"
                  aria-labelledby="userDropdown">
                  <DropdownLink linkHref={APP_ROUTE.CANDIDATE_SELF_SETTING}>
                    <i className="fas fa-cog fa-sm fa-fw mr-2 text-gray-400"></i>{" "}
                    Settings
                  </DropdownLink>
                  <Dropdown.Divider />
                  <Dropdown.Item onClick={logoutUser}>
                    <i className="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                    Logout
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </li>
          </ul>
        </div>
      </Nav>
    </>
  );
};
