import { APP_ROUTE } from "@src/constants";
import useWindowWidth from "@src/helper/resize";
import { setSideBar } from "@src/redux/actions";

import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Nav } from "react-bootstrap";

export const Sidebar: React.FC = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const { sidebarOpen } = useSelector((state: RootState) => state.menu);
  const nodes = useSelector((state: RootState) => state.auth.nodes);
  const width = useWindowWidth();
  const [prevOpen, setPrevOpen] = useState<boolean>(false);
  const candidate = useSelector((state: RootState) => state.auth.candidate);

  useEffect(() => {
    if (width > 768 && !prevOpen) {
      dispatch(setSideBar(true));
    }
  }, [width, prevOpen, dispatch]);

  const router = useRouter();

  return (
    <>
      <Nav
        id="sidebar"
        className={`sidebar-content js-simplebar sidebar js-sidebar ${sidebarOpen ? "" : "collapsed"}`}>
        <div className="sidebar-content js-simplebar">
          <Link
            className="sidebar-brand"
            href={APP_ROUTE.HOME.subdomainLink("www")}>
            <Image src={"/images/logo.svg"} height={38} width={220} alt="" />
          </Link>

          <ul className="sidebar-nav">
            <div className="sidenav-profile">
              <span>
                <Image
                  src={"/images/auth/undraw_profile.svg"}
                  className="img-profile rounded-circle"
                  width={40}
                  height={40}
                  alt=""
                />
              </span>
              <h4 className="candidate-name ">
                {candidate?.name ?? "Candidate"}
              </h4>
              <p className="text-clr p-0 mb-0 text-truncate">
                {candidate?.email ?? ""}
              </p>
            </div>

            <div className="divide-sidenav mb-3">Navigate</div>
            <li
              className={`sidebar-item ${router.pathname.startsWith(APP_ROUTE.CANDIDATE_DASHBOARD) ? "active" : ""}`}>
              <Link
                className="sidebar-link align-middle gap-2"
                href={APP_ROUTE.CANDIDATE_DASHBOARD}>
                {/* <DynamicIcon name={"dashboard"} /> */}
                <Image
                  src={"/images/icons/schedule-interview-icon.svg"}
                  className="img-profile"
                  width={24}
                  height={24}
                  alt=""
                />
                <span>Scheduled Interview</span>
              </Link>
            </li>

            <li
              className={`sidebar-item ${router.pathname.startsWith(APP_ROUTE.CANDIDATE_INTREVIEW_HISTORY) ? "active" : ""}`}>
              <Link
                className="sidebar-link align-middle gap-2"
                href={APP_ROUTE.CANDIDATE_INTREVIEW_HISTORY}>
                <Image
                  src={"/images/icons/interview-history-icon.svg"}
                  className="img-profile"
                  width={24}
                  height={24}
                  alt=""
                />
                <span>Interview History</span>
              </Link>
            </li>

            <li
              className={`sidebar-item ${router.pathname.startsWith(APP_ROUTE.CANDIDATE_MATCHING_JOB) ? "active" : ""}`}>
              <Link
                className="sidebar-link align-middle gap-2"
                href={APP_ROUTE.CANDIDATE_MATCHING_JOB}>
                <Image
                  src={"/images/icons/work.svg"}
                  className="img-profile"
                  width={24}
                  height={24}
                  alt=""
                />
                <span>Matching Jobs</span>
              </Link>
            </li>

            <div className="divide-sidenav mb-3 mt-4">Settings</div>
            <li
              className={`sidebar-item ${router.pathname.startsWith(APP_ROUTE.CANDIDATE_SELF_PROFILE) ? "active" : ""}`}>
              <Link
                className="sidebar-link align-middle gap-2"
                href={APP_ROUTE.CANDIDATE_SELF_PROFILE}>
                <Image
                  src={"/images/icons/my-profile-icon.svg"}
                  className="img-profile"
                  width={24}
                  height={24}
                  alt=""
                />
                <span>My Profile</span>
              </Link>
            </li>
            <li
              className={`sidebar-item ${router.pathname.startsWith(APP_ROUTE.CANDIDATE_SELF_SETTING) ? "active" : ""}`}>
              <Link
                className="sidebar-link align-middle gap-2"
                href={APP_ROUTE.CANDIDATE_SELF_SETTING}>
                <Image
                  src={"/images/icons/other-settings-icon.svg"}
                  className="img-profile"
                  width={24}
                  height={24}
                  alt=""
                />
                <span>Settings</span>
              </Link>
            </li>
          </ul>
        </div>
      </Nav>
    </>
  );
};
