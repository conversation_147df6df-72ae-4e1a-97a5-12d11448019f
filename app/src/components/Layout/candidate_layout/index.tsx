import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Head from "next/head";
import Loader from "@src/components/Loader/Loader";
import { Header } from "./Header";
import { AuthCandidateInterface } from "@src/redux/interfaces";
import { useRouter } from "next/router";
import { useAppDispatch } from "@src/redux/store";
import { candidateVerify, logoutCandidate } from "@src/redux/actions";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { Sidebar } from "./Sidebar";
import DialogContainer from "@src/components/Dialog/DialogContainer";

type CandidateLayoutProps = {
  children: React.ReactNode;
  subdomain: string;
};

export const CandidateLayout: React.FC<CandidateLayoutProps> = ({
  children,
}) => {
  const [loader, setLoader] = useState<boolean>(true);
  const { loader: loading, loaderText } = useSelector(
    (state: RootState) => state.loader,
  );
  const candidate = useSelector((state: RootState) => state.auth.candidate);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const verifyUserAuth = async () => {
    const { success, message: msg } = await dispatch(candidateVerify());
    if (!success) {
      flashMessage(msg, "error");
      await dispatch(logoutCandidate());
      await router.replace(APP_ROUTE.CANDIDATE_LOGIN);
    }
    await setLoader(false);
  };

  useEffect(() => {
    if (candidate) {
      verifyUserAuth();
    } else {
      router.replace(APP_ROUTE.CANDIDATE_LOGIN);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [candidate?.id]);

  if (loader) {
    return (
      <div>
        <div id="wrapper" />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Recruitease Pro</title>
        <meta name="description" content="Recruitease Pro" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DialogContainer />
      {loading && <Loader loaderText={loaderText} />}

      <section className="candidate-layout">
        <div id="wrapper">
          <Sidebar />
          <div className="main">
            {candidate && (
              <Header candidate={candidate as AuthCandidateInterface} />
            )}
            <div id="content" className="content">
              <div className="container-fluid p-0">{children}</div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
