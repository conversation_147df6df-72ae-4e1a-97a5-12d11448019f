import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import Head from "next/head";
import Loader from "@src/components/Loader/Loader";
import { Header } from "./Header";
import DialogContainer from "@src/components/Dialog/DialogContainer";

type WildcardPublicLayoutProps = {
  children: React.ReactNode;
  subdomain: string;
};

export const WildcardPublicLayout: React.FC<WildcardPublicLayoutProps> = ({
  children,
}) => {
  const { loader: loading, loaderText } = useSelector(
    (state: RootState) => state.loader,
  );

  return (
    <>
      <Head>
        <title>Recruitease Pro</title>
        <meta name="description" content="Recruitease Pro" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <DialogContainer />
      {loading && <Loader loaderText={loaderText} />}
      <Header />
      <section className="candidate-layout private-layout wildcard-public-layout h-100">
        <div id="wrapper">
          <div className="container-fluid">{children}</div>
        </div>
      </section>
    </>
  );
};
