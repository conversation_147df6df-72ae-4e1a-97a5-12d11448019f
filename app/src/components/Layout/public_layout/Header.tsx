import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { APP_ROUTE } from "@src/constants";
import useWindowWidth from "@src/helper/resize";
import useDarkHeader from "@src/helper/DarkHeaderCallback";
import { useRouter } from "next/router";

export const Header = (): JSX.Element => {
  const [prevOpen, setPrevOpen] = useState<boolean>(false);
  const [toggle, setToggle] = useState<boolean>(false);
  const width = useWindowWidth();
  const isDark = useDarkHeader();
  const router = useRouter();

  useEffect(() => {
    if (width > 768 && prevOpen) {
      setToggle(false);
    }
  }, [width, prevOpen]);

  useEffect(() => {
    if (prevOpen !== toggle) {
      setPrevOpen(toggle);
    }
  }, [toggle, prevOpen]);

  return (
    <>
      <header
        className={`header position-fixed w-100 top-0 z-9  ${isDark ? "darkHeader" : ""}`}>
        <nav className="navbar navbar-expand-lg py-2">
          <div className="container">
            <Link
              className="navbar-brand"
              href={APP_ROUTE.HOME.subdomainLink("www")}>
              <Image
                src="/images/logo-white-1.svg"
                alt="logo"
                height={38}
                width={256}
                priority
                className="h-auto"
              />
            </Link>
            <button
              className={`navbar-toggler`}
              type="button"
              data-bs-toggle="collapse"
              onClick={() => setToggle(!toggle)}
              aria-controls="navbarSupportedContent"
              aria-expanded={toggle ? "true" : "false"}
              aria-label="Toggle navigation">
              <span className="navbar-toggler-icon" />
            </button>
            <div className={`collapse navbar-collapse ${toggle ? "show" : ""}`}>
              <ul className="navbar-nav navbar-nav ms-auto main-navbar">
                <li className="nav-item">
                  <Link
                    className={`nav-link ${router.pathname == APP_ROUTE.HOME ? "active" : ""}`}
                    href={APP_ROUTE.HOME.subdomainLink("www")}
                    onClick={() => setToggle(!toggle)}>
                    Home
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${router.pathname.startsWith(APP_ROUTE.ABOUT_US) ? "active" : ""}`}
                    href={APP_ROUTE.ABOUT_US}
                    onClick={() => setToggle(!toggle)}>
                    About Us
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${router.pathname.startsWith(APP_ROUTE.WORK_PROCESS) ? "active" : ""}`}
                    href={APP_ROUTE.WORK_PROCESS}
                    onClick={() => setToggle(!toggle)}>
                    How It Works
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${router.pathname.startsWith(APP_ROUTE.CONTACT_US) ? "active" : ""}`}
                    href={APP_ROUTE.CONTACT_US}
                    onClick={() => setToggle(!toggle)}>
                    Contact Us
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    href={APP_ROUTE.REGISTER.subdomainLink("admin")}
                    className="btn btn-border rounded-5 medium"
                    type="submit">
                    Sign Up
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </header>
    </>
  );
};
