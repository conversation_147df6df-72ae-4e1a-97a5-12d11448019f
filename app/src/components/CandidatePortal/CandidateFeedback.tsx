import React, { useState, RefObject, ReactNode } from "react";
import type { CustomSelectDateEvent } from "@src/components/input/InputField";
import type { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { Form, Button } from "antd";
import { validateFieldError } from "@src/helper/validations/custom";
import { ValidateInputValue } from "@src/helper/common";
import { KeyPairInterface } from "@src/redux/interfaces";
import type { CustomSelectChangeEvent } from "../input/SelectField";
import DynamicFormField from "../input/DynamicFormField";
import StarRateIcon from "@mui/icons-material/StarRate";

type ModalFormInput = {
  state: KeyPairInterface;
  setState: React.Dispatch<React.SetStateAction<KeyPairInterface>>;
  fields: GlobalInputFieldType[];
  onSubmit: Function;
  onClose?: Function;
  onResend?: Function;
  buttonTitle: string;
  cancelButtonTitle?: string;
  checkbox?: boolean;
  checkboxTitle?: string;
  checkboxKey?: string;
  submitRef?: RefObject<HTMLButtonElement>; // or any other HTML element type
  submitClass?: string;
  groupClasses?: KeyPairInterface;
  customButtons?: ReactNode;
  customError?: string;
  formClass?: string;
};
export const ModalFeedbackForm: React.FC<ModalFormInput> = ({
  setState,
  fields,
  state,
  onSubmit,
  buttonTitle,
  submitRef = null,
  submitClass = "",
  formClass = undefined,
}) => {
  const [error, setError] = useState<KeyPairInterface>({});
  const [hover, setHover] = useState(0);

  const handleInputChangeEvent = async (
    e: React.ChangeEvent<
      HTMLInputElement | CustomSelectChangeEvent | CustomSelectDateEvent
    >,
  ) => {
    const { error, key, value } = ValidateInputValue(e);
    setState((prev: KeyPairInterface) => ({ ...prev, [key]: value }));
    setError((prev: KeyPairInterface) => ({ ...prev, [key]: error }));
  };

  const handleKeyDownEvent = async (
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    const target = e.target as any;
    const targetType = target?.type ?? ("text" as string);
    if (
      (e.key === "Enter" || e.key === "enter") &&
      !["textarea"].includes(targetType)
    ) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleSubmit = (e: any) => {
    const isValid = validate();
    if (isValid) {
      onSubmit();
    }
    return isValid;
  };

  const validate = () => {
    let isValid = true;
    fields.forEach((field: GlobalInputFieldType) => {
      if (field.name.toLowerCase() === "rating") {
        const valid = validateRating();
        isValid = valid;
      } else {
        const value: string = state[field.name] ?? "";
        const error = validateFieldError({ ...field, value });
        if (field.disabled) {
          setError((prev) => ({ ...prev, [field.name]: "" }));
        } else if (error) {
          setError((prev) => ({ ...prev, [field.name]: error.errorMsg }));
          if (error.errorMsg.length) {
            isValid = false;
          }
        }
      }
    });
    return isValid;
  };

  // handle click event for rating
  const handleClick = (rate: any) => {
    if (rate) {
      setError((prev: KeyPairInterface) => ({
        ...prev,
        rating: "",
      }));
    }
    setState((prev) => ({ ...prev, rating: rate }));
  };

  // validate rating
  const validateRating = () => {
    if (!state?.rating) {
      setError((prev: KeyPairInterface) => ({
        ...prev,
        rating: "Rate Your Interview Experience is required",
      }));
      return false;
    } else {
      return true;
    }
  };

  return (
    <>
      <Form className={formClass}>
        {fields?.map((field, fieldIndex) => {
          return (
            <>
              {field.name?.toLowerCase() === "rating" ? (
                <div className="col-lg-12 text-center">
                  <div
                    className="d-inline-flex mx-auto gap-2 flex-wrap justify-content-center py-3 px-4"
                    style={{ background: "#4864E10A", borderRadius: "10px" }}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <StarRateIcon
                        key={star}
                        className={`fs-4 ${(hover || state?.rating) >= star ? "text-warning" : "text-secondary"}`}
                        onMouseEnter={() => setHover(star)}
                        onMouseLeave={() => setHover(0)}
                        onClick={() => handleClick(star)}
                        style={{
                          cursor: "pointer",
                          transition: "color 0.2s",
                          fill:
                            (hover || state?.rating) >= star
                              ? "#facc15"
                              : "#D3D3D3",
                          width: "30px",
                          height: "30px",
                        }}
                      />
                    ))}
                  </div>
                  <p className="rating-error m-0 w-100 text-center">
                    {error?.rating}
                  </p>
                </div>
              ) : (
                <DynamicFormField
                  key={fieldIndex}
                  placeholder={`Enter ${field.label}`}
                  value={state[field.name]}
                  error={error[field.name]}
                  onChangeInput={handleInputChangeEvent}
                  onKeyDown={handleKeyDownEvent}
                  {...field}
                />
              )}
            </>
          );
        })}
      </Form>

      <div className="ant-modal-footer m-0 justify-content-center d-flex border-0">
        {/* {customButtons} */}
        <Button
          className={`btn btn-theme ${submitClass}`}
          onClick={handleSubmit}
          ref={submitRef}>
          {buttonTitle}
        </Button>
      </div>
    </>
  );
};
