import { OpportunityInterface } from "@src/redux/interfaces";
import WorkIcon from "@mui/icons-material/Work";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import { Button } from "react-bootstrap";
import { useAppDispatch } from "@src/redux/store";
import { openDialog } from "@src/redux/actions";
import DialogComponents from "../DialogComponents";

type RenderOpptotunityType = {
  jobDetail: OpportunityInterface;
  applyJob?: (job: OpportunityInterface) => void;
};

export const JobCard = ({
  jobDetail,
  applyJob = undefined,
}: RenderOpptotunityType) => {
  const { experience, salary } = jobDetail;
  const dispatch = useAppDispatch();

  const openJobDetail = (jobDetail: OpportunityInterface) => {
    const {
      experience,
      salary,
      department_name,
      location_names,
      description,
      responsibilities,
    } = jobDetail;

    const formatTextAsBullets = (text: string): JSX.Element => {
      const lines = text.split("\n"); // Split text by newlines
      return (
        <ul>
          {" "}
          {/* Add padding for bullet visibility */}
          {lines.map((line, index) => (
            <li
              key={index}
              style={{ listStyleType: "disc", marginBottom: "0.5rem" }}>
              {line}
            </li>
          ))}
        </ul>
      );
    };

    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Job Detail",
          width: "50%",
          className: "job-detail",
          message: (
            <>
              <div className="p-3">
                <div className="requirement">
                  <h4 className="mb-4 fw-semibold heading-clr">
                    {jobDetail.title}
                  </h4>
                  <h6 className="sub-head">Requirement:</h6>
                  {jobDetail.skills && (
                    <div className="skills d-flex gap-3 p-3 rounded-3">
                      <span>Skills:</span>
                      <p className="m-0 fw-medium text-black">
                        {jobDetail.skills}
                      </p>
                    </div>
                  )}
                  <div className="row">
                    <div className="col-6">
                      <div className="content">
                        <ul>
                          <li>
                            <span>Designation:</span>
                            <b>{jobDetail.designation}</b>
                          </li>
                          <li>
                            <span>Department:</span>
                            <b>{department_name}</b>
                          </li>
                          <li>
                            <span>No. of Vacancies:</span>
                            <b>{jobDetail.number_of_vacancies}</b>
                          </li>
                          <li>
                            <span>Experience Required:</span>
                            {experience > 0 ? (
                              <b>Up to {experience} Years</b>
                            ) : (
                              <b>-</b>
                            )}
                          </li>
                          <li>
                            <span>Salary:</span>
                            {salary > 0 ? <b>{salary}</b> : <b>-</b>}
                          </li>
                          <li>
                            <span>Location:</span>
                            <b
                              dangerouslySetInnerHTML={{
                                __html: `${location_names?.join(",  ") ?? ""}`,
                              }}
                            />
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="content job-overview-and-responsibilities">
                        <div className="job-overview">
                          <h6 className="sub-head">Job Overview:</h6>
                          <p className="m-0">
                            {formatTextAsBullets(description)}
                          </p>
                        </div>

                        <div className="key-responsibilities">
                          <h6 className="sub-head">Key Responsibilities:-</h6>
                          {formatTextAsBullets(responsibilities)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ),
        },
      }),
    );
  };

  return (
    <>
      <div className="col-md-6 col-lg-6 col-xl-6 col-sm-12 mb-3">
        <div className="candidate-list-card bg-white border rounded-3 overflow-hidden">
          <div className="p-3 pb-4">
            <div className="d-flex justify-content-between align-items-start flex-wrap flex-lg-nowrap position-relative">
              <div className="candidate-des d-flex gap-3 flex-wrap flex-lg-nowrap">
                <div className="brief">
                  <h4 className="heading-clr mb-3">{jobDetail.title}</h4>
                  <p className="text-clr m-0">{jobDetail.description}</p>
                  {jobDetail.location_names.map(
                    (location: string, index: number) => (
                      <p className="text-clr m-0 mt-2 mb-2 ps-4" key={index}>
                        <LocationOnIcon
                          style={{
                            fill: "var(--theme-primary-color)",
                            left: "-4px",
                          }}
                          className="position-absolute"
                        />{" "}
                        <b>{location}</b>
                      </p>
                    ),
                  )}

                  <ul className="pb-4">
                    <li>
                      Experience:{" "}
                      {experience > 0 ? (
                        <b>Up to {experience} yrs</b>
                      ) : (
                        <b>-</b>
                      )}
                    </li>
                    {salary && (
                      <li>
                        Salary: <b>{salary && salary > 0 ? salary : "-"}</b>
                      </li>
                    )}
                    <li className="job-part">
                      <WorkIcon
                        style={{
                          fill: "var(--theme-primary-color)",
                        }}
                      />
                      full time
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="d-flex mt-auto gap-2">
              <Button
                className="btn w-100 rounded-0"
                variant="light"
                onClick={() => openJobDetail(jobDetail)}>
                View Detail
              </Button>
              {applyJob != undefined && (
                <Button
                  className="btn w-100 rounded-0"
                  variant="primary"
                  onClick={() => applyJob(jobDetail)}>
                  Apply Job
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
