import React, { useState } from "react";
import { CardElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { useAppDispatch } from "@src/redux/store";
import { Button } from "antd";
import flashMessage from "../FlashMessage";
import { setLoader } from "@src/redux/actions";
import Form from "react-bootstrap/Form";
import { wildCardPaymentApi } from "@src/apis/wildcardApis";

interface UpgradePlanFormProps {
  close: () => void;
  onPayment: () => void;
  readonly selectedPlan: any;
  readonly buttonTitle?: string;
}

// Confirmation modal component
export const UpgradePlanForm = ({
  close,
  onPayment,
  selectedPlan,
  buttonTitle = "Upgrade",
}: UpgradePlanFormProps) => {
  // Function to handle confirmation button click

  const stripe = useStripe();
  const elements = useElements();
  const dispatch = useAppDispatch();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string>("");
  const [isCardComplete, setIsCardComplete] = useState<boolean>(false); // State to track card validity

  const makePayment = async () => {
    if (!stripe || !elements) return;
    setError("");
    dispatch(setLoader(true)); // Start loader
    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      setError("Card element not found");
      return;
    }

    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: "card",
      card: cardElement,
    });

    if (error) {
      setError(error.message ?? "");
    }

    if (!paymentMethod) return;

    const { success, ...response } = await wildCardPaymentApi.buyPlan({
      payment_plan_id: selectedPlan.id,
      payment_method_id: paymentMethod.id,
    });

    if (success) {
      const { client_secret } = response.data;
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        client_secret,
        {
          payment_method: { card: cardElement },
        },
      );

      if (error) {
        dispatch(setLoader(false));
        flashMessage(error.message || "An error occurred", "error");
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        setTimeout(() => {
          dispatch(setLoader(false));
          onPayment();
          close();
        }, 5000);
      } else {
        dispatch(setLoader(false));
      }
    } else {
      flashMessage(response.message, "error");
      dispatch(setLoader(false));
    }
  };

  const handleCardChange = (event: any) => {
    // Check if card details are valid and complete
    setIsCardComplete(event.complete);
    if (event.error) {
      setError(event.error.message);
    } else {
      setError("");
    }
  };

  return (
    <>
      <Form.Group className="mb-3 group-relative payment-card">
        <Form.Label>Plan Detail</Form.Label>
        <div className="subscription-card mb-2">
          <h6>{selectedPlan.product_name}</h6>
          <p className="subscription-plan-price">
            {selectedPlan.amount} {selectedPlan.currency.toUpperCase()}
          </p>
          <p className="subscription-plan-description">
            {selectedPlan.description}
          </p>
        </div>

        <Form.Label>Credit or debit card</Form.Label>
        <div className="card-element-container p-3 border rounded">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: "16px",
                  color: "#424770",
                  "::placeholder": {
                    color: "#aab7c4",
                  },
                },
                invalid: {
                  color: "#9e2146",
                },
              },
              hidePostalCode: true,
            }}
            onChange={handleCardChange}
          />
        </div>

        {error && <p className="error">{error}</p>}
      </Form.Group>

      <div className="ant-modal-footer">
        <Button
          className={`btn btn-theme`}
          disabled={!stripe || processing || !isCardComplete}
          onClick={() => makePayment()}>
          {processing
            ? "Processing..."
            : `${buttonTitle} ${selectedPlan.amount} ${selectedPlan.currency.toUpperCase()}`}
        </Button>
        <Button
          className={`btn btn-theme`}
          disabled={!stripe || processing}
          onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};
