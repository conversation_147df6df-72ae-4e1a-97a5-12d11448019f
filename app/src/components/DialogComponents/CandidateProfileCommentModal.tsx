import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateProfileCommentInterface,
  CandidateInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { candidateApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new business form
const CandidateProfileCommentFields: GlobalInputFieldType[] = [
  {
    name: "comment",
    label: "Any Comment",
    type: "textarea",
    dataType: "text",
    placeholder: "Add Comment",
    maxLength: 500,
    required: true,
    rows: 4,
  },
];

// Default values for the new business form
const defaultValue: KeyPairInterface = {
  comment: "",
};

// Props for the CandidateFeebackModal component
type CandidateProfileCommentModalProps = {
  candidate: CandidateInterface;
  close: Function;
  onSubmitComment?: (comment: CandidateProfileCommentInterface) => void;
};

// Component for the modal to create a new business
const CandidateProfileCommentModal = ({
  candidate,
  close,
  onSubmitComment,
}: CandidateProfileCommentModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new business
    const { success, ...response } = await candidateApi.addComment(
      candidate.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");

    if (success) {
      onSubmitComment && onSubmitComment(response.data);
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Submit" // Title for the submit button
        fields={CandidateProfileCommentFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default CandidateProfileCommentModal;
