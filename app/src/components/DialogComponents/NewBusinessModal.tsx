import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { BusinessInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { adminBusinessApi } from "@src/apis/adminApis";
import flashMessage from "../FlashMessage";
import { Timezones } from "@src/helper/selectOptions";

// Fields for the new business form
const NewBusinessFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Business Name",
    type: "text",
    dataType: "Alphanumeric",
    maxLength: 80,
    required: true,
  },
  {
    name: "contact_number",
    label: "Contact Number",
    type: "mobilenumber",
    dataType: "mobilenumber",
    maxLength: 15,
    required: true,
  },
  {
    name: "location",
    label: "Location",
    type: "text",
    dataType: "text",
    maxLength: 150,
    required: true,
  },
  {
    name: "website",
    label: "Website",
    type: "text",
    dataType: "websiteDomain",
    maxLength: 80,
    required: true,
    tooltipTitle:
      "This field only accepts domain names, including optional subdomains. Ensure the domain does not include paths or ports. Eg example.com, https://example.com, sub.example.com",
  },
  {
    name: "email",
    label: "Business Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
    tooltipTitle: "Email should contain website domain",
  },
];

// Default values for the new business form
const defaultValue: KeyPairInterface = {
  name: "",
  website: "",
  email: "",
  location: "",
  contact_number: "",
  timezone: "UTC",
};

// Props for the NewBusinessModal component
type NewBusinessModalProps = {
  onBusinessCreate: (business: BusinessInterface) => void;
  close: Function;
};

// Component for the modal to create a new business
const NewBusinessModal = ({
  close,
  onBusinessCreate,
}: NewBusinessModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);
  const [timezoneOptions, setTimezoneOptions] = useState<
    {
      label: string;
      value: string;
    }[]
  >(Timezones);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new business
    const { success, ...response } =
      await adminBusinessApi.adminCreateBusiness(state);
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If business creation is successful, call the callback function to update the UI
    if (success) {
      onBusinessCreate(response.data);
    }
  };

  function filterTimezonez(inputValue: string) {
    // Filter timezones matching the input search
    const filtered = Timezones.filter((tz) =>
      tz.label.toLowerCase().includes(inputValue.toLowerCase()),
    );

    setTimezoneOptions(filtered);
  }

  const BusinessFields = [
    ...NewBusinessFields,
    {
      name: "timezone",
      label: "Business Timezone",
      type: "select",
      dataType: "select",
      placeholder: "Select Timezone",
      required: true,
      tooltipTitle: "Business Timezone",
      showSearch: true,
      options: timezoneOptions,
      onSearch: filterTimezonez,
    },
  ];

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={BusinessFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewBusinessModal;
