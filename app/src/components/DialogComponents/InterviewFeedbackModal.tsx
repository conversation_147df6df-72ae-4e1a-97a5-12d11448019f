import { useEffect, useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { InterviewInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import {
  setLoader,
  updateCandidateInterviewRecordState,
} from "@src/redux/actions";
// import { interviewFeedbackApi } from "@src/apis/adminApis";
import flashMessage from "../FlashMessage";
import { RatingOptions } from "@src/helper/selectOptions";
import { interviewApi } from "@src/apis/wildcardApis";
import { ModalFeedbackForm } from "../CandidatePortal/CandidateFeedback";

// Fields for the new business form

const InterviewFeedbackFields: GlobalInputFieldType[] = [
  {
    name: "rating",
    label: "Rating",
    type: "select",
    dataType: "select",
    options: RatingOptions,
    placeholder: "Select Rating",
    required: true,
    groupName: "Feedback Detail",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-12",
  },

  {
    name: "feedback",
    label: "Feedback",
    type: "textarea",
    dataType: "text",
    placeholder: "Add Feedback",
    maxLength: 500,
    required: true,
    groupName: "Feedback Detail",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12",
    rows: 4,
  },
];

// Default values for the new business form
const defaultValue: KeyPairInterface = {
  rating: "",
  feedback: "",
};

// Props for the InterviewFeedbackModal component
type InterviewFeedbackModalProps = {
  interview: InterviewInterface;
  close: Function;
};

// Component for the modal to create a new business
const InterviewFeedbackModal = ({
  interview,
  close,
}: InterviewFeedbackModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  useEffect(() => {
    if (interview) {
      setState((prevState) => ({
        ...prevState,
        candidate_name: `${interview.candidate_name} - ${interview.candidate_email}`,
        job_title: interview.opportunity_title,
        interview_round: interview.interview_round,
      }));
    }
  }, [interview]);

  // Function to handle form submission
  const onSubmit = async () => {
    // Validate the form state before submission
    if (!state.rating || !state.feedback) {
      flashMessage("Please fill in all required fields.", "error");
      return;
    }
    if (state.approved_status === undefined) {
      flashMessage("Please select Qualified Status.", "error");
      return;
    }
    await dispatch(setLoader(true));
    // Call API to create a new business
    const { success, ...response } = await interviewApi.saveFeedback(
      interview.id,
      state,
    );
    await dispatch(setLoader(false));
    await dispatch(updateCandidateInterviewRecordState(response.data));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If business creation is successful, call the callback function to update the UI
    if (success) {
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <div className="text-center mt-1 d-inline-block w-100">
        <h5 className="ant-modal-title">Give Feedback & Review</h5>
        <p style={{ fontSize: "14px", color: "#262626" }}>
          Provide your feedback and rating for the interview.
        </p>
        <div className="d-flex gap-3 text-center justify-content-center flex-wrap">
          <button
            disabled={state.approved_status == 0}
            className="btn btn-primary mb-3"
            onClick={() => {
              setState((prev) => ({ ...prev, approved_status: 0 }));
            }}>
            Approved
          </button>
          <button
            className="btn btn-danger mb-3"
            disabled={state.approved_status == 1}
            onClick={() => {
              setState((prev) => ({ ...prev, approved_status: 1 }));
            }}>
            Not Qualified
          </button>
        </div>
        <ModalFeedbackForm
          buttonTitle="Save" // Title for the submit button
          fields={InterviewFeedbackFields} // Fields for the form
          setState={setState} // Function to update form state
          state={state} // Current form state
          onSubmit={onSubmit} // Function to handle form submission
          onClose={close} // Function to close the modal
        />
      </div>
    </>
  );
};

export default InterviewFeedbackModal;
