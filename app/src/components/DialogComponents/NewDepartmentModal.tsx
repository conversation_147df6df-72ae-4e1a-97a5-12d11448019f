import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { DepartmentInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { departmentApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new department form
const NewDepartmentFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Department Name",
    type: "text",
    dataType: "nameWithHyphenAndDot",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "description",
    label: "Department Description",
    type: "textarea",
    dataType: "text",
    maxLength: 255,
    required: false,
  },
];

// Default values for a new department
const defaultValue: KeyPairInterface = {
  name: "",
  description: "",
};

// Props for the NewDepartmentModal component
type NewDepartmentModalProps = {
  onDepartmentCreate: (department: DepartmentInterface) => void;
  close: Function;
};

// Component for the modal to add a new department
const NewDepartmentModal = ({
  close,
  onDepartmentCreate,
}: NewDepartmentModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create department
    const { success, ...response } =
      await departmentApi.createDepartment(state);
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If department creation is successful, pass the new department data to the parent component
    if (success) {
      close();
      onDepartmentCreate(response.data);
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={NewDepartmentFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewDepartmentModal;
