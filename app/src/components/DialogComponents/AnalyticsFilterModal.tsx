import React, { useState } from 'react';
import { KeyPairInterface } from "@src/redux/interfaces";
import { Tabs } from "antd";
import { AnalyticsFilter, AnalyticsFilterHistory } from '../WildCard';

type AnalyticsFiltersModalProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

export const AnalyticsFilterModal = (props: AnalyticsFiltersModalProps) => {
  const [filters, setFilters] = useState<KeyPairInterface>(props.filters || {});

  const handleConfirm = (newFilters: KeyPairInterface) => {
    // Clean up the filters - remove empty values and save_history
    const cleanedFilters: KeyPairInterface = {};

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== "" && key !== "save_history") {
        cleanedFilters[key] = value;
      }
    });

    setFilters(cleanedFilters);
    props.onConfirm(cleanedFilters);
  };

  return (
    <Tabs
      defaultActiveKey="Filter"
      animated={{ tabPane: true, inkBar: true }}
      items={[
        {
          key: "Filter",
          label: `New Search`,
          children: <AnalyticsFilter {...props} filters={filters} onConfirm={handleConfirm} />,
        },
        {
          key: "History",
          label: `Search History`,
          children: (
            <AnalyticsFilterHistory {...props} onConfirm={handleConfirm} />
          ),
        },
      ]}
    />
  );
};