import type { GlobalReadOnlyFieldType } from "../input/GlobalInput";
import { LocationInterface } from "@src/redux/interfaces";
import { ReadOnlyField } from "../input/ReadOnlyField";

// Fields for the new category form
const ViewLocationFields: GlobalReadOnlyFieldType[] = [
  {
    name: "address",
    label: "Address",
    type: "text",
  },
  {
    name: "city",
    label: "City",
    type: "text",
  },
  {
    name: "state",
    label: "State",
    type: "text",
  },
  {
    name: "country",
    label: "Country",
    type: "text",
  },
  {
    name: "pincode",
    label: "Pincode",
    type: "text",
  },
];

// Props for the ViewCategoryModal component
type ViewlocationModalProps = {
  location: LocationInterface;
};

// Component for the modal to edit a category
const ViewLocationModal = ({ location }: ViewlocationModalProps) => {
  return (
    <>
      {/* Render the modal form input component */}
      <ReadOnlyField
        fields={ViewLocationFields} // Fields for the form
        state={location} // Current form state
      />
    </>
  );
};

export default ViewLocationModal;
