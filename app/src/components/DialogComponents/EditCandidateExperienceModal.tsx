import { useEffect, useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateNewExperienceInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { YesNoOptions } from "@src/helper/selectOptions";

// Fields for the new experience form
const EditCandidateExperienceFields: GlobalInputFieldType[] = [
  {
    name: "employer",
    label: "Employer/Company",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 100,
    required: true,
    fieldPosition: 1,
    className: "col-sm-12 col-md-6 col-lg-6",
  },
  {
    name: "position",
    label: "Designation",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 255,
    required: true,
    fieldPosition: 2,
    className: "col-sm-12 col-md-6 col-lg-6",
  },
];

// Props for the EditCandidateExperienceModal component
type EditCandidateExperienceModalProps = {
  onCandidateExperienceUpdate: (
    experience: CandidateNewExperienceInterface,
  ) => void;
  close: Function;
  experience: CandidateNewExperienceInterface;
};

// Component for the modal to edit a experience
const EditCandidateExperienceModal = ({
  experience,
  close,
  onCandidateExperienceUpdate,
}: EditCandidateExperienceModalProps) => {
  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>({
    ...experience,
    is_current_experience: !!experience.is_current_experience,
  });

  // Function to handle form submission
  const onSubmit = async () => {
    let { responsibilities, ...restState } = state;
    responsibilities = responsibilities ?? [];
    let uniqueResponsibilities = responsibilities
      .filter((item?: string) => item !== null && item !== undefined) // Remove null and undefined
      .map((item: string) => item.trim()) // Trim the strings
      .filter((item: string) => item !== ""); // Remove empty strings

    onCandidateExperienceUpdate({
      ...restState,
      responsibilities: uniqueResponsibilities,
    } as CandidateNewExperienceInterface);
    close();
  };

  useEffect(() => {
    try {
      if (state.start_date && state.end_date) {
        const startDate = new Date(state.start_date);
        const endDate = new Date(state.start_date);
        if (startDate > endDate) {
          setState((prevState) => ({ ...prevState, end_date: null }));
        }
      }
    } catch (e) {}
  }, [state.start_date, state.end_date]);

  let EditFields: Array<GlobalInputFieldType> = [
    ...EditCandidateExperienceFields,
    {
      name: "is_current_experience",
      label: "Currently Working",
      fieldPosition: 3,
      type: "select",
      dataType: "select",
      className: "col-sm-12 col-md-6 col-lg-6",
      options: YesNoOptions,
    },
    {
      name: "start_date",
      label: "Start Date",
      type: "date",
      dataType: "date",
      required: true,
      maxDate: state.end_date ? new Date(state.end_date).toISOString() : null,
      fieldPosition: 4,
      className: "col-sm-12 col-md-6 col-lg-6",
    },
    {
      name: "end_date",
      label: "End Date",
      type: "date",
      dataType: "date",
      required: !state.is_current_experience,
      disabled: state.is_current_experience,
      minDate: state.start_date
        ? new Date(state.start_date).toISOString()
        : null,
      fieldPosition: 5,
      className: "col-sm-12 col-md-6 col-lg-6",
    },

    {
      name: "responsibilities",
      label: "Responsibilities",
      type: "textarea",
      dataType: "textarea",
      placeholder: "Enter responsibility",
      multipleFields: true,
      fieldPosition: 5,
      className: "col-sm-12",
    },
  ];

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Save Detail" // Title for the submit button
        fields={EditFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditCandidateExperienceModal;
