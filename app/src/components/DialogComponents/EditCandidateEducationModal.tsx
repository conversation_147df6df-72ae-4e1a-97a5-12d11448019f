import { useEffect, useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateNewEducationInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";

// Fields for the new education form
const EditCandidateEducationFields: GlobalInputFieldType[] = [
  {
    name: "degree",
    label: "Degree",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 100,
    required: true,
    fieldPosition: 1,
    className: "col-sm-12",
  },
  {
    name: "university",
    label: "College/University",
    type: "text",
    dataType: "string",
    placeholder: "Enter College/University Name",
    minLength: 3,
    maxLength: 255,
    required: true,
    fieldPosition: 2,
    className: "col-sm-12",
  },
  {
    name: "grade",
    label: "Percentage",
    type: "text",
    dataType: "decimaltwodigit",
    maxLength: 255,
    required: true,
    fieldPosition: 5,
    className: "col-sm-12",
  },
];

// Props for the EditCandidateEducationModal component
type EditCandidateEducationModalProps = {
  onCandidateEducationUpdate: (
    education: CandidateNewEducationInterface,
  ) => void;
  close: Function;
  education: CandidateNewEducationInterface;
};

// Component for the modal to edit a education
const EditCandidateEducationModal = ({
  education,
  close,
  onCandidateEducationUpdate,
}: EditCandidateEducationModalProps) => {
  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(education);

  // Function to handle form submission
  const onSubmit = async () => {
    onCandidateEducationUpdate(state as CandidateNewEducationInterface);
    close();
  };

  useEffect(() => {
    try {
      if (state.start_date && state.end_date) {
        const startDate = new Date(state.start_date);
        const endDate = new Date(state.start_date);
        if (startDate > endDate) {
          setState((prevState) => ({ ...prevState, end_date: null }));
        }
      }
    } catch (e) {}
  }, [state.start_date, state.end_date]);

  const EditFields: Array<GlobalInputFieldType> = [
    ...EditCandidateEducationFields,
    {
      name: "start_date",
      label: "Start Date",
      type: "date",
      dataType: "date",
      required: true,
      maxDate: state.end_date ? new Date(state.end_date).toISOString() : null,
      className: "col-sm-12",
      fieldPosition: 3,
    },
    {
      name: "end_date",
      label: "End Date",
      type: "date",
      dataType: "date",
      required: true,
      minDate: education.start_date
        ? new Date(state.start_date).toISOString()
        : null,
      className: "col-sm-12",
      fieldPosition: 4,
    },
  ];

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Save Detail" // Title for the submit button
        fields={EditFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditCandidateEducationModal;
