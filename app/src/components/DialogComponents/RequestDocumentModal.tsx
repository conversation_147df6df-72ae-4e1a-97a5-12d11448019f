import React, { useCallback, useEffect, useState } from "react";
import { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface } from "@src/redux/interfaces";
import { setLoader } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import { candidateApi } from "@src/apis/wildcardApis";

type RequestDocumentModalProps = {
  onConfirm: (state: KeyPairInterface) => Function; // Function to call when the confirmation button is clicked
  close: Function; // Function to close the modal
  candidateId: number;
};

// Confirmation modal component
const RequestDocumentModal = ({
  close,
  onConfirm,
  candidateId,
}: RequestDocumentModalProps) => {
  const dispatch = useAppDispatch();
  // Function to handle confirmation button click
  const [state, setState] = useState<KeyPairInterface>({
    adhar_certificate: true,
    pan_certificate: true,
    degree_certificate: true,
    other_certificate: true,
  });
  const [requested, setRequested] = useState<KeyPairInterface>({
    adhar_certificate: false,
    pan_certificate: false,
    degree_certificate: false,
    other_certificate: false,
  });

  const [fields, setFields] = useState<GlobalInputFieldType[]>([
    {
      name: "pan_certificate",
      label: "Pan Card",
      type: "checkbox",
      dataType: "boolean",
    },
    {
      name: "adhar_certificate",
      label: "Adhar Card",
      type: "checkbox",
      dataType: "boolean",
    },
    {
      name: "degree_certificate",
      label: "Degree/Deploma Certificate",
      type: "checkbox",
      dataType: "boolean",
    },
    {
      name: "other_certificate",
      label: "Other Certificate",
      type: "checkbox",
      dataType: "boolean",
    },
  ]);

  const fetchAndUpdateFields = useCallback(
    async (candidateId: number) => {
      await dispatch(setLoader(true));
      const { success, ...response } =
        await candidateApi.checkDocumentRequest(candidateId);
      if (success) {
        const {
          adhar_certificate,
          pan_certificate,
          degree_certificate,
          other_certificate,
        } = response.data;

        setRequested({
          pan_certificate: pan_certificate.saved || pan_certificate.requested,
          adhar_certificate:
            adhar_certificate.saved || adhar_certificate.requested,
          degree_certificate:
            degree_certificate.saved || degree_certificate.requested,
          other_certificate:
            other_certificate.saved || other_certificate.requested,
        });
        setFields([
          {
            name: "pan_certificate",
            label: `Pan Card ${pan_certificate.saved ? "(Saved)" : pan_certificate.requested ? "(Requested)" : ""}`,
            type: "checkbox",
            dataType: "boolean",
            disabled: pan_certificate.saved || pan_certificate.requested,
          },
          {
            name: "adhar_certificate",
            label: `Adhar Card ${adhar_certificate.saved ? "(Saved)" : adhar_certificate.requested ? "(Requested)" : ""}`,
            type: "checkbox",
            dataType: "boolean",
            disabled: adhar_certificate.saved || adhar_certificate.requested,
          },
          {
            name: "degree_certificate",
            label: `Degree/Deploma Certificate ${degree_certificate.saved ? "(Saved)" : degree_certificate.requested ? "(Requested)" : ""}`,
            type: "checkbox",
            dataType: "boolean",
            disabled: degree_certificate.saved || degree_certificate.requested,
          },
          {
            name: "other_certificate",
            label: `Other Certificate ${other_certificate.saved ? "(Saved)" : other_certificate.requested ? "(Requested)" : ""}`,
            type: "checkbox",
            dataType: "boolean",
            disabled: other_certificate.saved || other_certificate.requested,
          },
        ]);
      }
      await dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch, candidateId],
  );

  useEffect(() => {
    fetchAndUpdateFields(candidateId);
  }, [fetchAndUpdateFields, candidateId]);

  const [error, setError] = useState<string>("");

  useEffect(() => {
    const certificates = [
      "adhar_certificate",
      "pan_certificate",
      "degree_certificate",
      "other_certificate",
    ];

    // Flag to track if there is any error
    let hasError = true;
    let requestedCount = 0;

    // Check each certificate
    certificates.forEach((cert) => {
      if (requested[cert] == false && state[cert]) {
        hasError = false; // If any certificate is either requested or selected, no error
      }
      if (requested[cert]) {
        requestedCount += 1;
      }
    });

    if (hasError && requestedCount < 4) {
      setError("Please select atleast a single certificate.");
    } else if (requestedCount >= 4) {
      setError("Documents have already been requested/saved.");
    } else {
      setError("");
    }
  }, [state, requested]);

  const onSubmit = () => {
    if (!error) {
      onConfirm(state);
    }
  };

  return (
    <>
      {/* Display the message */}
      <h6 className="mt-2 mb-2 fw-bold">
        Select the documents you want to request from the list below.
      </h6>

      <ModalFormInput
        buttonTitle={"Submit"} // Title for the submit button
        fields={fields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
        customError={error}
        formClass={"document-request-form mt-3"}
        submitClass={error ? "disabled" : ""}
      />
    </>
  );
};

export default RequestDocumentModal;
