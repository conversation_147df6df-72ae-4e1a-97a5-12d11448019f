import { useState, useEffect } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface, LocationInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { locationApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new location form
const EditLocationFields: GlobalInputFieldType[] = [
  {
    name: "address",
    label: "Address",
    type: "text",
    dataType: "alphabetics1",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "city",
    label: "City",
    type: "text",
    dataType: "alphabetics",
    maxLength: 100,
    required: true,
  },
  {
    name: "state",
    label: "State",
    type: "text",
    dataType: "alphabetics1",
    maxLength: 100,
    required: true,
  },
  {
    name: "country",
    label: "Country",
    type: "text",
    dataType: "alphabetics1",
    maxLength: 100,
    required: true,
  },
  {
    name: "pincode",
    label: "Pincode",
    type: "text",
    dataType: "pincode",
    maxLength: 10,
    required: true,
  },
];

// Props for the EditLocationModal component
type EditLocationModalProps = {
  onLocationUpdate: (location: LocationInterface) => void;
  close: Function;
  readonly location: LocationInterface;
};

// Component for the modal to edit a location
const EditLocationModal = ({
  location,
  close,
  onLocationUpdate,
}: EditLocationModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>({
    address: location.address,
    city: location.city,
    state: location.state,
    country: location.country,
    pincode: location.pincode,
  });

  useEffect(() => {
    setState({
      address: location.address,
      city: location.city,
      state: location.state,
      country: location.country,
      pincode: location.pincode,
    });
  }, [location]);

  // Function to handle form submission
  const onSubmit = async () => {
    // Validate pincode

    await dispatch(setLoader(true));
    // Call API to update location
    const { success, ...response } = await locationApi.updateLocation(
      location.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If location update is successful, close the modal and pass the updated location data to the parent component
    if (success) {
      close();
      onLocationUpdate(response.data);
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Update" // Title for the submit button
        fields={EditLocationFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditLocationModal;
