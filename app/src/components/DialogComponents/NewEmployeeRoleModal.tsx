import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { EmployeeRoleInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new category form
const NewEmployeeRoleFields: GlobalInputFieldType[] = [
  {
    name: "role",
    label: "Role",
    type: "text",
    dataType: "alphabetics",
    minLength: 3,
    maxLength: 25,
    required: true,
  },
];

// Default values for a new category
const defaultValue: KeyPairInterface = {
  role: "",
};

// Props for the NewCategoryModal component
type NewEmployeeRoleModalProps = {
  onEmployeeRoleCreate: (EmployeeRole: EmployeeRoleInterface) => void;
  close: Function;
};

// Component for the modal to add a new category
const NewEmployeeRoleModal = ({
  close,
  onEmployeeRoleCreate,
}: NewEmployeeRoleModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));

    // Convert pincode to number
    const formattedState = {
      ...state,
    };

    // Call API to create EmployeeRole
    const { success, ...response } =
      await employeeManagementApi.createEmployeeRole(formattedState);
    await dispatch(setLoader(false));

    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");

    // If EmployeeRole creation is successful, pass the new EmployeeRole data to the parent component
    if (success) {
      onEmployeeRoleCreate(response.data);
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={NewEmployeeRoleFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewEmployeeRoleModal;
