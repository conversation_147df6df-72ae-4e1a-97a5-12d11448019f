import { useCallback, useEffect, useState } from "react";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import { GlobalInputFieldType } from "@src/components/input/GlobalInput";
import { setLoader } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import flashMessage from "@src/components/FlashMessage";

export const useFetchEmployeeRoles = (
  DefaultEmployeeFields: GlobalInputFieldType[] = [],
  close: () => void,
) => {
  const dispatch = useAppDispatch();
  const [newEmployeeFields, setEmployeeFields] = useState<
    GlobalInputFieldType[]
  >(DefaultEmployeeFields);

  const fetchAndSetRoles = useCallback(async () => {
    try {
      await dispatch(setLoader(true));
      const { success, ...response } =
        await employeeManagementApi.getEmployeesRoles();
      if (success) {
        const options = response.data.map((role: any) => ({
          label: role.name,
          value: role.id,
        }));
        const employeeRoleField: GlobalInputFieldType = {
          name: "employee_role_id",
          label: "Role",
          type: "select",
          dataType: "select",
          placeholder: "Select role",
          selectEmpty: true,
          required: true,
          options: options,
        };
        setEmployeeFields([...DefaultEmployeeFields, employeeRoleField]);
      } else {
        flashMessage("Something went wrong!", "error");
        close();
      }
      await dispatch(setLoader(false));
    } catch (err) {
      console.error("Error fetching employees data:", err);
    } finally {
      await dispatch(setLoader(false));
    }
  }, [dispatch, close, DefaultEmployeeFields]);

  useEffect(() => {
    fetchAndSetRoles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return newEmployeeFields;
};
