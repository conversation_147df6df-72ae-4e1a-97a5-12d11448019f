import React from "react";
import { Button } from "antd";

// Props for the SuccessModal component
type SuccessModalProps = {
  message: string | React.ReactNode; // Message to display in the modal
  close: Function; // Function to close the modal
  closable: boolean;
};

// Component for the success modal
const SuccessModal = ({ message, close, closable }: SuccessModalProps) => {
  return (
    <>
      {/* Display the message */}
      {message ?? ""}
      {/* Modal footer with close button */}
      {closable && (
        <div className="ant-modal-footer">
          <Button className="btn" key="cancel" onClick={() => close()}>
            Cancel
          </Button>
        </div>
      )}
    </>
  );
};

export default SuccessModal;
