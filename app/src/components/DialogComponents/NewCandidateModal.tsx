import React, { useState } from "react";
import { UploadResume, SaveDuplicateResume, CloudResume } from "./Candidate";
import { useAppDispatch } from "@src/redux/store";
import { updateDialogProperties } from "@src/redux/actions";
import { DuplicateCandidateInterface } from "@src/redux/interfaces";

// Props for the NewCandidateModal component
type NewCandidateModalProps = {
  type: string;
  onCandidateCreate: (candidate: any) => void;
  close: Function;
};

// Component for the modal to add a new candidate
const NewCandidateModal = ({
  type,
  close,
  onCandidateCreate,
}: NewCandidateModalProps) => {
  const dispatch = useAppDispatch();
  const [hasDuplicate, setDuplicate] = useState<boolean>(false);
  const [jobId, setJobId] = useState<any>(null);
  const [records, setRecords] = useState<DuplicateCandidateInterface[]>([]);

  const onDuplicateEvent = (
    data: DuplicateCandidateInterface[],
    jobId: null | number,
  ) => {
    dispatch(
      updateDialogProperties({
        width: "50%",
      }),
    );
    setRecords(data);
    setDuplicate(true);
    setJobId(jobId);
  };

  if (hasDuplicate) {
    return (
      <>
        <SaveDuplicateResume
          onCandidateCreate={onCandidateCreate}
          records={records}
          jobId={jobId}
          close={close}
        />
      </>
    );
  }

  if (["Google Drive", "Dropbox"].includes(type)) {
    const resumeSource = type == "Google Drive" ? "Google Drive/Docs" : type;
    return (
      <CloudResume
        onCandidateCreate={onCandidateCreate}
        setDuplicateRecords={onDuplicateEvent}
        close={close}
        resumeSource={resumeSource}
      />
    );
  }

  return (
    <UploadResume
      onCandidateCreate={onCandidateCreate}
      setDuplicateRecords={onDuplicateEvent}
      close={close}
    />
  );
};

export default NewCandidateModal;
