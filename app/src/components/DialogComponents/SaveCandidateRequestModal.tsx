import React from "react";
import { Button } from "antd";
import { resumeExtractionApi } from "@src/apis/wildcardApis";
import { JobRequestInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";

// Props for the SaveCandidateRequestModal component
type SaveCandidateRequestModalProps = {
  job: JobRequestInterface;
  close: Function;
  onSaveCandidate?: (data: JobRequestInterface) => void;
};

// Component for the modal to add a new candidate
const SaveCandidateRequestModal = ({
  job,
  onSaveCandidate,
  close,
}: SaveCandidateRequestModalProps) => {
  const dispatch = useAppDispatch();

  const saveCandidateModal = async () => {
    dispatch(setLoader(true));
    const { success, ...response } =
      await resumeExtractionApi.saveCandidateFromResume(job.id);
    dispatch(setLoader(false));
    if (success) {
      close();
      onSaveCandidate && onSaveCandidate(response.data);
    } else {
      flashMessage(response.message, "error");
    }
  };

  return (
    <>
      <div>
        <p>
          Are you sure you want to save this candidate? If a different email
          address is found in the resume, it will be replaced with the one you
          provided during the request form submission
        </p>
      </div>

      <div className="ant-modal-footer">
        <Button className={`btn btn-theme mr-1`} onClick={saveCandidateModal}>
          Save Candidate
        </Button>

        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default SaveCandidateRequestModal;
