import { useEffect, useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { BusinessInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { adminBusinessApi } from "@src/apis/adminApis";
import flashMessage from "../FlashMessage";
import { Timezones } from "@src/helper/selectOptions";

// Fields for the edit business form

// Default values for the edit business form
const defaultValue: KeyPairInterface = {
  name: "",
  website: "",
  email: "",
  timezone: "UTC",
};

// Props for the EditBusinessModal component
type EditBusinessModalProps = {
  readonly business: BusinessInterface;
  onUpdate: (business: BusinessInterface) => void;
  close: Function;
};

// Component for the modal to create a edit business
const EditBusinessModal = ({
  business,
  close,
  onUpdate,
}: EditBusinessModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);
  const [timezoneOptions, setTimezoneOptions] = useState<
    {
      label: string;
      value: string;
    }[]
  >(Timezones);

  useEffect(() => {
    if (business) {
      setState((prevState) => ({
        ...prevState,
        name: business.name,
        website: business.website,
        email: business.email,
        contact_number: business.contact_number,
        timezone: business.timezone,
      }));
    }
  }, [business]);
  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a edit business
    const { success, ...response } = await adminBusinessApi.adminUpdateBusiness(
      business.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If business creation is successful, call the callback function to update the UI
    if (success) {
      close();
      onUpdate(response.data);
    }
  };

  function filterTimezonez(inputValue: string) {
    // Filter timezones matching the input search
    const filtered = Timezones.filter((tz) =>
      tz.label.toLowerCase().includes(inputValue.toLowerCase()),
    );

    setTimezoneOptions(filtered);
  }

  const verified = business.email_verified;

  const EditBusinessFields: GlobalInputFieldType[] = [
    {
      name: "name",
      label: "Business Name",
      type: "text",
      dataType: "Alphanumeric",
      maxLength: 80,
      required: true,
    },
    {
      name: "contact_number",
      label: "Contact Number",
      type: "mobilenumber",
      dataType: "mobilenumber",
      maxLength: 15,
      required: true,
    },
    {
      name: "website",
      label: "Website",
      type: "text",
      dataType: "websiteDomain",
      maxLength: 80,
      required: !verified,
      disabled: verified,
      tooltipTitle:
        "This field only accepts domain names, including optional subdomains. Ensure the domain does not include paths or ports. Eg example.com, https://example.com, sub.example.com",
    },
    {
      name: "email",
      label: "Business Email",
      type: "email",
      dataType: "email",
      maxLength: 80,
      required: !verified,
      disabled: verified,
      tooltipTitle: verified
        ? undefined
        : "Email should contain website domain",
    },
    {
      name: "timezone",
      label: "Business Timezone",
      type: "select",
      dataType: "select",
      placeholder: "Select Timezone",
      required: true,
      tooltipTitle: "Business Timezone",
      showSearch: true,
      options: timezoneOptions,
      onSearch: filterTimezonez,
    },
  ];

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Save" // Title for the submit button
        fields={EditBusinessFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditBusinessModal;
