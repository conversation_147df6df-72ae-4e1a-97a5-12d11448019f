import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { EmployeeInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";
import { useFetchEmployeeRoles } from "./Hooks";

// Fields for the new employee form
const DefaultEmployeeFields: GlobalInputFieldType[] = [
  {
    name: "first_name",
    label: "First Name",
    type: "text",
    dataType: "alphabetics",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "last_name",
    label: "Last Name",
    type: "text",
    dataType: "alphabetics",
    maxLength: 80,
    required: false,
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
    disabled: true,
  },
  {
    name: "contact_number",
    label: "Contact Number",
    type: "mobilenumber",
    dataType: "mobilenumber",
    maxLength: 15,
  },
];

// Props for the EditEmployeeModal component
type EditEmployeeModalProps = {
  onEmployeeUpdate: (employee: EmployeeInterface) => void;
  close: () => void;
  employee: EmployeeInterface;
};

// Component for the modal to edit a employee
const EditEmployeeModal = ({
  employee,
  close,
  onEmployeeUpdate,
}: EditEmployeeModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>({
    first_name: employee.first_name,
    last_name: employee.last_name,
    email: employee.email,
    contact_number: employee.contact_number,
    employee_role_id: employee.employee_role_id,
  });

  // Fields for the new employee form
  const EditEmployeeFields = useFetchEmployeeRoles(
    DefaultEmployeeFields,
    close,
  );

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to update employee
    const { success, ...response } = await employeeManagementApi.updateEmployee(
      employee.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If employee update is successful, close the modal and pass the updated employee data to the parent component
    if (success) {
      close();
      onEmployeeUpdate(response.data);
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Update" // Title for the submit button
        fields={EditEmployeeFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditEmployeeModal;
