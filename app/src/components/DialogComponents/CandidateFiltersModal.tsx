import React, { useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { Tabs } from "antd";
import { CandidateFilter, CandidateFilterHistory } from "../WildCard";

type CandidateFiltersModalProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

const CandidateFiltersModal = (props: CandidateFiltersModalProps) => {
  const [filters, setFilters] = useState<KeyPairInterface>(props.filters);

  const handleConfirm = (newFilters: KeyPairInterface) => {
    let filters = {
      opportunity_id: newFilters.opportunity_id,
      qualification_id: newFilters.qualification_id,
      status_id: newFilters.status,
      experience: newFilters.experience,
      resume_upload_after: newFilters.resume_uploaded_after,
    };
    setFilters(filters);
    props.onConfirm(filters);
  };

  return (
    <Tabs
      defaultActiveKey="Filter"
      animated={{ tabPane: true, inkBar: true }}
      items={[
        {
          key: "Filter",
          label: `New Search`,
          children: <CandidateFilter {...props} filters={filters} />,
        },
        {
          key: "History",
          label: `Search History`,
          children: (
            <CandidateFilterHistory {...props} onConfirm={handleConfirm} />
          ),
        },
      ]}
    />
  );
};

export default CandidateFiltersModal;
