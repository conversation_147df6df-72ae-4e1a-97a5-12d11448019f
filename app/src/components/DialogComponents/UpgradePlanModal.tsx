import React, { useCallback, useEffect, useState } from "react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import { BusinessInterface } from "@src/redux/interfaces";
import { UpgradePlanForm } from "../Payment";
import { STRIPE_PUBLISHABLE_KEY } from "@src/constants";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";
import { Button } from "antd";
import { wildCardPaymentApi } from "@src/apis/wildcardApis";

const stripePromise: Promise<Stripe | null> = loadStripe(
  STRIPE_PUBLISHABLE_KEY!,
);

interface UpgradePlanModalProps {
  close: () => void;
  onPayment: () => void;
  readonly business: BusinessInterface;
  readonly payment_plan: any;
  readonly selected_plan?: any;
}

// Confirmation modal component
const UpgradePlanModal = (props: UpgradePlanModalProps) => {
  // Function to handle confirmation button click
  const dispatch = useAppDispatch();
  const { payment_plan, selected_plan, close } = props;
  const [selectedPlan, setSelectedPlan] = useState<any>(selected_plan || null);
  const [planList, setPlanList] = useState<Array<any>>([]);

  const fetchAndShowPlans = useCallback(
    async () => {
      dispatch(setLoader(true));
      const { success, ...response } = await wildCardPaymentApi.getPlans();
      if (success) {
        setPlanList(response.data);
      } else {
        flashMessage(response.message, "error");
        // close()
      }
      dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch],
  );

  useEffect(() => {
    fetchAndShowPlans();
  }, [payment_plan, selected_plan, fetchAndShowPlans]);

  const setPlan = (plan: any) => {
    setSelectedPlan(plan);
  };

  if (selectedPlan) {
    return (
      <>
        <Elements stripe={stripePromise}>
          <UpgradePlanForm
            {...props}
            selectedPlan={selectedPlan}
            buttonTitle={selected_plan ? "Renew" : "Upgrade"}
          />
        </Elements>
      </>
    );
  }

  return (
    <>
      <div className="subscription-card-modal plan-container ">
        <div className="">
          <strong>Note:</strong> After upgrading the plan, existing credits will
          be added to the new plan.
        </div>

        <h4 className="mb-2">Choose Your Payment Plan</h4>
        {planList.length ? (
          <div className="grid-container">
            {planList.map((plan: any, index: number) => (
              <div key={index} className="subscription-card">
                <h6>{plan.product_name}</h6>
                <p className="subscription-plan-price">
                  {plan.amount} {plan.currency.toUpperCase()}
                </p>
                <p className="subscription-plan-description">
                  {plan.description}
                </p>
                <Button
                  onClick={() =>
                    payment_plan?.id == plan.id ? {} : setPlan(plan)
                  }
                  disabled={
                    payment_plan
                      ? payment_plan?.id == plan.id ||
                        plan.amount <= payment_plan.amount
                      : false
                  }
                  className={"subscription-button"}>
                  {payment_plan
                    ? payment_plan.id == plan.id
                      ? "Active"
                      : plan.amount <= payment_plan?.amount
                        ? "Can't Upgrade"
                        : "Upgrade"
                    : "Upgrade"}
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="subscription-card">
            <h6 className="mb-2">Fetching Plan Lists</h6>
          </div>
        )}
      </div>
      <div className="ant-modal-footer">
        {/* Cancel button to close the modal */}
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default UpgradePlanModal;
