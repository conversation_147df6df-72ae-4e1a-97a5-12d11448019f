import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { EmployeeInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { employeeManagementApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";
import { useFetchEmployeeRoles } from "./Hooks";

const DefaultEmployeeFields: GlobalInputFieldType[] = [
  {
    name: "first_name",
    label: "First Name",
    type: "text",
    dataType: "alphabetics",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "last_name",
    label: "Last Name",
    type: "text",
    dataType: "alphabetics",
    maxLength: 80,
    required: false,
  },
  {
    name: "email",
    label: "Email",
    type: "email",
    dataType: "email",
    maxLength: 80,
    required: true,
  },
  {
    name: "contact_number",
    label: "Contact Number",
    type: "mobilenumber",
    dataType: "mobilenumber",
    maxLength: 15,
  },
];
// Default values for the new employee form
const defaultValue: KeyPairInterface = {
  first_name: "",
  last_name: "",
  email: "",
};

// Props for the NewEmployeeModal component
type NewEmployeeModalProps = {
  onEmployeeCreate: (employee: EmployeeInterface) => void;
  close: () => void;
};

// Component for the modal to create a new employee
const NewEmployeeModal = ({
  close,
  onEmployeeCreate,
}: NewEmployeeModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Fields for the new employee form
  const NewEmployeeFields = useFetchEmployeeRoles(DefaultEmployeeFields, close);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new employee
    const { success, ...response } =
      await employeeManagementApi.createEmployee(state);
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If employee creation is successful, call the callback function to update the UI
    if (success) {
      onEmployeeCreate(response.data);
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={NewEmployeeFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewEmployeeModal;
