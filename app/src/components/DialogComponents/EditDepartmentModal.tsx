import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { DepartmentInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { departmentApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new department form
const EditDepartmentFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Department Name",
    type: "text",
    dataType: "nameWithHyphenAndDot",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "description",
    label: "Department Description",
    type: "textarea",
    dataType: "text",
    maxLength: 255,
    required: false,
  },
];

// Props for the EditDepartmentModal component
type EditDepartmentModalProps = {
  onDepartmentUpdate: (department: DepartmentInterface) => void;
  close: Function;
  department: DepartmentInterface;
};

// Component for the modal to edit a department
const EditDepartmentModal = ({
  department,
  close,
  onDepartmentUpdate,
}: EditDepartmentModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>({
    name: department.name,
    description: department.description,
  });

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to update department
    const { success, ...response } = await departmentApi.updateDepartment(
      department.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If department update is successful, close the modal and pass the updated department data to the parent component
    if (success) {
      close();
      onDepartmentUpdate(response.data);
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Update" // Title for the submit button
        fields={EditDepartmentFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditDepartmentModal;
