import React, { useState } from "react";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { <PERSON><PERSON>, Switch } from "antd";
import { resumeExtractionApi } from "@src/apis/wildcardApis";
import flashMessage from "@src/components/FlashMessage";
import { DuplicateCandidateInterface } from "@src/redux/interfaces";
import { APP_ROUTE } from "@src/constants";
import { encrypt } from "@src/helper/encryption";

// Props for the SaveDuplicateResume component
type SaveDuplicateResumeProps = {
  readonly records: DuplicateCandidateInterface[];
  onCandidateCreate: (candidate: any) => void;
  jobId: number | null;
  close: Function;
};

// Component for the modal to add a new candidate
export const SaveDuplicateResume = ({
  records,
  close,
  jobId,
  onCandidateCreate,
}: SaveDuplicateResumeProps) => {
  const dispatch = useAppDispatch();
  const [ids, setIds] = useState<number[]>([]);

  const onChange = (field: any) => {
    const hasId = ids.includes(field.id);
    if (hasId) {
      setIds((prevState) => prevState.filter((id) => id !== field.id));
    } else {
      setIds((prevState) => [...prevState, field.id]);
    }
  };

  // Function to upload the resume file and extract details
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await resumeExtractionApi.saveDuplicateRecord({
        ids: ids,
        opportunity_id: jobId,
        // resume_source: "resume_source",
      });
    await dispatch(setLoader(false));

    if (success) {
      close();
      onCandidateCreate(response.data);
      // console.log("################ response :", response.data.resume_source);
    } else {
      flashMessage(response.message, "error");
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <p className="mb-3">
        We have found a candidate that already exists in the database. Do you
        want to update their details with the latest information? If so, please
        mark the update fields and save the changes.
      </p>

      <div className="table-responsive">
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-100px">Email</th>
              <th className="mw-80px">Candidate</th>
              <th>Update</th>
            </tr>
          </thead>
          <tbody>
            {records.map(
              (field: DuplicateCandidateInterface, index: number) => (
                <tr key={index}>
                  <th>{field.email ?? ""}</th>
                  <td>
                    <a
                      href={`${APP_ROUTE.CANDIDATE_MANAGEMENT}/${encrypt(`${field.candidate_id}`)}`}
                      target="_blank"
                      className="no-decoration">
                      View Candidate
                    </a>
                  </td>
                  <td>
                    <Switch
                      value={ids.includes(field.id)}
                      className="switch-theme"
                      onChange={() => onChange(field)}
                    />
                  </td>
                </tr>
              ),
            )}
          </tbody>
        </table>
      </div>

      <div className="ant-modal-footer mt-2 mb-1">
        <Button
          key="upload"
          className="btn btn-theme font-14 mr-1"
          onClick={onSubmit}>
          Save
        </Button>
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};
