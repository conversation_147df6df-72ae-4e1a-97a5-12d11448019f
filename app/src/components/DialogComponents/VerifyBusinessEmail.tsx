import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { BusinessInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { adminBusinessApi } from "@src/apis/adminApis";
import flashMessage from "../FlashMessage";

// Fields for the form to verify business email
const NewBusinessFields: GlobalInputFieldType[] = [
  {
    name: "otp",
    label: "OTP",
    type: "onlynumber",
    dataType: "onlynumber",
    strict: true,
    minLength: 6,
    maxLength: 6,
    required: true,
  },
];

// Props for the VerifyBusinessEmail component
type VerifyBusinessEmailProps = {
  close: Function; // Function to close the modal
  business: BusinessInterface; // Business object to verify email for
  onOtpVerified: Function; // Function to call after OTP verification
};

// Initial default values for the form fields
const defaultValue: KeyPairInterface = {
  otp: "",
};

// Component for verifying business email with OTP
const VerifyBusinessEmail = ({
  close,
  business,
  onOtpVerified,
}: VerifyBusinessEmailProps) => {
  const dispatch = useAppDispatch();

  // State to manage form field values
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await adminBusinessApi.adminVerifyBusinessEmail(business.id, state);
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");
    if (success) {
      await onOtpVerified(business);
      await close();
    }
  };

  // Function to handle resend OTP request
  const onResendOtp = async () => {
    setState((prev) => ({ ...prev, otp: "" }));
    await dispatch(setLoader(true));
    const { success, ...response } =
      await adminBusinessApi.adminResendBusinessEmail(business.id);
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");
    return success;
  };

  return (
    <>
      {/* Modal form input component */}
      <ModalFormInput
        buttonTitle="Verify"
        fields={NewBusinessFields}
        setState={setState}
        state={state}
        onSubmit={onSubmit}
        onResend={onResendOtp}
        onClose={close}
      />
    </>
  );
};

export default VerifyBusinessEmail;
