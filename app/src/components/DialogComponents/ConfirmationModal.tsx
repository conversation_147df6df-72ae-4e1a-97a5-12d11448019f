import React from "react";
import { Button } from "antd";

type ConfirmationModalProps = {
  message: string | React.ReactNode; // Message to display in the confirmation modal
  onConfirm?: Function; // Function to call when the confirmation button is clicked
  buttonTitle?: string; // Title for the confirmation button
  close: Function; // Function to close the modal
  closable: boolean;
};

// Confirmation modal component
const ConfirmationModal = ({
  message,
  close,
  onConfirm,
  buttonTitle,
  closable,
}: ConfirmationModalProps) => {
  // Function to handle confirmation button click
  const onConfirmButton = () => {
    close(); // Close the modal
    onConfirm && onConfirm(); // Call the onConfirm function if provided
  };

  return (
    <>
      {/* Display the message */}
      {message ?? ""}

      {/* Modal footer */}
      {closable || onConfirm ? (
        <div className="ant-modal-footer">
          {/* Render the confirmation button if onConfirm function is provided */}
          {onConfirm && (
            <Button
              className="btn btn-theme mr-1"
              onClick={() => onConfirmButton()}>
              {/* Use custom button title if provided, otherwise default to "Confirm" */}
              {buttonTitle ?? "Confirm"}
            </Button>
          )}

          {/* Cancel button to close the modal */}
          {closable && (
            <Button className="btn" key="cancel" onClick={() => close()}>
              Cancel
            </Button>
          )}
        </div>
      ) : (
        <></>
      )}
    </>
  );
};

export default ConfirmationModal;
