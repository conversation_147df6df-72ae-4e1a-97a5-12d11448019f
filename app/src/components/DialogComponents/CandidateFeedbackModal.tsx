import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { InterviewInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import flashMessage from "../FlashMessage";
import { RatingOptions } from "@src/helper/selectOptions";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import { ModalFeedbackForm } from "../CandidatePortal/CandidateFeedback";

// Fields for the new business form

const CandidateFeebackFields: GlobalInputFieldType[] = [
  {
    name: "rating",
    label: "Rate Your Interview Experience",
    type: "select",
    dataType: "select",
    options: RatingOptions,
    placeholder: "Select Rating",
    required: true,
    groupName: "Feedback Detail",
    groupPosition: 1,
    fieldPosition: 1,
    className: "col-sm-12 col-md-12",
  },

  {
    name: "suggestions",
    label: "Any Suggestions",
    type: "textarea",
    dataType: "text",
    placeholder: "Add Suggestions",
    maxLength: 500,
    required: true,
    groupName: "Feedback Detail",
    groupPosition: 1,
    fieldPosition: 3,
    className: "col-sm-12",
    rows: 4,
  },
];

// Default values for the new business form
const defaultValue: KeyPairInterface = {
  rating: "",
  feedback: "",
};

// Props for the CandidateFeebackModal component
type CandidateFeebackModalProps = {
  interview: InterviewInterface;
  close: Function;
  onSubmitFeedback: () => void;
};

// Component for the modal to create a new business
const CandidateFeedbackModal = ({
  interview,
  close,
  onSubmitFeedback,
}: CandidateFeebackModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));
    // Call API to create a new business
    const { success, ...response } = await interviewsApi.saveFeedback(
      interview.id,
      state,
    );
    await dispatch(setLoader(false));
    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");
    // If business creation is successful, call the callback function to update the UI
    if (success) {
      onSubmitFeedback();
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <div className="text-center mt-5 d-inline-block">
        <h5 className="ant-modal-title">Give Feedback & Review</h5>
        <p style={{ fontSize: "14px", color: "#262626" }}>
          You’ve successfully completed our online assessment test! We’re
          reviewing your responses. Meanwhile, take a moment to share your
          interview experience with us!
        </p>
        <ModalFeedbackForm
          buttonTitle="Submit" // Title for the submit button
          fields={CandidateFeebackFields} // Fields for the form
          setState={setState} // Function to update form state
          state={state} // Current form state
          onSubmit={onSubmit} // Function to handle form submission
          onClose={close} // Function to close the modal
        />
      </div>
    </>
  );
};

export default CandidateFeedbackModal;
