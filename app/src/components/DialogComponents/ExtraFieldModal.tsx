import { useState } from "react";
import {
  DefaultExtraField,
  type GlobalInputFieldType,
} from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface } from "@src/redux/interfaces";
import flashMessage from "../FlashMessage";
import DeleteIcon from "@mui/icons-material/Delete";

// Fields for the new Field form
const ExtraFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Field Name",
    type: "text",
    dataType: "camelizealphanumeric",
    maxLength: 80,
    required: true,
    tooltipTitle:
      "Each word must start with an uppercase letter and can contain letters or numbers. Only single spaces are allowed. No special characters or leading numbers",
  },
  {
    name: "value",
    label: "Field Value",
    type: "text",
    dataType: "text",
    maxLength: 80,
    required: false,
  },
];

// Default values for the new Field form
const defaultValue: KeyPairInterface = {
  name: "",
  value: "",
};

// Props for the NewFieldModal component
type NewFieldModalProps = {
  fields: GlobalInputFieldType[];
  onFieldCreate: (Field: GlobalInputFieldType, value: string) => void;
  close: Function;
  deleteExtraField: (name: string) => void;
};

// Component for the modal to create a new Field
const NewFieldModal = ({
  fields,
  close,
  onFieldCreate,
  deleteExtraField,
}: NewFieldModalProps) => {
  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  const formatFieldName = (name: string) =>
    name.toLowerCase().replace(/\s+/g, "_");

  // Function to handle form submission
  const onSubmit = async () => {
    if (state.name.trim() == "") {
      flashMessage("Please add valid name", "error");
      return;
    }
    let hasError: boolean = fields.some(
      (field) =>
        field.label.toLowerCase().trim() === state.name.toLowerCase().trim(),
    );

    if (hasError) {
      flashMessage("Field Already exist", "error");
    } else {
      onFieldCreate(
        {
          ...DefaultExtraField,
          groupPosition: 4,
          fieldPosition: 1,
          label: state.name.trim(),
          placeholder: `Enter ${state.name.trim()}`,
          name: `extra_fields.${formatFieldName(state.name)}`,
          customLabelButton: (
            <DeleteIcon
              className="delete-icon cursor-pointer ml-1"
              fontSize="large"
              onClick={() =>
                deleteExtraField(`extra_fields.${formatFieldName(state.name)}`)
              }
            />
          ),
        },
        state.value,
      );
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={ExtraFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewFieldModal;
