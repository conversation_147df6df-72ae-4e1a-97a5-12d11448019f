import React, { useEffect, useState } from "react";
import { KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { getAllOpportunityOptions } from "@src/redux/actions";
import { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";

type InterviewFiltersModalProps = {
  filters: KeyPairInterface;
  onConfirm: (state: KeyPairInterface) => void;
  close: Function;
};

const InterviewFiltersModal = ({
  filters,
  onConfirm,
  close,
}: InterviewFiltersModalProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>(filters);
  const [loading, setLoading] = useState<boolean>(false);
  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );

  useEffect(() => {
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  const fetchOpportunitiesOptions = async (
    search: string,
    showId: number | undefined = undefined,
  ) => {
    showId = search == "" ? filters.opportunity_id : showId;
    await setLoading(true);
    await dispatch(getAllOpportunityOptions({ search, showId }));
    await setLoading(false);
  };

  const FilterFields: GlobalInputFieldType[] = [
    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "date_filter",
      label: "Schedule Filter",
      placeholder: "Select Schedule Filter",
      options: [
        { label: "All", value: "" },
        { label: "Today", value: "today" },
        { label: "Tomorrow", value: "tomorrow" },
        { label: "This Week", value: "this_week" },
        { label: "Next Week", value: "next_week" },
        { label: "This Month", value: "this_month" },
        { label: "Next Month", value: "next_month" },
        { label: "Past", value: "past" },
        { label: "Upcoming", value: "upcoming" },
      ],

      type: "select",
      dataType: "select",
      loading: loading,
      selectEmpty: true,
    },
    {
      name: "interview_status",
      label: "Interview Status",
      placeholder: "Select Interview Status",
      options: [
        { label: "All", value: "" },
        { label: "Cancelled", value: "0" },
        { label: "Scheduled", value: "1" },
        { label: "Rejected", value: "2" },
        { label: "Finalized", value: "3" },
      ],
      type: "select",
      dataType: "select",
      loading: loading,
      selectEmpty: true,
    },
  ];

  const onSubmit = (state: KeyPairInterface) => {
    onConfirm(state);
    close();
  };

  return (
    <ModalFormInput
      state={state}
      setState={setState}
      fields={FilterFields}
      onSubmit={() => onSubmit(state)}
      onClose={() => onSubmit({})}
      buttonTitle="Apply Filters"
      cancelButtonTitle="Clear Filters"
    />
  );
};

export default InterviewFiltersModal;
