import React, { useEffect, useRef, useState } from "react";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateInterface,
  CandidateInterviewInterface,
  EmailSubjectContentInterface,
  EmailTemplateInterface,
  KeyPairInterface,
  OpportunityInterface,
} from "@src/redux/interfaces";
import { Button } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { getAllEmailTemplateOptions, setLoader } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import {
  emailTemplateApi,
  candidateInterviewApi,
  candidateApi,
} from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";
import { CustomEditor } from "../CustomEditor";
import SignatureCanvas from "react-signature-canvas";
import HtmlPreviewIframe from "../Common/HtmlPreview";
import { CandidateCardWithLinks } from "../WildCard";

const defaultValue: EmailSubjectContentInterface = {
  subject: "",
  content: "",
};

type SendOfferLetterModalProps = {
  close: Function;
  readonly interview: CandidateInterviewInterface;
  onSubmit: (interview: CandidateInterviewInterface) => void;
};

// Component for the modal to create a new email
const SendOfferLetterModal = ({
  close,
  interview,
  onSubmit,
}: SendOfferLetterModalProps) => {
  const dispatch = useAppDispatch();
  // State to manage form data
  const [loading, setLoading] = useState<boolean>(false);
  const [previewContent, setPreviewContent] = useState<boolean>(false);
  const [state, setState] = useState<KeyPairInterface>(defaultValue);
  const [errors, setErrors] = useState<KeyPairInterface>({ content: "" });
  const [disabled, setDisabled] = useState<boolean>(false);
  const [opportunity, setOpportunity] = useState<OpportunityInterface | null>(
    null,
  );

  const [candidate, setCandidate] = useState<CandidateInterface | null>(null);
  const [emailTemplate, setEmailTemplate] =
    useState<EmailTemplateInterface | null>(null);
  const sigCanvas: any = useRef(null);

  const templateOptions = useSelector(
    (state: RootState) => state.emailTemplate.options,
  );
  const currentEmployee = useSelector(
    (state: RootState) => state.auth.employee,
  );

  const fetchAndSetInterviewDetail = async (id: number) => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await candidateInterviewApi.getInterviewCandidateDetail(id);
    if (!success) {
      flashMessage(response.message, "error");
      close();
    }

    const { interview, candidate, opportunity } = response.data;

    if (interview.status_name == "Finalize") {
      setCandidate(candidate);
      setOpportunity(opportunity);
      setState((prev) => ({
        ...prev,
        job: opportunity.title,
        subject: `${currentEmployee?.business_name ?? "Recruitease Pro "} - Offer Letter for ${opportunity.title}`,
      }));
    } else {
      close();
    }
    await dispatch(setLoader(false));
  };

  const fetchAndSetTemplate = async (template_id: number) => {
    await setDisabled(true);
    await dispatch(setLoader(true));
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateDetail(template_id);
    if (success) {
      setEmailTemplate(response.data);
    } else {
      setEmailTemplate(null);
      flashMessage(response.message, "error");
    }
    await setDisabled(false);
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    fetchAndSetInterviewDetail(interview.id);
    /* eslint-disable react-hooks/exhaustive-deps */
  }, []);

  useEffect(() => {
    if (state.template_id) {
      fetchAndSetTemplate(state.template_id);
    } else {
      setState((prev) => ({ ...prev, content: "" }));
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [state.template_id]);

  useEffect(() => {
    if (opportunity && emailTemplate && candidate) {
      buildTemplateLocally(opportunity, emailTemplate, candidate);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [opportunity?.id, emailTemplate?.id]);

  const fetchEmailTemplateOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllEmailTemplateOptions({ search, type_id: 1 }));
    await setLoading(false);
  };

  const validateForm = () => {
    let valid = true;
    let newErrors: KeyPairInterface = {};

    const plainText = state?.content
      ? state?.content
          ?.replace(/<[^>]+>/g, "")
          ?.replace(/&nbsp;/g, " ")
          ?.trim()
      : "";

    if (!plainText || plainText.length < 200) {
      newErrors.content =
        "Offer letter content must be at least 200 characters.";
      valid = false;
    } else if (plainText.length > 5000) {
      newErrors.content =
        "Offer letter content must not exceed 5000 characters.";
      valid = false;
    }
    setErrors(newErrors);
    return valid;
  };

  // Function to handle form submission
  const handleSubmit = async () => {
    if (validateForm() && opportunity && candidate) {
      // const file = dataURLtoBlob(formData?.signature);
      const body = {
        content: state.content,
        subject: state.subject,
        opportunity_id: opportunity.id,
      };

      await dispatch(setLoader(true));
      await setDisabled(true);
      const { success, ...response } = await candidateApi.sendOfferLetter(
        candidate.id,
        body,
      );
      await dispatch(setLoader(false));
      flashMessage(response.message, success ? "success" : "error");
      if (success) {
        await close();
        await onSubmit({ ...interview, offer_sent: true });
      }
    }
  };

  const buildTemplateLocally = (
    job: OpportunityInterface,
    emailTemplate: EmailTemplateInterface,
    candidate: CandidateInterface,
  ) => {
    let content = emailTemplate.email_body;
    const currentYear = new Date().getFullYear();

    // Create a replacements object
    const replacements: { [key: string]: string } = {};

    // Map job and candidate properties to placeholders
    replacements["%candidate_name%"] = candidate.name || "N/A"; // Adjust based on actual candidate property
    replacements["%job_title%"] = job.title || "N/A"; // Adjust based on actual job property
    replacements["%job_description%"] = job.description || "N/A"; // Adjust based on actual job property
    replacements["%job_responsibilities%"] = job.responsibilities || "N/A"; // Adjust based on actual job property
    replacements["%job_salary%"] = (job.salary || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_experience%"] = (job.experience || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_questions%"] = (job.questions || "N/A").toString(); // Adjust based on actual job property
    replacements["%business_name%"] = (
      currentEmployee?.business_name || "N/A"
    ).toString(); // Adjust based on actual job property
    replacements["%current_date%"] = new Date().toISOString().split("T")[0];
    replacements["%employee_name%"] =
      currentEmployee?.first_name ?? "" + currentEmployee?.last_name ?? "";
    replacements["%employee_job_title%"] = currentEmployee?.employee_role ?? "";
    replacements["%employee_contact%"] =
      currentEmployee?.contact ?? currentEmployee?.business_contact ?? "  ";
    replacements["%current_year%"] = currentYear.toString();

    // Replace placeholders in content
    const regex = /%[a-zA-Z0-9_]+%/g;
    const replacedContent = content.replace(
      regex,
      (match) => replacements[match] || match,
    );

    setState((prev: any) => ({ ...prev, content: replacedContent }));
  };

  const handleSignatureClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
    }
    setState((prevState: any) => ({
      ...prevState,
      content: prevState?.content?.replace(/<img[^>]*>/g, ""),
    }));
  };

  const handleSignatureSave = () => {
    if (sigCanvas.current.isEmpty()) {
      return;
    }

    const originalCanvas = sigCanvas.current.getCanvas();
    const scaleFactor = 0.3; // Scale down to 30%

    const resizedWidth = originalCanvas.width * scaleFactor;
    const resizedHeight = originalCanvas.height * scaleFactor;

    // Create off-screen canvas
    const resizedCanvas = document.createElement("canvas");
    resizedCanvas.width = resizedWidth;
    resizedCanvas.height = resizedHeight;

    const ctx = resizedCanvas.getContext("2d");
    if (ctx) {
      ctx.scale(scaleFactor, scaleFactor);
      ctx.drawImage(originalCanvas, 0, 0);
    }

    const data = resizedCanvas.toDataURL("image/png"); // smaller image now

    setState((prevState: any) => {
      // Remove any existing signature image
      let newContent = prevState.content
        .replace(/<div class="signature-wrapper">[\s\S]*?<\/div>/g, "")
        .trim();
      // Append the new resized signature
      newContent += `<div class="signature-wrapper"><img src="${data}" class="signature-img"/></div>`;
      return {
        ...prevState,
        signature: data,
        content: newContent,
      };
    });
  };

  const EmailFormFields = [
    {
      name: "job",
      label: "Job Title",
      placeholder: "Job Title",
      type: "text",
      dataType: "text",
      disabled: true,
    },
    {
      name: "template_id",
      label: "Email Template",
      placeholder: "Select Template",
      options: templateOptions,
      onSearch: fetchEmailTemplateOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
      required: true,
    },
    {
      name: "subject",
      label: "Email Subject",
      placeholder: "Enter Email Subject",
      type: "text",
      dataType: "text",
      maxLength: 1000,
      required: true,
      disabled: disabled,
    },
  ];

  if (previewContent) {
    return (
      <>
        <div className="d-flex pb-2 pt-3 border-top p-4">
          <h6>
            <strong>Subject:</strong> {state.subject}
          </h6>
        </div>
        <div className="editor-preview">
          <HtmlPreviewIframe
            htmlContent={state.content}
            height={"calc(100vh)"}
          />
        </div>

        <div className="ant-modal-footer mt-4">
          <Button
            className={`btn btn-theme mr-1`}
            onClick={() => setPreviewContent(false)}>
            Edit Offer Letter
          </Button>

          <Button className={`btn btn-theme mr-1`} onClick={handleSubmit}>
            Send Offer Letter
          </Button>

          <Button key="cancel" onClick={() => close()}>
            Cancel
          </Button>
        </div>
      </>
    );
  }
  return (
    <>
      {/* Render the modal form input component */}
      <div className="row">
        <div className="col-md-8 col-sm-12">
          <div className="card card-border  mb-4">
            <div className="card-body">
              <CustomEditor
                value={state.content}
                onChange={(content) => {
                  setState((prev: any) => ({ ...prev, content }));
                }}
              />
              {errors?.content && (
                <div className="text-danger mt-2">{errors?.content}</div>
              )}
            </div>
          </div>
          <div className="card card-border mb-0">
            <div className="card-body">
              <div>
                <label className="offer-letter-form-label">Signature</label>
              </div>
              <div className="signature-box">
                <div className="signature-box-inner">
                  <SignatureCanvas
                    penColor="green"
                    ref={sigCanvas}
                    canvasProps={{
                      width: 900,
                      height: 170,
                      className: "sigCanvas",
                    }}
                  />
                </div>
                <div className="signature-box-btn">
                  <button
                    className="btn btn-primary"
                    onClick={handleSignatureClear}>
                    Clear
                  </button>
                  <button
                    className="btn btn-primary"
                    onClick={handleSignatureSave}>
                    Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-4 col-sm-12">
          <div className="card card-border">
            <div className="card-body send-offer">
              <h6 className="mb-0">Send Offer Letter</h6>
              <div className="p-3">
                <ModalFormInput
                  buttonTitle="Send Offer Letter" // Title for the submit button
                  fields={EmailFormFields} // Fields for the form
                  setState={setState} // Function to update form state
                  state={state} // Current form state
                  onSubmit={handleSubmit} // Function to handle form submission
                  onClose={close} // Function to close the modal
                  submitClass={"ml-1"}
                  customButtons={
                    <>
                      {!state.subject ||
                      !state.content ||
                      state.subject?.trim() === "" ||
                      state.content?.trim() === "" ? (
                        <></>
                      ) : (
                        <Button
                          className={`btn btn-theme mr-1`}
                          onClick={() => setPreviewContent(true)}>
                          Preview
                        </Button>
                      )}
                    </>
                  }
                />
              </div>
            </div>
          </div>

          <CandidateCardWithLinks candidate={candidate as CandidateInterface} />
        </div>
      </div>
    </>
  );
};

export default SendOfferLetterModal;
