import type { GlobalReadOnlyFieldType } from "../input/GlobalInput";
import { DepartmentInterface } from "@src/redux/interfaces";
import { ReadOnlyField } from "../input/ReadOnlyField";

// Fields for the new department form
const ViewDepartmentFields: GlobalReadOnlyFieldType[] = [
  {
    name: "name",
    label: "Department Name",
    type: "text",
  },
  {
    name: "description",
    label: "Department Description",
    type: "textarea",
  },
];

// Props for the ViewDepartmentModal component
type ViewDepartmentModalProps = {
  department: DepartmentInterface;
};

// Component for the modal to edit a department
const ViewDepartmentModal = ({ department }: ViewDepartmentModalProps) => {
  return (
    <>
      {/* Render the modal form input component */}
      <ReadOnlyField
        fields={ViewDepartmentFields} // Fields for the form
        state={department} // Current form state
      />
    </>
  );
};

export default ViewDepartmentModal;
