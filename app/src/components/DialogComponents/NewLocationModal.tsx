import { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { LocationInterface, KeyPairInterface } from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { locationApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";

// Fields for the new category form
const NewLocationFields: GlobalInputFieldType[] = [
  {
    name: "address",
    label: "Address",
    type: "text",
    dataType: "alphabetics1",
    minLength: 3,
    maxLength: 80,
    required: true,
  },
  {
    name: "city",
    label: "City",
    type: "text",
    dataType: "alphabetics",
    maxLength: 100,
    required: true,
  },
  {
    name: "state",
    label: "State",
    type: "text",
    dataType: "alphabetics",
    maxLength: 100,
    required: true,
  },
  {
    name: "country",
    label: "Country",
    type: "text",
    dataType: "dotWithOneAlphanumeric",
    maxLength: 100,
    required: true,
  },
  {
    name: "pincode",
    label: "Pincode",
    type: "text",
    dataType: "pincode",
    maxLength: 10,
    required: true,
  },
];

// Default values for a new category
const defaultValue: KeyPairInterface = {
  address: "",
  city: "",
  state: "",
  country: "",
  pincode: "",
};

// Props for the NewCategoryModal component
type NewLocationModalProps = {
  onLocationCreate: (location: LocationInterface) => void;
  close: Function;
};

// Component for the modal to add a new category
const NewLocationModal = ({
  close,
  onLocationCreate,
}: NewLocationModalProps) => {
  const dispatch = useAppDispatch();

  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(defaultValue);

  // Function to handle form submission
  const onSubmit = async () => {
    await dispatch(setLoader(true));

    // Convert pincode to number
    const formattedState = {
      ...state,
    };

    // Call API to create location
    const { success, ...response } =
      await locationApi.createLocation(formattedState);
    await dispatch(setLoader(false));

    // Display flash message based on API response
    flashMessage(response.message, success ? "success" : "error");

    // If location creation is successful, pass the new location data to the parent component
    if (success) {
      onLocationCreate(response.data);
      close();
    }
  };

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Add" // Title for the submit button
        fields={NewLocationFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default NewLocationModal;
