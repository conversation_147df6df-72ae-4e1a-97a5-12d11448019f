import { useEffect, useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateNewSkillInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";

// Fields for the new skill form
const DefaultCandidateSkillFields: GlobalInputFieldType[] = [
  {
    name: "name",
    label: "Skill Name",
    type: "text",
    dataType: "string",
    minLength: 3,
    maxLength: 100,
    required: true,
    className: "col-sm-12",
    tooltipTitle: "Skill Name and please add single skill only",
  },
  {
    name: "skill_type",
    label: "Skill Type",
    type: "select",
    dataType: "select",
    placeholder: "Select Skill Type",
    required: true,
    selectEmpty: true,
    className: "col-sm-12",
    options: [
      { label: "Technical", value: "Technical" },
      { label: "Soft", value: "Soft" },
    ],
  },
];

// Props for the EditCandidateSkillModal component
type EditCandidateSkillModalProps = {
  onCandidateSkillUpdate: (skill: CandidateNewSkillInterface) => void;
  close: Function;
  skill: CandidateNewSkillInterface;
};

// Component for the modal to edit a skill
const EditCandidateSkillModal = ({
  skill,
  close,
  onCandidateSkillUpdate,
}: EditCandidateSkillModalProps) => {
  // State to manage form data
  const [state, setState] = useState<KeyPairInterface>(skill);
  const [prevSkillSubType, setPrevSkillSubType] = useState<string>(
    skill.skill_type == "Technical" ? skill.sub_skill_type ?? "" : "",
  );

  // Function to handle form submission
  const onSubmit = async () => {
    onCandidateSkillUpdate(state as CandidateNewSkillInterface);
    close();
  };

  useEffect(() => {
    if (state.skill_type == "Technical") {
      setState((prev) => ({ ...prev, sub_skill_type: prevSkillSubType }));
    } else {
      setState((prev) => ({ ...prev, sub_skill_type: "Soft Skill" }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.skill_type]);

  useEffect(() => {
    if (state.skill_type == "Technical") {
      setPrevSkillSubType(state.sub_skill_type);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.sub_skill_type]);

  let EditCandidateSkillFields = [...DefaultCandidateSkillFields];
  if (state.skill_type == "Technical") {
    EditCandidateSkillFields = [
      ...DefaultCandidateSkillFields,
      {
        name: "sub_skill_type",
        label: "Technial Skill Type",
        type: "text",
        dataType: "string",
        minLength: 3,
        maxLength: 100,
        required: true,
        className: "col-sm-12",
        tooltipTitle:
          "Technical Skill Type (Like Database, Cloud, Deployment etc)",
      },
    ];
  }

  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Save Detail" // Title for the submit button
        fields={EditCandidateSkillFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default EditCandidateSkillModal;
