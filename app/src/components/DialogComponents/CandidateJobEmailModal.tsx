import React, { useEffect, useState } from "react";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  CandidateInterface,
  EmailSubjectContentInterface,
  EmailTemplateInterface,
  KeyPairInterface,
  OpportunityInterface,
} from "@src/redux/interfaces";
import { Button } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import {
  getAllEmailTemplateOptions,
  getAllOpportunityOptions,
  setLoader,
} from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import { opportunityApi, emailTemplateApi } from "@src/apis/wildcardApis";
import flashMessage from "../FlashMessage";
import HtmlPreviewIframe from "../Common/HtmlPreview";
import { CustomEditor } from "../CustomEditor";
import { CandidateCardWithLinks } from "../WildCard";

const defaultValue: EmailSubjectContentInterface = {
  subject: "",
  content: "",
};

type CandidateJobEmailModalProps = {
  close: Function;
  readonly candidate: CandidateInterface;
  onSendEmail: (data: EmailSubjectContentInterface) => void;
};

// Component for the modal to create a new email
const CandidateJobEmailModal = ({
  close,
  candidate,
  onSendEmail,
}: CandidateJobEmailModalProps) => {
  const dispatch = useAppDispatch();
  // State to manage form data
  const [loading, setLoading] = useState<boolean>(false);
  const [previewContent, setPreviewContent] = useState<boolean>(false);
  const [state, setState] = useState<KeyPairInterface>(defaultValue);
  const [errors, setErrors] = useState<KeyPairInterface>({ content: "" });
  const [disabled, setDisabled] = useState<boolean>(false);

  const [opportunity, setOpportunity] = useState<OpportunityInterface | null>(
    null,
  );
  const [emailTemplate, setEmailTemplate] =
    useState<EmailTemplateInterface | null>(null);

  const opportunityOptions = useSelector(
    (state: RootState) => state.opportunity.options,
  );

  const templateOptions = useSelector(
    (state: RootState) => state.emailTemplate.options,
  );

  const fetchAndSetOpportunity = async (opportunity_id: number) => {
    await setDisabled(true);
    await dispatch(setLoader(true));
    const { success, ...response } =
      await opportunityApi.getOpportunityDetail(opportunity_id);
    if (success) {
      setOpportunity(response.data);
    } else {
      setOpportunity(null);
      flashMessage(response.message, "error");
    }
    await setDisabled(false);
    await dispatch(setLoader(false));
  };

  const fetchAndSetTemplate = async (template_id: number) => {
    await setDisabled(true);
    await dispatch(setLoader(true));
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateDetail(template_id);
    if (success) {
      setEmailTemplate(response.data);
    } else {
      setEmailTemplate(null);
      flashMessage(response.message, "error");
    }
    await setDisabled(false);
    await dispatch(setLoader(false));
  };

  useEffect(() => {
    if (state.opportunity_id) {
      fetchAndSetOpportunity(state.opportunity_id);
    } else {
      setState((prev) => ({ ...prev, content: "" }));
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [state.opportunity_id]);

  useEffect(() => {
    if (state.template_id) {
      fetchAndSetTemplate(state.template_id);
    } else {
      setState((prev) => ({ ...prev, content: "" }));
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [state.template_id]);

  useEffect(() => {
    if (opportunity && emailTemplate) {
      buildTemplateLocally(opportunity, emailTemplate, candidate);
    }
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [opportunity?.id, emailTemplate?.id]);

  const fetchOpportunitiesOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllOpportunityOptions({ search }));
    await setLoading(false);
  };

  const fetchEmailTemplateOptions = async (search: string) => {
    await setLoading(true);
    await dispatch(getAllEmailTemplateOptions({ search }));
    await setLoading(false);
  };

  const validateForm = () => {
    let valid = true;
    let newErrors: KeyPairInterface = {};

    const plainText = state?.content
      ? state?.content
          ?.replace(/<[^>]+>/g, "")
          ?.replace(/&nbsp;/g, " ")
          ?.trim()
      : "";

    if (!plainText || plainText.length < 200) {
      newErrors.content = "Email content must be at least 200 characters.";
      valid = false;
    } else if (plainText.length > 2000) {
      newErrors.content = "Email content must not exceed 2000 characters.";
      valid = false;
    }
    setErrors(newErrors);
    return valid;
  };

  // Function to handle form submission
  const onSubmit = async () => {
    if (validateForm()) {
      if (!state.opportunity_id || !state.template_id) {
        flashMessage("Please select a job and email template.", "error");
        return;
      }
      if (!state.subject || state.subject.trim() === "") {
        flashMessage("Please enter an email subject.", "error");
        return;
      }
      if (!state.content || state.content.trim() === "") {
        flashMessage("Please enter email content.", "error");
        return;
      }
      onSendEmail(state as EmailSubjectContentInterface);
    }
  };

  const handleSubmit = () => {
    onSendEmail(state as EmailSubjectContentInterface);
  };

  const buildTemplateLocally = (
    job: OpportunityInterface,
    emailTemplate: EmailTemplateInterface,
    candidate: CandidateInterface,
  ) => {
    let content = emailTemplate.email_body;

    // Create a replacements object
    const replacements: { [key: string]: string } = {};

    // Map job and candidate properties to placeholders
    replacements["%candidate_name%"] = candidate.name || "N/A"; // Adjust based on actual candidate property
    replacements["%job_title%"] = job.title || "N/A"; // Adjust based on actual job property
    replacements["%job_description%"] = job.description || "N/A"; // Adjust based on actual job property
    replacements["%job_responsibilities%"] = job.responsibilities || "N/A"; // Adjust based on actual job property
    replacements["%job_salary%"] = (job.salary || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_experience%"] = (job.experience || "N/A").toString(); // Adjust based on actual job property
    replacements["%job_questions%"] = (job.questions || "N/A").toString(); // Adjust based on actual job property

    // Replace placeholders in content
    const regex = /%[a-zA-Z0-9_]+%/g;
    const replacedContent = content.replace(
      regex,
      (match) => replacements[match] || match,
    );

    setState((prev) => ({
      ...prev,
      content: replacedContent,
      subject: job.title,
    }));
  };

  const EmailFormFields = [
    {
      name: "opportunity_id",
      label: "Job",
      placeholder: "Select Job",
      options: opportunityOptions,
      onSearch: fetchOpportunitiesOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
      required: true,
      className: "col-sm-12 col-md-6 col-lg-6",
    },
    {
      name: "template_id",
      label: "Email Template",
      placeholder: "Select Template",
      options: templateOptions,
      onSearch: fetchEmailTemplateOptions,

      type: "select",
      dataType: "select",
      showSearch: true,
      loading: loading,
      selectEmpty: true,
      required: true,
      className: "col-sm-12 col-md-6 col-lg-6",
    },
    {
      name: "subject",
      label: "Email Subject",
      placeholder: "Enter Email Subject",
      type: "text",
      dataType: "text",
      maxLength: 1000,
      required: true,
      disabled: disabled,
    },
  ];

  if (previewContent) {
    return (
      <>
        <div className="d-flex pb-2 pt-3 border-top p-4">
          <h6>
            <strong>Subject:</strong> {state.subject}
          </h6>
        </div>
        <div className="editor-preview">
          <HtmlPreviewIframe
            htmlContent={state.content}
            height={"calc(100vh)"}
          />
        </div>

        <div className="ant-modal-footer mt-4">
          <Button
            className={`btn btn-theme mr-1`}
            onClick={() => setPreviewContent(false)}>
            Edit Email
          </Button>

          <Button className={`btn btn-theme mr-1`} onClick={handleSubmit}>
            Send Email
          </Button>

          <Button key="cancel" onClick={() => close()}>
            Cancel
          </Button>
        </div>
      </>
    );
  }
  return (
    <>
      <div className="row">
        <div className="col-md-8 col-sm-12">
          <div className="card card-border  mb-4">
            <div className="card-body">
              <CustomEditor
                value={state.content}
                onChange={(content) => {
                  setState((prev: any) => ({ ...prev, content }));
                }}
              />
              {errors?.content && (
                <div className="text-danger mt-2">{errors?.content}</div>
              )}
            </div>
          </div>
        </div>
        <div className="col-md-4 col-sm-12">
          <div className="card card-border">
            <div className="card-body send-offer">
              <h6 className="mb-0">Send Email</h6>
              <div className="p-3">
                <ModalFormInput
                  buttonTitle="Send Email" // Title for the submit button
                  fields={EmailFormFields} // Fields for the form
                  setState={setState} // Function to update form state
                  state={state} // Current form state
                  onSubmit={onSubmit} // Function to handle form submission
                  onClose={close} // Function to close the modal
                  submitClass={"ml-1"}
                  customButtons={
                    <>
                      {!state.subject ||
                      !state.content ||
                      state.subject?.trim() === "" ||
                      state.content?.trim() === "" ? (
                        <></>
                      ) : (
                        <Button
                          className={`btn btn-theme mr-1`}
                          onClick={() => setPreviewContent(true)}>
                          Preview
                        </Button>
                      )}
                    </>
                  }
                />
              </div>
            </div>
          </div>

          <CandidateCardWithLinks candidate={candidate as CandidateInterface} />
        </div>
      </div>
    </>
  );
};

export default CandidateJobEmailModal;
