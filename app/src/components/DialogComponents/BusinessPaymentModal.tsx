import React, { useCallback, useEffect, useState } from "react";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import { BusinessInterface } from "@src/redux/interfaces";
import { BusinessPaymentForm } from "../Payment";
import { STRIPE_PUBLISHABLE_KEY } from "@src/constants";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";
import { adminPaymentApi } from "@src/apis/adminApis";
import flashMessage from "../FlashMessage";
import { Button } from "antd";

const stripePromise: Promise<Stripe | null> = loadStripe(
  STRIPE_PUBLISHABLE_KEY!,
);

interface BusinessPaymentModalProps {
  close: () => void;
  onPayment: () => void;
  readonly business: BusinessInterface;
}

// Confirmation modal component
const BusinessPaymentModal = (props: BusinessPaymentModalProps) => {
  // Function to handle confirmation button click
  const dispatch = useAppDispatch();
  const { business, close } = props;
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [planList, setPlanList] = useState<Array<any>>([]);

  const fetchAndShowPlans = useCallback(
    async () => {
      dispatch(setLoader(true));
      const { success, ...response } = await adminPaymentApi.getPlans();
      if (success) {
        setPlanList(response.data);
      } else {
        flashMessage(response.message, "error");
        // close()
      }
      dispatch(setLoader(false));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch],
  );

  useEffect(() => {
    fetchAndShowPlans();
  }, [business, fetchAndShowPlans]);

  const setPlan = (plan: any) => {
    setSelectedPlan(plan);
  };

  if (selectedPlan) {
    return (
      <>
        <Elements stripe={stripePromise}>
          <BusinessPaymentForm {...props} selectedPlan={selectedPlan} />
        </Elements>
      </>
    );
  }

  return (
    <>
      <div className="subscription-card-modal plan-container ">
        <h4 className="mb-2">Choose Your Payment Plan</h4>
        {planList.length ? (
          <div className="grid-container">
            {planList.map((plan: any, index: number) => (
              <div key={index} className="subscription-card">
                <h6>{plan.product_name}</h6>
                <p className="subscription-plan-price">
                  {plan.amount} {plan.currency.toUpperCase()}
                </p>
                <p className="subscription-plan-description">
                  {plan.description}
                </p>
                <Button
                  onClick={() => setPlan(plan)}
                  className={"subscription-button"}>
                  Buy Now
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="subscription-card">
            <h6 className="mb-2">Fetching Plan Lists</h6>
          </div>
        )}
      </div>
      <div className="ant-modal-footer">
        {/* Cancel button to close the modal */}
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default BusinessPaymentModal;
