import React, { useState } from "react";
import type { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import {
  EmailSubjectContentInterface,
  KeyPairInterface,
} from "@src/redux/interfaces";
import { Button } from "antd";

const SendEmailFields: GlobalInputFieldType[] = [
  {
    name: "subject",
    label: "Email Subject",
    placeholder: "Enter Email Subject",
    type: "text",
    dataType: "text",
    maxLength: 1000,
    required: true,
  },
  {
    name: "content",
    label: "Email content",
    placeholder: "Enter Email content",
    type: "html",
    dataType: "html",
    maxLength: 1000,
    required: true,
    fieldClassName: "min-height-100-max-200",
  },
];

const defaultValue: EmailSubjectContentInterface = {
  subject: "",
  content: "",
};

type CustomEmailModalProps = {
  close: Function;
  onSendEmail: (data: EmailSubjectContentInterface) => void;
  buildTemplate: (content: string) => void;
};

// Component for the modal to create a new email
const CustomEmailModal = ({
  close,
  onSendEmail,
  buildTemplate,
}: CustomEmailModalProps) => {
  // State to manage form data
  const [previewContent, setPreviewContent] = useState<boolean>(false);
  const [state, setState] = useState<KeyPairInterface>(defaultValue);
  // Function to handle form submission
  const onSubmit = async () => {
    onSendEmail(state as EmailSubjectContentInterface);
  };

  const handleSubmit = () => {
    onSendEmail(state as EmailSubjectContentInterface);
  };

  if (previewContent) {
    return (
      <>
        <div className="d-flex pb-2 pt-3 border-top ">
          <h6>
            <strong>Subject:</strong> Recruitease Pro - {state.subject}
          </h6>
        </div>
        <div
          dangerouslySetInnerHTML={{
            __html: buildTemplate
              ? buildTemplate(state.content)
              : state.content,
          }}
          className="pb-4 border-bottom"
        />

        <div className="ant-modal-footer mt-4">
          <Button
            className={`btn btn-theme mr-1`}
            onClick={() => setPreviewContent(false)}>
            Edit Email
          </Button>

          <Button className={`btn btn-theme mr-1`} onClick={handleSubmit}>
            Send Email
          </Button>

          <Button className="btn" key="cancel" onClick={() => close()}>
            Cancel
          </Button>
        </div>
      </>
    );
  }
  return (
    <>
      {/* Render the modal form input component */}
      <ModalFormInput
        buttonTitle="Send Email" // Title for the submit button
        fields={SendEmailFields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
        customButtons={
          <>
            {!state.subject ||
            !state.content ||
            state.subject?.trim() === "" ||
            state.content?.trim() === "" ? (
              <></>
            ) : (
              <Button
                className={`btn btn-theme mr-1`}
                onClick={() => setPreviewContent(true)}>
                Preview
              </Button>
            )}
          </>
        }
      />
    </>
  );
};

export default CustomEmailModal;
