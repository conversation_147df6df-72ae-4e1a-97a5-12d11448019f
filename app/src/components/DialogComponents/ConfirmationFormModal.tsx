import React, { useState } from "react";
import { GlobalInputFieldType } from "../input/GlobalInput";
import { ModalFormInput } from "../ModalInput/ModalFormInput";
import { KeyPairInterface } from "@src/redux/interfaces";

type ConfirmationModalProps = {
  message: string | React.ReactNode; // Message to display in the confirmation modal
  onConfirm: (state: KeyPairInterface) => Function; // Function to call when the confirmation button is clicked
  buttonTitle?: string; // Title for the confirmation button
  close: Function; // Function to close the modal
  fields: GlobalInputFieldType[];
  defaultState?: KeyPairInterface;
};

// Confirmation modal component
const ConfirmationFormModal = ({
  message,
  close,
  onConfirm,
  fields,
  defaultState = {},
  buttonTitle = "Save",
}: ConfirmationModalProps) => {
  // Function to handle confirmation button click
  const [state, setState] = useState<KeyPairInterface>(defaultState);

  const onSubmit = () => {
    onConfirm(state);
  };

  return (
    <>
      {/* Display the message */}
      {message ?? ""}

      <ModalFormInput
        buttonTitle={buttonTitle} // Title for the submit button
        fields={fields} // Fields for the form
        setState={setState} // Function to update form state
        state={state} // Current form state
        onSubmit={onSubmit} // Function to handle form submission
        onClose={close} // Function to close the modal
      />
    </>
  );
};

export default ConfirmationFormModal;
