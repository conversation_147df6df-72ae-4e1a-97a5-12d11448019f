import React, { useState } from "react";
import { useAppDispatch } from "@src/redux/store";
import flashMessage from "../FlashMessage";
import { setLoader } from "@src/redux/actions";
import { Upload, Button } from "antd";
import { jobsApi } from "@src/apis/wildcardApis";
import { FormGroup, FormLabel } from "react-bootstrap";
import ArchiveIcon from "@mui/icons-material/Archive";
import InputField, { CustomSelectDateEvent } from "../input/InputField";
import { ValidateInputValue } from "@src/helper/common";
import { KeyPairInterface } from "@src/redux/interfaces";
import { validateFieldError } from "@src/helper/validations/custom";

// Props for the JobRequest component
type JobRequestProps = {
  jobId: number;
  close: Function;
};

// Component for the modal to add a new candidate
const JobRequest = ({ jobId, close }: JobRequestProps) => {
  const dispatch = useAppDispatch();
  const [state, setState] = useState<KeyPairInterface>({});
  const [error, setError] = useState<KeyPairInterface>({});

  const [uploadedResume, setUploadResumeData] = useState<Array<any>>([]);

  // Function to validate the uploaded file before submission
  const beforeUpload = async (file: File) => {
    const fileExtension = file.name.split(".").pop()?.toLowerCase();
    const allowedFileTypes = [
      "ppt",
      "pptx",
      "pdf",
      "odt",
      "ods",
      "odp",
      "odg",
      "doc",
      "docx",
    ];
    if (!fileExtension || !allowedFileTypes.includes(fileExtension)) {
      flashMessage(
        `Only ${allowedFileTypes.join(", ")} files are accepted.`,
        "error",
      );
      return Upload.LIST_IGNORE;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      flashMessage("Maximum file upload size allowed is 2 MB.", "error");
      return Upload.LIST_IGNORE;
    }
    const isFileExist = uploadedResume.some(
      (uploadedFile) =>
        uploadedFile.name === file.name && uploadedFile.size === file.size,
    );

    if (isFileExist) {
      flashMessage(`${file.name} has already been uploaded.`, "error");
      return Upload.LIST_IGNORE; // Prevent the file from being uploaded
    }
    return true;
  };

  // Function to handle file changes
  const handleChange = async (info: any) => {
    const { status } = info.file;
    if (!status || status === "error") {
      return null;
    }
    const files = info.fileList;
    if (files) {
      setUploadResumeData([files[files.length - 1]]);
      setError((prev) => ({ ...prev, file: "" }));
    }
  };

  // Function to upload the resume file and extract details
  const handleSubmit = async () => {
    const isValid = validate();
    if (!isValid) {
      return null;
    }

    const formData = new FormData();
    const file = uploadedResume[0];
    formData.append(`files`, file.originFileObj);
    formData.append(`name`, state.name);
    formData.append(`email`, state.email);

    await dispatch(setLoader(true));
    const { success, ...response } = await jobsApi.applyJobRequest(
      jobId,
      formData,
    );
    await dispatch(setLoader(false));
    flashMessage(response.message, success ? "success" : "error");
    if (success) {
      setUploadResumeData([]);
      close();
    }
  };

  const handleRemove = (uid: string) => {
    setUploadResumeData(uploadedResume.filter((file) => file.uid !== uid));
  };

  const handleInputChangeEvent = async (
    e: React.ChangeEvent<HTMLInputElement | CustomSelectDateEvent>,
  ) => {
    const { error, key, value, changable, index } = ValidateInputValue(e);
    if (changable || !(e.target instanceof HTMLInputElement)) {
      setState((prev: KeyPairInterface) => ({ ...prev, [key]: value }));
    }
    setError((prev: KeyPairInterface) => ({ ...prev, [key]: error }));
  };

  const handleInputBlurEvent = (
    e: React.ChangeEvent<HTMLInputElement | CustomSelectDateEvent>,
  ) => {
    const { error, key, value, trim, index } = ValidateInputValue(e, "blur");
    const onEmptyNull = e.target.dataset?.empty ?? false;
    if (
      onEmptyNull &&
      (!value || (typeof value == "string" && value.trim() === ""))
    ) {
      setState((prev: KeyPairInterface) => ({ ...prev, [key]: null }));
    } else {
      if (trim && typeof value == "string") {
        setState((prev: KeyPairInterface) => ({
          ...prev,
          [key]: value.trim(),
        }));
      } else {
        setState((prev: KeyPairInterface) => ({ ...prev, [key]: value }));
      }
    }
    setError((prev: KeyPairInterface) => ({ ...prev, [key]: error }));
  };

  const handleKeyDownEvent = async (
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    const target = e.target as any;
    const targetType = target?.type ?? ("text" as string);
    if (
      (e.key === "Enter" || e.key === "enter") &&
      !["textarea"].includes(targetType)
    ) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const validate = () => {
    let isValid = true;

    const nameError = validateFieldError({
      name: "name",
      label: "Name",
      type: "text",
      dataType: "alphabetics",
      maxLength: 80,
      value: state.name,
      required: true,
    });
    if (nameError) {
      setError((prev) => ({ ...prev, name: nameError.errorMsg }));
      if (nameError.errorMsg.length) {
        isValid = false;
      }
    }
    const emailError = validateFieldError({
      name: "email",
      label: "Email",
      type: "email",
      dataType: "email",
      maxLength: 80,
      value: state.email,
      required: true,
    });
    if (emailError) {
      setError((prev) => ({ ...prev, email: emailError.errorMsg }));
      if (emailError.errorMsg.length) {
        isValid = false;
      }
    }

    if (
      !uploadedResume ||
      (uploadedResume !== undefined &&
        uploadedResume !== null &&
        Object.keys(uploadedResume).length === 0) ||
      uploadedResume === undefined ||
      uploadedResume === null
    ) {
      setError((prev) => ({ ...prev, file: "Resume is required" }));
      isValid = false;
    } else {
      setError((prev) => ({ ...prev, file: "" }));
    }

    return isValid;
  };

  return (
    <>
      {/* Render the modal form input component */}
      <FormGroup className="mb-3">
        <InputField
          label={"Name"}
          name={"name"}
          type={"string"}
          dataType={"alphabetics"}
          placeholder={"Enter your name"}
          value={state.name}
          error={error.name}
          maxLength={80}
          required={true}
          onChangeInput={handleInputChangeEvent}
          onBlur={handleInputBlurEvent}
          onKeyDown={handleKeyDownEvent}
        />
        <InputField
          label={"Email"}
          name={"email"}
          type={"email"}
          dataType={"email"}
          placeholder={"Enter your email"}
          value={state.email}
          error={error.email}
          required={true}
          maxLength={80}
          onChangeInput={handleInputChangeEvent}
          onBlur={handleInputBlurEvent}
          onKeyDown={handleKeyDownEvent}
        />
        <div className="mb-3 group-relative">
          <FormLabel style={{ color: "black" }}>
            Upload Resume<span style={{ color: "red" }}>*</span>
          </FormLabel>
          <Upload.Dragger
            name="file"
            multiple={false}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            onRemove={() => setUploadResumeData([])}
            accept=".ppt,.pptx,.pdf,.odt,.ods,.odp,.odg,.doc,.docx"
            showUploadList={true}
            fileList={uploadedResume}>
            <p className="ant-upload-drag-icon">
              <ArchiveIcon fontSize="large" color="primary" />
            </p>
            <p className="ant-upload-text">
              Click or drag file to this area to upload
            </p>
            <p className="ant-upload-text">
              Maximum file upload size allowed is 2 MB.
            </p>
          </Upload.Dragger>
          {error.file && <p className="error mt-1">{error.file}</p>}
        </div>
      </FormGroup>

      <div className="ant-modal-footer mt-2 mb-1">
        <Button
          key="upload"
          className="btn btn-theme font-14 mr-1"
          onClick={handleSubmit}>
          Submit
        </Button>
        <Button className="btn" key="cancel" onClick={() => close()}>
          Cancel
        </Button>
      </div>
    </>
  );
};

export default JobRequest;
