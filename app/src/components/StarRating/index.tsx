import React from "react";
import Image from "next/image"; // Adjust the import based on your setup

type StarRating = {
  rating: number;
};

export const StarRating: React.FC<StarRating> = ({ rating }) => {
  const totalStars = 5; // Total number of stars

  return (
    <>
      {Array.from({ length: totalStars }, (_, index) => {
        const starIndex = index + 1;
        return (
          <Image
            key={index}
            src={
              starIndex <= rating
                ? "/images/fill-star.svg"
                : "/images/unfill-star.svg"
            }
            width={20}
            height={19}
            alt={starIndex <= rating ? "filled star" : "unfilled star"}
          />
        );
      })}
    </>
  );
};
