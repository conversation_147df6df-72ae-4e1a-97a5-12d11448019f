import { Info } from "@mui/icons-material";
import { Tooltip } from "antd";
import React from "react";
import Form from "react-bootstrap/Form";

type CheckBoxFieldProps = {
  label: string;
  name: string;
  type: string;
  placeholder: string;
  value: any;
  onChangeInput: (e: React.ChangeEvent<HTMLInputElement>) => void; // Define the type for onChangeInput function
  style?: React.CSSProperties;
  onBlur?: (e: any) => void; // Define the type for onKeyDown function
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void; // Define the type for onKeyDown function
  maxLength?: number;
  minLength?: number;
  error?: string;
  disabled?: boolean;
  dataType?: string;
  strict?: boolean;
  tooltipTitle?: string;
  required?: boolean;
  minDate?: string | null;
  maxDate?: string | null;
  onEmptyNull?: boolean;
  trim?: boolean;
  className?: string;
  dateFormat?: string;
  customNode?: any;
};

const CheckBoxField = ({
  label,
  name,
  type,
  placeholder,
  value,
  onChangeInput,
  style,
  onKeyDown,
  error,
  dataType,
  strict,
  tooltipTitle,
  minDate,
  maxDate,
  required = false,
  disabled = false,
  onEmptyNull = false,
  trim = false,
  className = "",
  onBlur,
  customNode,
  ...props
}: CheckBoxFieldProps): JSX.Element => {
  return (
    <Form.Group className={`mb-3 group-relative ${className}`}>
      <Form.Check.Input
        type="checkbox"
        name={name}
        checked={value}
        onChange={onChangeInput}
        disabled={disabled}
        autoComplete={"off"}
        onBlur={(e) => onBlur && onBlur(e as any)}
        onKeyDown={(e) =>
          disabled ? () => {} : onKeyDown && onKeyDown(e as any)
        }
      />
      {customNode}

      <Form.Label>
        {label}
        {tooltipTitle && (
          <Tooltip placement="top" title={tooltipTitle} trigger={"hover"}>
            <Info fontSize="small" className="cursor-pointer" />
          </Tooltip>
        )}
      </Form.Label>

      {error && <p className="error">{error}</p>}
    </Form.Group>
  );
};

export default CheckBoxField;
