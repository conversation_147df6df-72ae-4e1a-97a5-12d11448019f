import React from "react";
import InputField from "@src/components/input/InputField";
import SelectField from "./SelectField";
import CheckBoxField from "./CheckboxInput";
import HtmlEditor from "./HtmlEditor";

const DynamicFormField = (props: any): JSX.Element => {
  let InputComponent: React.ComponentType<any> = InputField; // Default to InputField

  switch (props.type) {
    case "select":
      InputComponent = SelectField;
      break;
    case "checkbox":
      InputComponent = CheckBoxField;
      break;
    case "html":
      InputComponent = HtmlEditor;
      break;
    default:
      InputComponent = InputField;
      break;
  }

  return <InputComponent {...props} />;
};

export default DynamicFormField;
