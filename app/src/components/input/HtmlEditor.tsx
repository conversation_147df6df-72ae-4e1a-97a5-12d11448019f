import { Info } from "@mui/icons-material";
import { Tooltip } from "antd";
import React from "react";
import Form from "react-bootstrap/Form";
import dynamic from "next/dynamic";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

interface HtmlEvent<T> {
  value: T;
  name: string;
  type: string;
  dataset?: {
    [key: string]: any | undefined; // Index signature for optional key-value pairs
  };
}

const toolbarOptions = [
  [{ font: [] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  ["bold", "italic", "underline", "strike"], // toggled buttons
  [{ color: [] }, { background: [] }], // dropdown with defaults from theme
  [{ script: "sub" }, { script: "super" }], // superscript/subscript
  [{ list: "ordered" }, { list: "bullet" }],
  [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
  [{ direction: "rtl" }], // text direction

  [{ size: ["small", false, "large", "huge"] }], // custom dropdown
  [{ align: [] }],

  ["link"], // media
  ["clean"], // remove formatting button
];

const modules = {
  toolbar: toolbarOptions,
};

export type CustomHtmlEvent = HtmlEvent<string>;

type HtmlEditorProps = {
  label: string;
  name: string;
  type: string;
  placeholder: string;
  value: any;
  onChangeInput: (
    e: React.ChangeEvent<HTMLInputElement | CustomHtmlEvent>,
  ) => void; // Define the type for onChangeInput function
  style?: React.CSSProperties;
  labelStyle?: React.CSSProperties;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void; // Define the type for onKeyDown function
  onBlur?: (e: any) => void; // Define the type for onKeyDown function
  error?: string;
  disabled?: boolean;
  dataType?: string;
  strict?: boolean;
  tooltipTitle?: string;
  required?: boolean;
  trim?: boolean;
  className?: string;
  fieldClassName?: string;
  customNode?: any;
};

const HtmlEditor = ({
  label,
  name,
  value,
  onChangeInput,
  error,
  tooltipTitle,
  required = false,
  onBlur,
  className = "",
  onKeyDown,
  disabled,
  labelStyle,
  fieldClassName = "",
  customNode,
  ...props
}: HtmlEditorProps): JSX.Element => {
  const handleInputChange = (data: string) => {
    const target = {
      value: data,
      name: name,
      type: "html",
      dataset: { label: label, type: "html" },
    } as CustomHtmlEvent;
    const newEvent = {
      target: target,
    } as React.ChangeEvent<CustomHtmlEvent>;
    onChangeInput(newEvent);
  };

  const onBlurEvent = () => {
    const target = {
      value: value,
      name: name,
      type: "html",
      dataset: { label: label, type: "html" },
    } as CustomHtmlEvent;
    const newEvent = {
      target: target,
    } as React.ChangeEvent<CustomHtmlEvent>;
    onBlur && onBlur(newEvent as any);
  };

  return (
    <Form.Group className={`mb-3 group-relative ${className}`}>
      <Form.Label style={labelStyle}>
        {label}
        {required && <span className="required">* </span>}
        {tooltipTitle && (
          <Tooltip placement="top" title={tooltipTitle} trigger={"hover"}>
            <Info fontSize="small" className="cursor-pointer" />
          </Tooltip>
        )}
      </Form.Label>
      {customNode}
      <ReactQuill
        value={value}
        onChange={handleInputChange}
        onBlur={onBlurEvent}
        readOnly={disabled}
        className={fieldClassName}
        modules={modules}
        {...props}
      />
      {error && <p className="error">{error}</p>}
    </Form.Group>
  );
};

export default HtmlEditor;
