import React from "react";
import Form from "react-bootstrap/Form";
import type {
  GlobalReadOnlyFieldType,
  GlobalReadOnlyFieldTypeRecord,
} from "../input/GlobalInput";
import { KeyPairInterface } from "@src/redux/interfaces";

type ReadOnlyFieldProps = {
  fields: GlobalReadOnlyFieldType[];
  state: KeyPairInterface;
};

// Helper function to group fields by their `group` property
const groupFieldsByGroup = (fields: GlobalReadOnlyFieldType[]) => {
  const groups: GlobalReadOnlyFieldTypeRecord = fields.reduce((acc, field) => {
    const groupName = field.groupName || "ungrouped"; // Default to 'ungrouped' if group is empty
    if (!acc[groupName]) {
      acc[groupName] = [];
    }
    acc[groupName].push(field);
    return acc;
  }, {} as GlobalReadOnlyFieldTypeRecord);

  // Second pass: Sort groups and fields
  const sortedGroups = Object.entries(groups)
    .sort(([groupNameA, fieldsA], [groupNameB, fieldsB]) => {
      // Extract groupPosition for sorting groups
      const positionA = fieldsA[0]?.groupPosition ?? Number.MAX_SAFE_INTEGER;
      const positionB = fieldsB[0]?.groupPosition ?? Number.MAX_SAFE_INTEGER;
      return positionA - positionB;
    })
    .reduce((acc, [groupName, fields]) => {
      // Sort fields within each group
      const sortedFields = fields.sort(
        (a, b) =>
          (a.fieldPosition ?? Number.MAX_SAFE_INTEGER) -
          (b.fieldPosition ?? Number.MAX_SAFE_INTEGER),
      );
      acc[groupName] = sortedFields;
      return acc;
    }, {} as GlobalReadOnlyFieldTypeRecord);

  return sortedGroups;
};

export const ReadOnlyField: React.FC<ReadOnlyFieldProps> = ({
  fields,
  state,
}) => {
  const groupedFields = groupFieldsByGroup(fields);

  return (
    <>
      <Form>
        {Object.keys(groupedFields).map((groupName, groupIndex) => (
          <div key={groupIndex} className="group-section row row-gap-2">
            {groupName !== "ungrouped" && groupName !== "" && (
              <h6 className="form-group-title">{groupName}</h6>
            )}
            {groupedFields[groupName].map((field, fieldIndex) => (
              <Form.Group
                className={`mb-3 group-relative ${field.className || ""}`}
                key={fieldIndex}>
                <Form.Label>{field.label}</Form.Label>
                {typeof state[field.name] === "string" ? (
                  <div className="form-control custom-height-100 disabled">
                    {state[field.name]?.trim()?.length > 0
                      ? state[field.name].trim()
                      : "N/A"}
                  </div>
                ) : (
                  <>
                    {field.multiple && Array.isArray(state[field.name]) ? (
                      <>
                        {state[field.name].map((val: any, index: number) => (
                          <div
                            className="form-control custom-height-100 disabled mt-1"
                            key={index}>
                            {val}
                          </div>
                        ))}
                      </>
                    ) : (
                      <div className="form-control custom-height-100 disabled">
                        {state[field.name]}
                      </div>
                    )}
                  </>
                )}
              </Form.Group>
            ))}
          </div>
        ))}
      </Form>
    </>
  );
};
