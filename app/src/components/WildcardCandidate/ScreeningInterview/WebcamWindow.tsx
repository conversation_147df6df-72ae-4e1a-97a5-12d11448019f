import React, { forwardRef, useEffect, useRef } from "react";
import Webcam from "react-webcam";

const WebcamWindow = forwardRef(
  (
    {
      audio,
      videoConstraints,
      imageSmoothing,
      disablePictureInPicture,
      mirrored,
      muted,
      onUserMedia,
      onUserMediaError,
      style,
      mode,
      screenStream,
      onScreenShareStopped,
    }: any,
    ref: any,
  ) => {
    const layoutStyles: React.CSSProperties = {
      position: mode === "coding" ? "fixed" : "relative",
      top: mode === "coding" ? "10px" : undefined,
      right: mode === "coding" ? "10px" : undefined,
      width: mode === "coding" ? "230px" : "100%",
      height: mode === "coding" ? "120px" : "400px",
      zIndex: mode === "coding" ? 1000 : undefined,
      boxShadow: mode === "coding" ? "0 0 10px rgba(0,0,0,0.2)" : undefined,
      borderRadius: "8px",
      overflow: "hidden",
      display: "flex",
      flexDirection: "row",
      background: "#000",
    };

    const halfStyles: React.CSSProperties = {
      width: "50%",
      height: "100%",
      objectFit: "cover",
      background: "#222",
    };

    const screenVideoRef = useRef<HTMLVideoElement>(null);

    useEffect(() => {
      if (screenVideoRef.current && screenStream) {
        screenVideoRef.current.srcObject = screenStream;
      }
    }, [screenStream]);

    // Listen for screen share stop
    useEffect(() => {
      if (screenStream) {
        const [videoTrack] = screenStream.getVideoTracks();
        const handleEnded = () => {
          if (onScreenShareStopped) onScreenShareStopped();
        };
        if (videoTrack) {
          videoTrack.addEventListener("ended", handleEnded);
        }
        return () => {
          if (videoTrack) {
            videoTrack.removeEventListener("ended", handleEnded);
          }
        };
      }
    }, [screenStream, onScreenShareStopped]);

    return (
      <div className="webcam-screenshare-container" style={layoutStyles}>
        {/* Webcam Frame */}
        <div
          style={{ ...halfStyles, display: "flex", flexDirection: "column" }}>
          <div
            style={{
              width: "100%",
              background: "#181818",
              color: "#fff",
              textAlign: "center",
              padding: "6px 0",
              fontWeight: 500,
              fontSize: "12px",
              borderBottom: "1px solid #333",
            }}>
            Webcam
          </div>
          <div style={{ flex: 1 }}>
            <Webcam
              audio={audio}
              videoConstraints={videoConstraints}
              imageSmoothing={imageSmoothing}
              disablePictureInPicture={disablePictureInPicture}
              ref={ref}
              mirrored={mirrored}
              muted={muted}
              onUserMedia={onUserMedia}
              onUserMediaError={onUserMediaError}
              style={{
                ...style,
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </div>
        </div>
        {/* Vertical Divider */}
        <div
          style={{
            width: "4px",
            height: "100%",
            background: "#444",
            alignSelf: "stretch",
          }}
        />
        {/* Screen Share Frame */}
        <div
          style={{ ...halfStyles, display: "flex", flexDirection: "column" }}>
          <div
            style={{
              width: "100%",
              background: "#181818",
              color: "#fff",
              textAlign: "center",
              padding: "6px 0",
              fontWeight: 500,
              fontSize: "12px",
              borderBottom: "1px solid #333",
            }}>
            Screen Share
          </div>
          <div style={{ flex: 1 }}>
            {screenStream ? (
              <video
                ref={screenVideoRef}
                autoPlay
                playsInline
                muted
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
              />
            ) : (
              <div
                style={{
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: "#fff",
                  background: "#333",
                  fontSize: "12px",
                }}>
                {"Screen share not active"}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
);

// ✅ Add display name to satisfy react/display-name rule
WebcamWindow.displayName = "WebcamWindow";

export default WebcamWindow;
