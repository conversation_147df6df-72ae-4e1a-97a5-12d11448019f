import { ScreeningQuestionType } from "@src/redux/interfaces";
import React, { useEffect, useState } from "react";
import Form from "react-bootstrap/Form";

export const ScreeningQuestion: React.FC<ScreeningQuestionType> = ({
  questionNumber,
  questionId,
  question,
  answer,
  onSetAnswer,
  options = [],
  disable = false,
}) => {
  const [selectedOption, setSelectedOption] = useState<string>("");
  const [textAnswer, setTextAnswer] = useState<string>("");

  useEffect(() => {
    setTextAnswer(answer || "");
    setSelectedOption(answer || "");
  }, [answer]);

  const handleOptionChange = (option: string) => {
    setSelectedOption(option);
    onSetAnswer({ questionId, answer: option });
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disable) {
      return null;
    }
    const value = e.target.value;
    setTextAnswer(value);
    onSetAnswer({ questionId, answer: value }); // Set answer as an object
  };

  return (
    <>
      <li>
        <span>{questionNumber}</span>
        <Form.Group className={`m-0 group-relative`}>
          <Form.Label>
            <pre className="interview-question">{question}</pre>
          </Form.Label>
          {options && options.length > 0 ? ( // Check if options are available
            <Form>
              {options.map((option, index) => (
                <Form.Check
                  key={index}
                  type="radio"
                  label={option}
                  name={questionId}
                  value={option}
                  checked={selectedOption === option}
                  onChange={() => handleOptionChange(option)}
                />
              ))}
            </Form>
          ) : (
            <Form.Control
              as="textarea" // Using textarea for text input
              placeholder="Your answer"
              value={textAnswer}
              id={questionId}
              onChange={handleTextChange}
              autoComplete="off"
              rows={3} // Adjust the number of rows as needed
              disabled={disable}
            />
          )}
        </Form.Group>
      </li>
    </>
  );
};
