import { Mic, Stop } from "@mui/icons-material";
import { interviewsApi } from "@src/apis/wildcardCandidateApis";
import flashMessage from "@src/components/FlashMessage";
import { setLoader, setLoaderText } from "@src/redux/actions";
import { useAppDispatch } from "@src/redux/store";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";

type AudioRecordingType = {
  interviewId: number;
  title?: string;
  setText: (text: string) => void;
  uniqueKey: number | string;
  stream: MediaStream;
  setRecordingStatus: React.Dispatch<React.SetStateAction<boolean>>; // Function to update state
};

export const AudioRecording: React.FC<AudioRecordingType> = ({
  uniqueKey,
  interviewId,
  setText,
  title = "",
  stream,
  setRecordingStatus,
}) => {
  const dispatch = useAppDispatch();
  const audioChunks = useRef<Blob[]>([]);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null,
  );
  const [isRecording, setIsRecording] = useState<boolean>(false);

  useEffect(() => {
    setIsRecording(false);

    // Cleanup on unmount or when uniqueKey changes
    return () => {
      setIsRecording(false);
    };
  }, [uniqueKey]);

  useEffect(() => {
    setRecordingStatus(isRecording);
    return () => {
      setRecordingStatus(false);
    };
  }, [isRecording, setRecordingStatus]);

  const onUploadProgress = async (progressEvent: any) => {
    const percentCompleted = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total,
    );
    await dispatch(setLoaderText(`Extracting Audio ${percentCompleted}% `));
    if (percentCompleted === 100) {
      await dispatch(setLoaderText(`Extracting Text from Audio`));
    }
  };

  const fetchAndSetText = async (audioBlob: Blob) => {
    const formData = new FormData();
    if (audioBlob) {
      formData.append("file", audioBlob, `${new Date().toISOString()}.wav`);
    } else {
      return null;
    }

    await dispatch(setLoader(true));
    const { success, ...response } = await interviewsApi.convertAudioToText(
      interviewId,
      formData,
      onUploadProgress,
    );
    await dispatch(setLoader(false));
    await dispatch(setLoaderText(""));

    if (success) {
      const { valid, text } = response.data;
      if (valid) {
        setText(text);
      } else {
        flashMessage(response.message, "error");
      }
    } else {
      flashMessage(response.message, "error");
    }
  };

  const startRecording = async () => {
    if (stream) {
      const audioStream = new MediaStream(stream.getAudioTracks());
      const recorder = new MediaRecorder(audioStream);

      recorder.ondataavailable = (event: BlobEvent) => {
        audioChunks.current = [...audioChunks.current, event.data];
      };

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks.current, { type: "audio/wav" });
        const audioURL = URL.createObjectURL(audioBlob);
        await fetchAndSetText(audioBlob);
        audioChunks.current = [];
      };

      recorder.start();
      setMediaRecorder(recorder);
      setIsRecording(true);
    } else {
      console.error("No media stream available for recording.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  return (
    <>
      <Button
        onClick={isRecording ? stopRecording : startRecording}
        variant="audio-answer">
        {isRecording ? <Stop /> : <Mic />}
        {isRecording ? " Stop" : " Start"} {title}
      </Button>
    </>
  );
};
