import React, { useState } from "react";
import { ScreeningQuestion } from "./ScreeningQuestion";
import {
  InterviewAnswer,
  ScreeningQuestionInterface,
} from "@src/redux/interfaces";
import { Button } from "react-bootstrap";
import { AudioRecording } from "./AudioRecording";

type QuestionListType = {
  questions: ScreeningQuestionInterface[];
  answers: InterviewAnswer[];
  setAnswers: React.Dispatch<React.SetStateAction<InterviewAnswer[]>>;
  handleSubmit: () => void;
  submitted: boolean;
  interviewId: number;
  stream: MediaStream | null;
  currentQuestionIndex: any;
  attemptQuestions: any;
  handleSetAnswer: any;
  goToPreviousQuestion: any;
  goToNextQuestion: any;
};

export const QuestionList: React.FC<QuestionListType> = ({
  interviewId,
  questions,
  answers,
  handleSubmit,
  submitted,
  stream,
  currentQuestionIndex,
  attemptQuestions,
  handleSetAnswer,
  goToPreviousQuestion,
  goToNextQuestion,
}) => {
  const [disableTextField, setDisableTextField] = useState<boolean>(false);
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const answer =
    answers.find((ans) => ans.questionId === questions[currentQuestionIndex].id)
      ?.answer || null;

  const handleText = (value: string) => {
    const interviewQuestionId = questions[currentQuestionIndex].id;
    const completeAnswer = (answer ?? "") + (answer ? " " : "") + (value ?? "");
    handleSetAnswer({
      questionId: interviewQuestionId,
      answer: completeAnswer,
    }); // Set answer as an object
  };

  const interviewQuestionOptions =
    questions[currentQuestionIndex]?.options ?? [];
  const interviewQuestionNumber = currentQuestionIndex + 1;
  const currentQuestion: any = questions[currentQuestionIndex];
  const interviewQuestionId: any = currentQuestion?.id ?? 0;
  const interviewQuestion = currentQuestion?.question ?? "";

  return (
    <>
      <div className="interview-question card p-4 m-0 h-100">
        <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
          <h4 className="mb-2 heading-clr d-flex align-items-center gap-3">
            Interviewer Questions{" "}
            <span className="ms-auto">
              Questions: <strong>{questions.length}</strong> / Attempted:{" "}
              <strong>{`${attemptQuestions ?? ""}`}</strong>
            </span>
          </h4>
        </div>
        <ul>
          <ScreeningQuestion
            questionNumber={interviewQuestionNumber}
            questionId={interviewQuestionId}
            question={interviewQuestion}
            options={interviewQuestionOptions}
            answer={answer}
            disable={disableTextField}
            onSetAnswer={handleSetAnswer}
          />
        </ul>

        <div className="pagination-button">
          <Button
            className="btn btn-primary border-0 d-flex gap-2 align-items-center"
            onClick={goToPreviousQuestion}
            disabled={
              currentQuestionIndex === 0 || disableTextField || disableTextField
            }>
            Previous
          </Button>
          <Button
            className="btn btn-primary border-0 d-flex gap-2 align-items-center"
            onClick={goToNextQuestion}
            disabled={
              currentQuestionIndex === questions.length - 1 || disableTextField
            }>
            Next
          </Button>

          {(!interviewQuestionOptions ||
            interviewQuestionOptions.length == 0) &&
            stream && (
              <AudioRecording
                setText={handleText}
                interviewId={interviewId}
                stream={stream}
                uniqueKey={interviewQuestionNumber}
                setRecordingStatus={setDisableTextField}
                title={"Recording"}
              />
            )}

          {isLastQuestion && !submitted && (
            <Button
              className="btn btn-primary border-0 d-flex gap-2 align-items-center"
              onClick={() => handleSubmit()}>
              Submit
            </Button>
          )}
        </div>
      </div>
    </>
  );
};
