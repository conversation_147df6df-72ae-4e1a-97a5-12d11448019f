import React, { useEffect, useState } from "react";
import { Skeleton } from "antd";
import {
  CandidateInterface,
  OpportunityInterface,
} from "@src/redux/interfaces";
import { useAppDispatch } from "@src/redux/store";
import DialogComponents from "@src/components/DialogComponents";
import { openDialog } from "@src/redux/actions";
import { Button } from "antd";
import { dashboardApi } from "@src/apis/wildcardCandidateApis";

type CandidateInterviewJobCardType = {
  candidate: CandidateInterface | null;
};

export const CandidateInterviewJobCard: React.FC<
  CandidateInterviewJobCardType
> = ({ candidate }) => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [jobDetail, setJobDetail] = useState<OpportunityInterface | null>(null);

  useEffect(() => {
    getDashboardActiveJob();
  }, []);

  // Function to get stats from API and update the stats value
  const getDashboardActiveJob = async () => {
    setLoading(true);
    const { success, data } = await dashboardApi.dashboardActiveJob();
    if (success) {
      setJobDetail(data);
    } else {
      setJobDetail(null);
    }
    setLoading(false);
  };

  const openJobInfo = (job: OpportunityInterface) => {
    console.warn("For testing open modal");

    // Format text into a list with bullet points
    const formatTextWithBullets = (text: string) => {
      const lines = text.split("\n"); // Split the text by newlines
      return `<ul>${lines.map((line) => `<li>${line}</li>`).join("")}</ul>`; // Wrap each line in <li>
    };

    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          ClassName: "candidate-job-detail-modal",
          title: "Job Details",
          width: "50%",
          message: (
            <div className="mt-2 mb-2">
              <h6>Job Overview:</h6>
              <div
                dangerouslySetInnerHTML={{
                  __html: formatTextWithBullets(job.description),
                }}
              />
              <h6>Key Responsibilities:</h6>
              <div
                dangerouslySetInnerHTML={{
                  __html: formatTextWithBullets(job.responsibilities),
                }}
              />
            </div>
          ),
        },
      }),
    );
  };

  if (!candidate || loading) {
    return (
      <div className="card m-0">
        <div className="p-3 border-bottom">
          <h6 className="m-0 fw-semibold heading-clr">
            <Skeleton active paragraph={false} />
          </h6>
        </div>
        <div className="p-3">
          <Skeleton active paragraph={{ rows: 10 }} />
        </div>
      </div>
    );
  }

  if (!jobDetail) {
    return <></>;
  }

  const { experience, salary, department_name, location_names } = jobDetail;

  return (
    <div className="card m-0">
      <div className="p-3 border-bottom">
        <h6 className="m-0 fw-semibold heading-clr">Job Details</h6>
      </div>
      <div className="p-3">
        <div className="requirement">
          <h4 className="mb-4 fw-semibold heading-clr">{jobDetail.title}</h4>
          <h6 className="sub-head">Requirement:</h6>
          {jobDetail.skills && (
            <div className="skills d-flex gap-3 p-3 rounded-3">
              <span>Skills:</span>
              <p className="m-0 fw-medium text-black">{jobDetail.skills}</p>
            </div>
          )}
          <div className="content">
            <ul>
              <li>
                <span>No. of Vacancies:</span>
                <b>{jobDetail.number_of_vacancies}</b>
              </li>
              <li>
                <span>Experience Required:</span>
                {experience > 0 ? <b>Up to {experience} Years</b> : <b>-</b>}
              </li>
              <li>
                <span>Salary:</span>
                <b>{salary && salary > 0 ? salary : "-"}</b>
              </li>
              <li>
                <span>Department:</span>
                <b>{department_name}</b>
              </li>
              <li>
                <span>Location:</span>
                <b>{location_names?.join(", ")}</b>
              </li>
            </ul>
          </div>

          <div className="d-flex justify-content-end">
            <Button
              className="btn btn-primary border-0 mt-5"
              onClick={() => openJobInfo(jobDetail)}>
              Job Detail&apos;s{" "}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
