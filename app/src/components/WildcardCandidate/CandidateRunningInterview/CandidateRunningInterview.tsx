import React, { useEffect, useState } from "react";
import { Skeleton } from "antd";
import { CandidateInterface } from "@src/redux/interfaces";
import { dashboardApi } from "@src/apis/wildcardCandidateApis";
import { CandidateInterviewCard } from "./CandidateInterviewCard";

type CandidateRunningInterviewType = {
  candidate: CandidateInterface | null;
};

export const CandidateRunningInterview: React.FC<
  CandidateRunningInterviewType
> = ({ candidate }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [interviewDetails, setInterviewDetails] = useState<any[]>([]);

  useEffect(() => {
    getDashboardActiveJob();
  }, []);

  // Function to get stats from API and update the stats value
  const getDashboardActiveJob = async () => {
    setLoading(true);
    const { success, data } = await dashboardApi.activeJobInterviewStats();
    if (success) {
      setInterviewDetails(data);
    } else {
      setInterviewDetails([]);
    }
    setLoading(false);
  };

  if (!candidate || loading) {
    return (
      <div className="card h-100">
        <div className="p-3 border-bottom">
          <h6 className="m-0 fw-semibold heading-clr">
            <Skeleton active title={false} paragraph={{ rows: 1 }} />
          </h6>
        </div>
        <div className="p-3">
          <Skeleton active title={false} paragraph={{ rows: 15 }} />
        </div>
      </div>
    );
  }

  if (interviewDetails.length == 0) {
    return <></>;
  }

  return (
    <>
      <div className="card h-100">
        <div className="p-3 border-bottom">
          <h6 className="m-0 fw-semibold heading-clr">
            Track Interviews Rounds
          </h6>
        </div>
        <div className="p-3">
          <div className="round-steps">
            <ul>
              {interviewDetails.map((interview, index) => (
                <CandidateInterviewCard key={index} interview={interview} />
              ))}
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};
