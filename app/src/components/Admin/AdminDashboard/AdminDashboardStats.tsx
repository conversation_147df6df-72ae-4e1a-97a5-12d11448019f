import React, { useEffect, useState } from "react";
import { adminDashboardApi } from "@src/apis/adminApis";
import { <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON> } from "@mui/x-charts";
import { ButtonGroup, Button } from "react-bootstrap";
import { Skeleton } from "antd";
import { useAppDispatch } from "@src/redux/store";
import { setLoader } from "@src/redux/actions";

import Image from "next/image";

type BarChartDataType = {
  data: number[];
  label: string[];
  title: string;
  xLabel?: string;
  yLabel?: string;
  has_data: boolean;
};
type PieChartDataType = { id: number; value: number; label: string };

type StatsState = {
  bar_stats: BarChartDataType;
  pie_stats: {
    data: Array<PieChartDataType>;
    title: string;
    has_data: boolean;
  };
  stats_type: string;
};

const monthlyLabels: string[] = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];
const defaultMonthlyValue: number[] = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

const defaultStats: StatsState = {
  bar_stats: {
    data: defaultMonthlyValue,
    label: monthlyLabels,
    title: "",
    has_data: false,
  },
  pie_stats: { data: [], title: "", has_data: false },
  stats_type: "monthly",
};

export const AdminDashboardStats = () => {
  const dispatch = useAppDispatch();
  // State for current labels and values
  const [loading, setLoading] = useState<boolean>(false);
  const [state, setStats] = useState<StatsState>(defaultStats);

  useEffect(() => {
    getDashboardStats("monthly", true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle changing the stats type
  const handleDataChange = (type: string) => {
    getDashboardStats(type);
  };

  const getDashboardStats = async (type: string, skelton: boolean = false) => {
    if (skelton) {
      setLoading(true);
    } else {
      dispatch(setLoader(true));
    }
    const { success, data } = await adminDashboardApi.adminDashboardStats({
      type: type,
    });
    if (success) {
      setStats((prev) => ({ ...prev, ...data }));
    }
    if (skelton) {
      setLoading(false);
    } else {
      dispatch(setLoader(false));
    }
  };

  return (
    <>
      <div className="row">
        <div className="col-xl-8 col-lg-12">
          <div className="card p-3">
            <div className="card-header common-heading p-0 mb-3 border-0 bg-white d-flex justify-content-between">
              <h4 className="heading-clr">{state.bar_stats.title}</h4>
              {state.bar_stats.has_data && (
                <ButtonGroup aria-label="stats" className="ms-auto">
                  {["monthly", "weekly", "yearly"].map((type: string) => (
                    <Button
                      key={type}
                      variant="primary"
                      onClick={() => handleDataChange(type)}
                      disabled={state.stats_type === type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </Button>
                  ))}
                </ButtonGroup>
              )}
            </div>
            {loading ? (
              <Skeleton
                active
                paragraph={{ rows: 10 }}
                title={{ width: "100%" }}
                style={{ width: "100%", height: 400 }}
              />
            ) : state.bar_stats.has_data ? (
              <BarChart
                height={300}
                series={[{ data: state.bar_stats.data }]}
                xAxis={[
                  {
                    data: state.bar_stats.label,
                    scaleType: "band",
                    label: state.bar_stats.xLabel,
                  },
                ]}
                yAxis={[
                  {
                    label: state.bar_stats.yLabel,
                    tickLabelInterval: (value) => Number.isInteger(value),
                    valueFormatter: (value) => `${Math.floor(value)}`,
                  },
                ]}
              />
            ) : (
              <div className="no-data text-center">
                <Image
                  width={140}
                  height={140}
                  src="/images/no-data-icon.svg"
                  alt="icon"
                />
                <p className="m-0">
                  Note: Track the number of applicants and their progress on a
                  weekly, monthly, or yearly basis. Stay up-to-date on your
                  hiring process as new data becomes available. Monitor
                  candidate stats for your job postings here.
                </p>
              </div>
            )}
          </div>
        </div>
        <div className="col-xl-4 col-lg-12">
          <div className="card p-3">
            <div className="card-header common-heading p-0 mb-3 border-0 bg-white">
              <h4 className="heading-clr text-center">
                {state.pie_stats.title}
              </h4>
            </div>
            {loading ? (
              <Skeleton
                active
                avatar={{
                  style: {
                    width: 300,
                    margin: 50,
                    height: 300,
                    aspectRatio: 1,
                    borderRadius: "50%",
                  }, // Customize size and shape
                }}
                paragraph={false}
                title={false}
                style={{
                  width: "100%",
                  height: 300,
                  maxWidth: 300,
                  borderRadius: "50%",
                }}
              />
            ) : state.pie_stats.has_data ? (
              <PieChart
                series={[{ data: state.pie_stats.data }]}
                className="pie-chart"
                height={300}
                margin={{ top: 0, bottom: 120, left: 10, right: 10 }}
                slotProps={{
                  legend: {
                    direction: "row",
                    position: { vertical: "bottom", horizontal: "middle" },
                    padding: 0,
                  },
                }}
              />
            ) : (
              <div className="no-data text-center">
                <Image
                  width={140}
                  height={140}
                  src="/images/no-data-icon.svg"
                  alt="icon"
                />
                <p className="m-0 mt-2">
                  Here’s a quick overview of the candidates who have applied for
                  the job posting, along with the candidates who have been
                  shortlisted.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
