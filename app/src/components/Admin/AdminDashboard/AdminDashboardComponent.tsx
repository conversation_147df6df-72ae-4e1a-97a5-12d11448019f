import React, { useEffect, useState } from "react";
import { adminDashboardApi } from "@src/apis/adminApis";
import Image from "next/image";
import { AdminDashboardStats } from "./AdminDashboardStats";

type StatsState = {
  total_users: number;
  total_businesses: number;
  jobs_active: number;
  total_jobs_last_week: number;
  total_candidates: number;
  total_shortlisted: number;
  total_shortlisted_last_week: number;
};

export const AdminDashboardComponent = () => {
  const [stats, setStats] = useState<StatsState>({
    total_users: 0,
    total_businesses: 0,
    jobs_active: 0,
    total_jobs_last_week: 0,
    total_candidates: 0,
    total_shortlisted: 0,
    total_shortlisted_last_week: 0,
  });
  const [time, setTime] = useState<string>("");

  useEffect(() => {
    const options_time: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    };
    const formattedTime = new Date().toLocaleTimeString("en-US", options_time);
    setTime(formattedTime);
    const interval = setInterval(() => {
      const formattedTime = new Date().toLocaleTimeString(
        "en-US",
        options_time,
      );
      setTime(formattedTime);
    }, 1000);

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, []);

  useEffect(() => {
    getDashboardStats();
  }, []);

  // Function to get stats from API and update the stats value
  const getDashboardStats = async () => {
    const { success, data } = await adminDashboardApi.adminDashboardDetails();
    if (success) {
      setStats((prev) => ({ ...prev, ...data }));
    }
  };

  return (
    <>
      <div className="container-fluid p-0">
        <div className="dashboard-pattern-top">
          <div className="d-flex justify-content-between align-items-end mb-4 row-gap-mobile flex-wrap flex-lg-nowrap">
            <div className="welcome-heading">
              <h1 className="text-white">Welcome!</h1>
              <p className="mb-0 text-white">
                Check Out What&apos;s Happening. Here Are Your Key Updates For
                The Day.
              </p>
            </div>
            <p className="m-0 text-white">{time.replace(" at ", " | ")}</p>
          </div>

          <div className="card bg-transparent shadow-none">
            <div className="row row-gap-mobile row-gap-4">
              <div className="col-xl-4 col-lg-12">
                <div className="stat-card stat-card4 d-flex justify-content-between rounded-3 p-3">
                  <div className="stat-left">
                    <p className="mb-0 d-flex align-items-center text-clr">
                      Total Business
                      <span className="ms-3">{stats.total_businesses}</span>
                    </p>
                  </div>
                  <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                    <Image
                      width={50}
                      height={50}
                      src="/images/stat-icon4.svg"
                      alt="icon"
                    />
                  </div>
                </div>
              </div>

              <div className="col-xl-4 col-lg-12">
                <div className="stat-card stat-card5 d-flex justify-content-between rounded-3 p-3">
                  <div className="stat-left">
                    <p className="mb-0 d-flex align-items-center text-clr">
                      Total Business Employees
                      <span className="ms-3">{stats.total_users}</span>
                    </p>
                  </div>
                  <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                    <Image
                      width={50}
                      height={50}
                      src="/images/stat-icon5.svg"
                      alt="icon"
                    />
                  </div>
                </div>
              </div>

              {/* <div className="col-xl-4 col-lg-12">
                <div className="stat-card stat-card1 d-flex justify-content-between p-3">
                  <div className="stat-left">
                    <p className="mb-0 d-flex align-items-center text-clr">
                      Jobs Active
                      <span className="ms-3">{stats.jobs_active}</span>
                    </p>
                    <label className="mb-0 text-clr">
                      <strong className="me-2">
                        {stats.total_jobs_last_week}
                      </strong>
                      jobs added last week
                    </label>
                  </div>
                  <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                    <Image
                      width={50}
                      height={50}
                      src="/images/stat-icon1.svg"
                      alt="icon"
                    />
                  </div>
                </div>
              </div> */}

              <div className="col-xl-4 col-lg-12">
                <div className="stat-card stat-card2 d-flex justify-content-between p-3">
                  <div className="stat-left">
                    <p className="mb-0 d-flex align-items-center text-clr">
                      Total Candidates
                      <span className="ms-3">{stats.total_candidates}</span>
                    </p>
                    <label className="mb-0 text-clr">
                      <strong className="me-2">{stats.total_candidates}</strong>
                      imported to database
                    </label>
                  </div>
                  <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                    <Image
                      width={50}
                      height={50}
                      src="/images/stat-icon2.svg"
                      alt="icon"
                    />
                  </div>
                </div>
              </div>

              {/* <div className="col-xl-4 col-lg-12">
                <div className="stat-card stat-card3 d-flex justify-content-between rounded-3 p-3">
                  <div className="stat-left">
                    <p className="mb-0 d-flex align-items-center text-clr">
                      Total Shortlisted
                      <span className="ms-3">{stats.total_shortlisted}</span>
                    </p>
                    <label className="mb-0 text-clr">
                      <strong className="me-2">
                        {stats.total_shortlisted_last_week}
                      </strong>
                      interviewed last week
                    </label>
                  </div>
                  <div className="stat-right d-flex justify-content-center align-items-center ms-auto">
                    <Image
                      width={50}
                      height={50}
                      src="/images/stat-icon3.svg"
                      alt="icon"
                    />
                  </div>
                </div>
              </div> */}
            </div>
          </div>
        </div>

        <AdminDashboardStats />
      </div>
    </>
  );
};
