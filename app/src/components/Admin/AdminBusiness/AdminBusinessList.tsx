import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Switch } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "@src/redux/reducers";
import { useAppDispatch } from "@src/redux/store";
import { AuthAdminInterface, BusinessInterface } from "@src/redux/interfaces";
import {
  closeDialog,
  openDialog,
  setLoader,
  updateBusinessDetail,
} from "@src/redux/actions";
import DialogComponents from "@src/components/DialogComponents";
import { adminBusinessApi } from "@src/apis/adminApis";
import Dropdown from "react-bootstrap/Dropdown";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import CopyToClipboard from "react-copy-to-clipboard";
import flashMessage from "@src/components/FlashMessage";
import { APP_ROUTE } from "@src/constants";
import { encryptString } from "@src/helper/encryption";

type AdminBusinessListProps = {
  verifyBusiness: (business: BusinessInterface) => void;
  readonly currentUser: AuthAdminInterface;
  fetchData: (page: number, limit: number) => void;
  addNewButton?: React.ReactNode;
};

export function AdminBusinessList({
  fetchData,
  currentUser,
  verifyBusiness,
  addNewButton,
}: AdminBusinessListProps) {
  const dispatch = useAppDispatch();

  const { rows, count, currentPage, limit } = useSelector(
    (state: RootState) => state.business,
  );

  // Handle page change for pagination
  const handlePageChange = async (page: number) => {
    await fetchData(page, limit);
  };

  // Update the status of a business
  const updateBusinessStatus = async (business: BusinessInterface) => {
    await dispatch(setLoader(true));
    const payload = { status: business.status == 1 ? 0 : 1 };
    const { success, ...response } =
      await adminBusinessApi.adminUpdateBusinessStatus(business.id, payload);
    if (success) {
      dispatch(
        updateBusinessDetail({
          id: business.id,
          data: { ...business, ...response.data },
        }),
      );
      flashMessage(response.message, "success");
      dispatch(closeDialog());
    } else {
      flashMessage(response.message, "error");
    }
    await dispatch(setLoader(false));
  };

  // Update the payment status of a business
  const updateBusinessPayment = async (business: BusinessInterface) => {
    await dispatch(setLoader(true));
    const { success, ...response } =
      await adminBusinessApi.adminBusinessPayment(business.id);
    if (success) {
      dispatch(
        updateBusinessDetail({
          id: business.id,
          data: { ...business, ...response.data },
        }),
      );
      flashMessage(response.message, "success");
      dispatch(closeDialog());
    } else {
      flashMessage(response.message, "error");
    }
    await dispatch(setLoader(false));
  };

  // Open confirmation modal for status change
  const openStatusChangeModal = (business: BusinessInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.CONFIRMATION_MODAL,
        options: {
          title: "Business Status Update Confirmation",
          message: (
            <div className="mt-2 mb-2">
              Are you sure you want to update the status of the business? This
              action may affect your current running functionality.
            </div>
          ),
          onConfirm: () => updateBusinessStatus(business),
        },
      }),
    );
  };

  const businessLogin = async (business: BusinessInterface) => {
    if (!business.email_verified) {
      return false;
    }
    dispatch(setLoader(true));
    const { success } = await adminBusinessApi.wildcardLogin(business.id);
    dispatch(setLoader(false));
    if (success) {
      const params = new URLSearchParams({
        login_token: encryptString(`${new Date().toTimeString()}-Super Admin`),
      });
      let url = `${APP_ROUTE.DASHBOARD.subdomainLink(business.subdomain)}?${params}`;
      window.open(url, "_blank");
    } else {
      flashMessage("Failed to open portal", "error");
    }
  };

  // Open edit modal for update business detail
  const openEditModal = (business: BusinessInterface) => {
    dispatch(
      openDialog({
        config: DialogComponents.EDIT_BUSINESS_MODAL,
        options: {
          business: business,
          onUpdate: (business: BusinessInterface) =>
            dispatch(updateBusinessDetail({ id: business.id, data: business })),
        },
      }),
    );
  };

  // Open confirmation modal for payment
  const openPaymentModal = (business: BusinessInterface) => {
    if (business.email_verified) {
      dispatch(
        openDialog({
          config: DialogComponents.BUSINESS_PAYMENT_MODAL,
          options: {
            title: "Business Payment Plan",
            business: business,
            onPayment: () => updateBusinessPayment(business),
          },
        }),
      );
    } else {
      dispatch(
        openDialog({
          config: DialogComponents.CONFIRMATION_MODAL,
          options: {
            title: "Business Verify Alert",
            message: (
              <div className="mt-2 mb-2">
                Before making a payment, please verify your business
              </div>
            ),
          },
        }),
      );
    }
  };

  const hasRows = rows && rows.length > 0;
  const isSuperAdmin = currentUser.user_type == "Super Admin";
  return (
    <>
      <div
        className={`table-responsive business-list ${hasRows ? "" : "no-records"}`}>
        <table
          className="table table-hover dataTable"
          style={{ width: "100%" }}>
          <thead>
            <tr role="row">
              <th className="mw-50px">Sr. No</th>
              {isSuperAdmin && <th className="mw-100px">Employee Email</th>}
              <th className="mw-100px">Business Name</th>
              <th className="mw-100px">Business Email</th>
              <th className="mw-100px">Website</th>
              <th className="mw-80px">Email Verified</th>
              <th className="mw-80px">Status</th>
              <th className="mw-80px">Plans</th>
              <th className="mw-80px">Date & Time</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {hasRows ? (
              rows.map((business: BusinessInterface, index: number) => (
                <tr key={index}>
                  <th>{index + 1 + (currentPage - 1) * limit}</th>
                  {isSuperAdmin && <td>{business.user_email}</td>}
                  <td>{business.name}</td>
                  <td>{business.email}</td>
                  <td>{business.website}</td>
                  <td>
                    {isSuperAdmin ? (
                      !!business.email_verified ? (
                        "Verified"
                      ) : (
                        "Pending Verification"
                      )
                    ) : (
                      <>
                        {!!business.email_verified ? (
                          <>
                            <Button className="btn-theme-secondary" disabled>
                              Verified
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              className="btn-theme"
                              onClick={() => verifyBusiness(business)}>
                              Verify
                            </Button>
                          </>
                        )}
                      </>
                    )}
                  </td>
                  <td>
                    <Switch
                      value={business.status == 1}
                      className="switch-theme"
                      onChange={() => openStatusChangeModal(business)}
                    />
                  </td>
                  <td>
                    {isSuperAdmin ? (
                      !!business.payment_status ? (
                        (business.plan_name ?? "").toUpperCase()
                      ) : (
                        "Pending Payment"
                      )
                    ) : (
                      <>
                        {!!business.payment_status ? (
                          <>
                            <Button className="btn-theme-secondary" disabled>
                              {(business.plan_name ?? "").toUpperCase()}
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              className="btn-theme"
                              onClick={() => openPaymentModal(business)}>
                              Activate
                            </Button>
                          </>
                        )}
                      </>
                    )}
                  </td>
                  <td>
                    {business.created_at?.strftime("%B %d, %Y %I:%M %p") ??
                      "N/A"}
                  </td>
                  <td>
                    <div className="business-action no-arrow">
                      <Dropdown>
                        <Dropdown.Toggle
                          id={`business-dropdown-${business.id}}`}
                          className="text-decoration-none"
                          as="span"
                          type="button"
                          role="button"
                          aria-haspopup="true"
                          aria-expanded="false">
                          <span className="text-black small">
                            <MoreVertIcon />
                          </span>
                        </Dropdown.Toggle>

                        <Dropdown.Menu
                          className="dropdown-menu-right shadow animated--grow-in"
                          aria-labelledby={`business-dropdown-${business.id}}`}>
                          <Dropdown.Item
                            onClick={() => businessLogin(business)}
                            disabled={!business.email_verified}
                            className="no-decoration">
                            Business Portal
                          </Dropdown.Item>
                          <Dropdown.Item
                            onClick={() => openEditModal(business)}
                            className="no-decoration">
                            Edit Detail
                          </Dropdown.Item>
                          <CopyToClipboard
                            text={APP_ROUTE.HOME.subdomainLink(
                              business.subdomain,
                            )}
                            onCopy={() =>
                              flashMessage("Business Url Copied!", "success")
                            }>
                            <Dropdown.Item eventKey="2">
                              Copy Business Url
                            </Dropdown.Item>
                          </CopyToClipboard>
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr className="no-records">
                <td colSpan={isSuperAdmin ? 10 : 9} className="text-center">
                  <span>No Records Found </span>
                  {addNewButton && (
                    <>
                      <br />
                      {addNewButton}
                    </>
                  )}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <Pagination
        className="mt-4"
        current={currentPage}
        total={count}
        pageSize={limit}
        hideOnSinglePage
        onChange={handlePageChange}
      />
    </>
  );
}
