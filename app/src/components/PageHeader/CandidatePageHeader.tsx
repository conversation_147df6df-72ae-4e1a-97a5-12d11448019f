import React from "react";
import { CurrentTime } from "../Common";

interface PageHeaderProps {
  pageTitle: string;
  children?: React.ReactNode;
  breadcrumb?: string[];
}

const CandidatePageHeader: React.FC<PageHeaderProps> = ({
  pageTitle,
  children,
  breadcrumb = [],
}) => {
  return (
    <>
      <div className="container-fluid p-0">
        <div className="dashboard-pattern-top">
          <div className="d-flex justify-content-between align-items-end mb-4 row-gap-mobile flex-wrap flex-lg-nowrap relative">
            <div className="welcome-heading d-flex bredcrubs">
              <h5 className="text-white m-0 pe-2">{pageTitle}</h5>
              {breadcrumb.map((val: string, index: number) => (
                <h6
                  className="m-0 page-head text-white position-relative ps-3"
                  key={index}>
                  {val}
                </h6>
              ))}
            </div>
            <CurrentTime />
          </div>
          {children}
        </div>
      </div>
    </>
  );
};

export default CandidatePageHeader;
