import { NextRequest, NextResponse } from "next/server";

// Util to generate random request ID
function generateRandomString(length: number): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  return Array.from(
    { length },
    () => chars[Math.floor(Math.random() * chars.length)],
  ).join("");
}

export async function middleware(request: NextRequest) {
  const requestId = generateRandomString(20);
  const origin = request.headers.get("origin");

  let subdomain: string | null = null;
  if (origin) {
    const hostname = origin.split("//").pop() ?? "";
    const parts = hostname.split(".");
    if (parts.length > 2) {
      subdomain = parts[0]; // like "app.example.com" → "app"
    }
  }

  const ip =
    request.ip ||
    request.headers.get("x-forwarded-for") ||
    request.headers.get("x-real-ip") ||
    "unknown";

  const method = request.method;
  const path = request.nextUrl.pathname;

  console.log(
    `[${requestId}] ${ip} - ${method} ${path} (subdomain: ${subdomain ?? "none"})`,
  );

  // Note: middleware cannot access response status (no `call_next` in Next.js).
  // You can use custom headers to track request IDs if needed downstream.

  const response = NextResponse.next();

  // Optionally pass requestId/subdomain via headers
  response.headers.set("x-request-id", requestId);
  if (subdomain) response.headers.set("x-subdomain", subdomain);

  return response;
}

export const config = {
  matcher: ["/((?!_next|favicon.ico).*)"], // avoid logging static assets
};
