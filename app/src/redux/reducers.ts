import { combineReducers } from "redux";

import loader from "./slices/loader";
import auth from "./slices/auth";
import business from "./slices/business";
import dialog from "./slices/dialog";
import menu from "./slices/menu";
import department from "./slices/department";
import employee from "./slices/employee";
import opportunity from "./slices/opportunity";
import candidate from "./slices/candidate";
import setting from "./slices/setting";
import interview from "./slices/interview";
import emailTemplate from "./slices/emailTemplate";
import wildcardCandidate from "./slices/wildcardCandidate";
import location from "./slices/location";
import jobRequest from "./slices/jobRequest";
import candidateInterview from "./slices/candidateInterview";
import dashboard from "./slices/dashboard";
import analyticsManagement from "./slices/analyticsManagement";
import ouath from "./slices/ouath";

/**
 * Combines all reducers into a single rootReducer.
 */
const rootReducer = combineReducers({
  loader,
  auth,
  business,
  dialog,
  menu,
  department,
  employee,
  opportunity,
  candidate,
  setting,
  interview,
  location,
  emailTemplate,
  wildcardCandidate,
  jobRequest,
  candidateInterview,
  dashboard,
  analyticsManagement,
  ouath,
});

/**
 * Represents the type of the root state of the Redux store.
 */
export type RootState = ReturnType<typeof rootReducer>;

export default rootReducer;
