export interface KeyPairInterface {
  [key: string]: any;
}

export interface KeyPairStringInterface {
  [key: string]: string;
}

interface IdWithTimestampInterface {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface PagePermissionInterface {
  [key: string]: string[];
}

export interface ForgotPasswordInputInterface {
  email: string;
}

export interface OtpInputInterface {
  otp: string;
}

export interface SuccessMessageInterface {
  success: boolean;
  message: string;
}

export interface ResetPasswordInputInterface {
  password: string;
  confirm_password: string;
}

export interface EmailPasswordInputInterface {
  email: string;
  password: string;
}

export interface RegisterInputInterface {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  dob: string;
}

export interface AuthAdminInterface {
  id: number;
  email: string;
  email_verified: boolean;
  first_name: string;
  last_name: string;
  user_type_id: number;
  user_type: string;
}

export interface AuthEmployeeInterface {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  employee_role_id: number;
  employee_role: string;
  business_id: number;
  business_name: string;
  business_contact: string;
  contact: string;
}

export interface AuthCandidateInterface {
  id: number;
  email: string;
  name: string;
  business_id: number;
}

export interface BusinessInterface {
  id: number;
  name: string;
  subdomain: string;
  user_email?: string;
  website: string;
  email: string;
  email_verified: boolean;
  status: number;
  contact_number: string;
  payment_status: boolean;
  timezone: string;
  plan_name: string;
  created_at: string;
  updated_at: string;
}

export interface DepartmentInterface {
  id: number;
  name: string;
  description: string;
  status: number;
  is_default: number;
  created_by_id?: number;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export interface SubDepartmentInterface extends DepartmentInterface {
  department_id: number;
  department_name: string;
}

export interface EmployeeRoleInterface {
  id: number;
  name: string;
}

export interface EmployeeInterface {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  status: number;
  employee_role_id?: number;
  employee_role_name?: string;
  contact_number?: string;
  created_by_id?: number;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export interface LocationInterface {
  id: number;
  address: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
}

export interface OpportunityInterface {
  id: number;
  title: string;
  designation: string;
  number_of_vacancies: number;
  description: string;
  salary: number;
  experience: number;
  contact_name: string;
  contact_email: string;
  contact_phone: string;
  contact_person_id: number;
  questions: string[];
  status: number;
  department_id: number;
  department_name: string;
  created_by_id?: number;
  created_by_name?: string;
  location_names: string[];
  location_ids: number[];
  opportunity_qualifications: string[];
  responsibilities: string;
  skills?: string;
  created_at: string;
  updated_at: string;
  extra_fields: KeyPairStringInterface;
  job_type: string; // e.g., "Full-time", "Part-time", etc.
}

export interface CandidateNewEducationInterface {
  degree: string;
  university: string;
  grade: string;
  start_date?: string;
  end_date?: string;
}

export type CandidateEducationsInterface = CandidateNewEducationInterface &
  IdWithTimestampInterface;

export interface CandidateNewExperienceInterface {
  name: string;
  employer: string;
  position: string;
  start_date?: string;
  end_date?: string;
  responsibilities: string[]; // Array of strings
  is_current_experience: boolean;
}

export type CandidateExperienceInterface = CandidateNewExperienceInterface &
  IdWithTimestampInterface;

export interface CandidateNewSkillInterface {
  name: string;
  skill_type: string;
  sub_skill_type: string;
}

export type CandidateSkillInterface = CandidateNewSkillInterface &
  IdWithTimestampInterface;

export interface CandidateNewQualificationInterface {
  name: string;
  description: string;
}

export type CandidateQualificationInterface =
  CandidateNewQualificationInterface & IdWithTimestampInterface;

export interface CandidateInterface {
  id: number;
  name: string;
  email: string;
  contact?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  designation: string;
  is_fresher: boolean;
  qualification: string;
  total_experience: number;
  status: number;
  status_name: string;
  opportunity_id: number;
  opportunity_title: string;
  professional_title?: string;
  has_interview?: boolean;
  summary?: string;
  resume_url: string;
  gender?: string;
  dob?: string;
  blacklist: number;
  blacklist_reason: string;
  business_id?: number;
  created_by_id?: number;
  achievements: string[]; // Array of strings
  created_by_name: string; // Assuming this is a string or another related interface
  created_at: string;
  updated_at: string;
  educations_informations: Array<CandidateEducationsInterface>;
  experiences: Array<CandidateExperienceInterface>;
  skills: Array<CandidateSkillInterface>;
  qualifications: Array<CandidateQualificationInterface>;
  scores?: KeyPairInterface;
  job_scores?: KeyPairInterface;
  interviews?: Array<InterviewInterface>;
  total_gap?: number;
  resume_source?: string;
  interview_status?: string;
  interview_job_title?: string;
}

export interface CandidateBasicInterface {
  id: number;
  name: string;
  email: string;
  designation: string;
  contact?: string;
}

export interface CandidateSearchFilters {
  id: number;
  opportunity: string;
  qualification: string;
  experience: number;
  status: string;
  resume_uploaded_after: string;
}

export interface SidebarNodeInterface {
  name: string;
  path: string;
  icon: string | null;
  type: string;
  childrens: SidebarNodeInterface[];
}

export interface NodeSettingInterface {
  id: number;
  name: string;
  path: string;
  icon: string | null;
  type: string;
  permissions: number[];
}

export interface PermissionInterface {
  id: number;
  name: string;
}

export interface DependentPermissionInterface {
  [key: string]: {
    permission_id: number;
    node_id: number;
    dependent_permission_id: number;
    tooltip: string;
  }[];
}

export interface DisabledPermissionInterface {
  [nodeId: number]: {
    [permissionId: number]: string;
  };
}

export interface FeedbackInterface extends IdWithTimestampInterface {
  candidate_id: number;
  candidate_name: string;
  candidate_email: string;

  interview_round: number;
  rating: number;
  feedback: string;
  approved_status: string;

  job_id: number;
  job_title: string;

  created_by_id: number;
  created_by_name: string;
}

export interface CandidateInterviewInterface {
  id: number;
  offer_sent: boolean;

  candidate_id: number;
  candidate_name: string;
  candidate_email: string;
  candidate_designation: string;
  candidate_exist?: boolean;

  interviewer_id: number;
  interviewer_name: string;
  interviewer_email: string;
  interview_at: string;
  interview_round: number;

  status_id: number;
  status_name: string;
  status: string;

  opportunity_id: number;
  opportunity_title: string;

  created_by_id: number;
  created_by_name: string;

  business_name: string;

  created_at: string;
  updated_at: string;
  schedule_interviews: Array<InterviewDetailInterface>;
}

export interface NewInterviewInterface {
  interview_at: string;
  meeting_link: string;
  comment: string;

  candidate_id: number;
  candidate_name: string;
  candidate_email: string;
  candidate_designation: string;

  interviewer_id: number;
  interviewer_name: string;
  interviewer_email: string;
  status: number;
  status_name: string;
  interview_round: number;

  opportunity_id: number;
  opportunity_title: string;

  phone_number: string;

  created_by_id: number;
  created_by_name: string;
  interview_mode_id: number;
  interview_mode_name: string;
  candidate_exist?: boolean;
  latest_feedback?: FeedbackInterface;
  score?: number;
  passing_percentage?: number;
  time_duration?: number;
  show_marks?: boolean;
}

export type InterviewInterface = NewInterviewInterface &
  IdWithTimestampInterface & {
    feedback?: FeedbackInterface;
  };

export type InterviewDetailInterface = NewInterviewInterface &
  IdWithTimestampInterface & {
    opportunity: OpportunityInterface;
    feedback: FeedbackInterface | null;
    interview_location_id: number;
  };

export interface PageDetailInterface {
  title: string | null;
  singular_name: string | null;
  description: string | null;
}

export interface EmailSubjectContentInterface {
  subject: string;
  content: string;
}

export interface DuplicateCandidateInterface extends IdWithTimestampInterface {
  candidate_id: number;
  duplicate_record: boolean;
  email: string;
  resume_url: string;
  resume_source: string;
}

export interface EmailTemplateInterface extends IdWithTimestampInterface {
  template_name: number;
  email_body: string;
}

export interface CandidateDocumentInterface extends IdWithTimestampInterface {
  name: string; // Name or label of the document
  candidate_id: number; // Unique ID of the candidate
  candidate_name: string; // Email of the candidate
  candidate_email: string; // Email of the candidate
  content_type: string; // MIME type of the document (e.g., image/png)
  document_url: string; // URL of the document
}

export interface JobRequestInterface {
  id: number; // Unique ID of the candidate
  name: string; // Email of the candidate
  email: string; // Email of the candidate
  resume_url: string; // URL of the document
  opportunity_id: number;
  opportunity_title: string;
  created_at: string;
  status: number;
  candidate_exist?: boolean;
  has_running_interview?: boolean;
  candidate_id?: number;
  has_job_interview?: boolean;
}

export interface TestCaseInterface {
  input: string;
  output: string;
}

// Define the question type
export interface ScreeningQuestionInterface {
  id: number;
  type: string;
  question: string;
  options: string[];
  language: string;
  starter_code: string;
  test_cases: TestCaseInterface[];
}

export interface DashboardStatsState {
  total_candidates: number;
  total_employees: number;
  jobs_active: number;
  total_candidates_last_week: number;
  total_jobs_last_week: number;
  total_shortlisted: number;
  total_shortlisted_last_week: number;
  total_interviews: number;
  total_interviews_last_week: number;
  total_completed_interviews: number;
  remaining_credits: number;
  total_credits: number;
  total_running_interviews: number;
  has_candidate_permission: boolean;
  has_interview_permission: boolean;
  has_job_permission: boolean;
}

export interface CandidateProfileCommentInterface
  extends IdWithTimestampInterface {
  candidate_id: number;
  employee_id: number;
  employee_name: string;
  comment?: string;
  created_by: string;
}

export interface DocumentRequestInterface extends IdWithTimestampInterface {
  name: string;
  email: string;
  adhar_certificate: boolean;
  pan_certificate: boolean;
  degree_certificate: boolean;
  other_certificate: boolean;
}

export type ScreeningQuestionType = {
  questionNumber: number;
  questionId: string;
  question: string;
  options: string[];
  answer: string | null;
  onSetAnswer: (answer: { questionId: string; answer: string }) => void;
  disable?: boolean;
};

export type InterviewAnswer = {
  questionId: number;
  answer: string;
};

export type DateFilterType = "week" | "month" | "year";
