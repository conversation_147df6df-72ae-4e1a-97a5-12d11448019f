// slices/businessSlice.js

import { createSlice } from "@reduxjs/toolkit";
import { BusinessInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { adminBusinessApi } from "@src/apis/adminApis";

interface BusinessState {
  rows: BusinessInterface[]; // Adjust the type according to your business data structure
  limit: number;
  count: number;
  currentPage: number;
}

const initialState: BusinessState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
};

const businessSlice = createSlice({
  name: "business",
  initialState,
  reducers: {
    setBusinessData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateBusinessDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
  },
});

const { setBusinessData } = businessSlice.actions;

export const { updateBusinessDetail } = businessSlice.actions;

/**
 * Updates the detail of a business in the business data array.
 * @param businessData Array of business data
 * @param payload Payload containing the updated data and the ID of the business
 * @returns Updated array of business data
 */
const UpdateDetailInRow = (businessData: BusinessInterface[], payload: any) => {
  const detail = payload.data;
  const newBusinessData = businessData.map((business: BusinessInterface) => {
    if (business.id == payload.id) {
      return { ...business, ...detail };
    }
    return business;
  });
  return newBusinessData;
};

/**
 * Thunk action for fetching all admin business list.
 * @param params Parameters for fetching the business list
 */
export const getAllAdminBusinessList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await adminBusinessApi.adminBusinessList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setBusinessData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setBusinessData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

export default businessSlice.reducer;
