import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface LoaderInterface {
  loader: boolean;
  loaderText: string;
}

interface ProgressBarPayload {
  showProgressBar: boolean;
  progress: number;
}

const initialState: LoaderInterface = {
  loader: false,
  loaderText: "",
};

const loaderSlice = createSlice({
  name: "loader",
  initialState,
  reducers: {
    setLoader: (state, action: PayloadAction<any>) => {
      state.loader = action.payload;
    },
    setLoaderText: (state, action: PayloadAction<any>) => {
      state.loaderText = action.payload;
    },
  },
});

export const { setLoader, setLoaderText } = loaderSlice.actions;

export default loaderSlice.reducer;
