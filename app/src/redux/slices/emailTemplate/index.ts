// slices/emailTemplateSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { EmailTemplateInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { emailTemplateApi } from "@src/apis/wildcardApis";

interface EmailTemplateState {
  rows: EmailTemplateInterface[]; // Adjust the type according to your emailTemplate data structure
  limit: number;
  count: number;
  currentPage: number;
  emailTemplate: null | EmailTemplateInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: EmailTemplateState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  emailTemplate: null,
  options: [],
};

const emailTemplateSlice = createSlice({
  name: "emailTemplate",
  initialState,
  reducers: {
    setEmailTemplateData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateEmailTemplateDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setEmailTemplateState: (
      state,
      action: PayloadAction<null | EmailTemplateInterface>,
    ) => {
      state.emailTemplate = action.payload;
    },
    setEmailTemplateOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
  },
});

const {
  setEmailTemplateData,
  setEmailTemplateState,
  setEmailTemplateOptionsState,
} = emailTemplateSlice.actions;

export const { updateEmailTemplateDetail } = emailTemplateSlice.actions;

/**
 * Updates the detail of a emailTemplate in the emailTemplate data array.
 * @param emailTemplateData Array of emailTemplate data
 * @param payload Payload containing the updated data and the ID of the emailTemplate
 * @returns Updated array of emailTemplate data
 */
const UpdateDetailInRow = (
  emailTemplateData: EmailTemplateInterface[],
  payload: any,
) => {
  const detail = payload.data;
  const newEmailTemplateData = emailTemplateData.map(
    (emailTemplate: EmailTemplateInterface) => {
      if (emailTemplate.id === payload.id) {
        return { ...emailTemplate, ...detail };
      }
      return emailTemplate;
    },
  );
  return newEmailTemplateData;
};

/**
 * Retrieves all emailTemplate list from the server and dispatches an action to update the emailTemplate data in the Redux store.
 * @param params Parameters for fetching emailTemplate list
 */
export const getAllEmailTemplateList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setEmailTemplateData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setEmailTemplateData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves emailTemplate detail from the server and dispatches an action to update the emailTemplate data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the emailTemplate to fetch.
 * @param callback Optional callback function to execute after fetching the emailTemplate detail.
 */
export const getEmailTemplateDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateDetail(id);
    if (success) {
      await dispatch(setEmailTemplateState(response.data));
    } else {
      await dispatch(setEmailTemplateState(null));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all emailTemplate options from the server and dispatches an action to update the emailTemplate options data in the Redux store.
 * @param params Parameters for fetching emailTemplate list
 */
export const getAllEmailTemplateOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await emailTemplateApi.getEmailTemplateOptions(params);
    if (success) {
      await dispatch(setEmailTemplateOptionsState(response.data));
    } else {
      await dispatch(setEmailTemplateOptionsState([]));
    }
  };

export default emailTemplateSlice.reducer;
