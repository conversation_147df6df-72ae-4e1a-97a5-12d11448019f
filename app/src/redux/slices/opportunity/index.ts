// slices/opportunitySlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { OpportunityInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { opportunityApi } from "@src/apis/wildcardApis";

interface OpportunityState {
  rows: OpportunityInterface[]; // Adjust the type according to your opportunity data structure
  limit: number;
  count: number;
  currentPage: number;
  opportunity: null | OpportunityInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: OpportunityState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  opportunity: null,
  options: [],
};

const opportunitySlice = createSlice({
  name: "opportunity",
  initialState,
  reducers: {
    setOpportunityData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    addOpportunity: (state, action: PayloadAction<OpportunityInterface>) => {
      state.rows.push(action.payload);
      // state.rows.unshift(action.payload); // Add to the beginning
      state.count += 1; // Increase total count by 1
      if (state.rows.length > state.limit) {
        state.rows.pop(); // Remove the last item if rows exceed the limit
      }
    },
    updateOpportunityState: (
      state,
      action: PayloadAction<OpportunityInterface>,
    ) => {
      const index = state.rows.findIndex(
        (opportunity) => opportunity.id === action.payload.id,
      );
      if (index !== -1) {
        state.rows[index] = { ...state.rows[index], ...action.payload };
      }
    },
    setOpportunityState: (
      state,
      action: PayloadAction<null | OpportunityInterface>,
    ) => {
      state.opportunity = action.payload;
    },
    setOpportunityOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
  },
});

const {
  setOpportunityData,
  setOpportunityState,
  addOpportunity,
  updateOpportunityState,
  setOpportunityOptionsState,
} = opportunitySlice.actions;

/**
 * Retrieves all opportunity list from the server and dispatches an action to update the opportunity data in the Redux store.
 * @param params Parameters for fetching opportunity list
 */
export const getAllOpportunityList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await opportunityApi.getOpportunitiesList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setOpportunityData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setOpportunityData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves opportunity detail from the server and dispatches an action to update the opportunity data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the opportunity to fetch.
 * @param callback Optional callback function to execute after fetching the opportunity detail.
 */
export const getOpportunityDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await opportunityApi.getOpportunityDetail(id);
    if (success) {
      await dispatch(setOpportunityState(response.data));
    } else {
      await dispatch(setOpportunityState(null));
    }
    callback && callback(success, response);
  };

/**
 * Creates a new opportunity on the server and dispatches an action to add the opportunity to the Redux store.
 * Optionally, executes a callback function with the response.
 * @param data The data for the new opportunity.
 * @param callback Optional callback function to execute after creating the opportunity.
 */
export const createOpportunity =
  (data: any, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await opportunityApi.createOpportunity(data);
    if (success) {
      await dispatch(addOpportunity(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Updates an existing opportunity on the server and dispatches an action to update the opportunity in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the opportunity to update.
 * @param data The updated data for the opportunity.
 * @param callback Optional callback function to execute after updating the opportunity.
 */
export const updateOpportunity =
  (
    id: number,
    data: any,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await opportunityApi.updateOpportunity(
      id,
      data,
    );
    if (success) {
      await dispatch(updateOpportunityState(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Updates the status of an existing opportunity on the server and dispatches an action to update the opportunity status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the opportunity to update.
 * @param status The updated status for the opportunity.
 * @param callback Optional callback function to execute after updating the opportunity status.
 */
export const updateOpportunityStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await opportunityApi.updateOpportunityStatus(id, { status });
    if (success) {
      await dispatch(updateOpportunityState(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all opportunity options from the server and dispatches an action to update the opportunity options data in the Redux store.
 * @param params Parameters for fetching opportunity list
 */
export const getAllOpportunityOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await opportunityApi.getOpportunityOptions(params);
    if (success) {
      await dispatch(setOpportunityOptionsState(response.data));
    } else {
      await dispatch(setOpportunityOptionsState([]));
    }
  };

export default opportunitySlice.reducer;
