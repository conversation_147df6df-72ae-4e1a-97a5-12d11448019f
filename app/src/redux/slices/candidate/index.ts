// slices/candidateSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { CandidateInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { candidateApi } from "@src/apis/wildcardApis";

interface CandidateState {
  rows: CandidateInterface[]; // Adjust the type according to your candidate data structure
  limit: number;
  count: number;
  currentPage: number;
  candidate: null | CandidateInterface;
  statusOptions: Array<{ value: string | number; label: string }>;
  qualificationOptions: Array<{ value: string | number; label: string }>;
}

const initialState: CandidateState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  candidate: null,
  statusOptions: [],
  qualificationOptions: [],
};

const candidateSlice = createSlice({
  name: "candidate",
  initialState,
  reducers: {
    setCandidateData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    addCandidate: (state, action: PayloadAction<CandidateInterface>) => {
      state.rows.push(action.payload);
      // state.rows.unshift(action.payload); // Add to the beginning
      state.count += 1; // Increase total count by 1
      if (state.rows.length > state.limit) {
        state.rows.pop(); // Remove the last item if rows exceed the limit
      }
    },
    updateCandidateState: (
      state,
      action: PayloadAction<CandidateInterface>,
    ) => {
      const index = state.rows.findIndex(
        (candidate) => candidate.id === action.payload.id,
      );
      if (index !== -1) {
        state.rows[index] = { ...state.rows[index], ...action.payload };
      }
    },
    setCandidateState: (
      state,
      action: PayloadAction<null | CandidateInterface>,
    ) => {
      state.candidate = action.payload;
    },
    setCandidateStatusOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.statusOptions = action.payload ?? [];
    },
    setCandidateQualificationOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.qualificationOptions = action.payload ?? [];
    },
  },
});

const {
  setCandidateData,
  addCandidate,
  updateCandidateState,
  setCandidateStatusOptionsState,
  setCandidateQualificationOptionsState,
} = candidateSlice.actions;

export const { setCandidateState } = candidateSlice.actions;

/**
 * Retrieves all candidate list from the server and dispatches an action to update the candidate data in the Redux store.
 * @param params Parameters for fetching candidate list
 */
export const getAllCandidateList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateApi.getCandidatesList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setCandidateData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setCandidateData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves candidate detail from the server and dispatches an action to update the candidate data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the candidate to fetch.
 * @param callback Optional callback function to execute after fetching the candidate detail.
 */
export const getCandidateDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await candidateApi.getCandidateDetail(id);
    if (success) {
      await dispatch(setCandidateState(response.data));
    } else {
      await dispatch(setCandidateState(null));
    }
    callback && callback(success, response);
  };

/**
 * Updates the status of an existing candidate on the server and dispatches an action to update the candidate status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the candidate to update.
 * @param status The updated status for the candidate.
 * @param callback Optional callback function to execute after updating the candidate status.
 */
export const updateCandidateStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await candidateApi.updateCandidateStatus(
      id,
      { status },
    );
    if (success) {
      await dispatch(updateCandidateState(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all candidate status options from the server and dispatches an action
 * to update the candidate status options data in the Redux store.
 *
 * @param {any} params - Parameters for fetching the candidate status options list.
 * @returns {Function} A thunk function that fetches candidate status options and dispatches actions to update the Redux store.
 */
export const getAllCandidateStatusOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateApi.getCandidateStatusOptions(params);
    if (success) {
      await dispatch(setCandidateStatusOptionsState(response.data));
    } else {
      await dispatch(setCandidateStatusOptionsState([]));
    }
  };

/**
 * Retrieves all candidate qualification options from the server and dispatches an action
 * to update the candidate qualification options data in the Redux store.
 *
 * @param {any} params - Parameters for fetching the candidate qualification options list.
 * @returns {Function} A thunk function that fetches candidate qualification options and dispatches actions to update the Redux store.
 */
export const getAllCandidateQualificationOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateApi.getCandidateQualificationOptions(params);
    if (success) {
      await dispatch(setCandidateQualificationOptionsState(response.data));
    } else {
      await dispatch(setCandidateQualificationOptionsState([]));
    }
  };

export default candidateSlice.reducer;
