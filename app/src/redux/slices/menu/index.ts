import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface MenuInterface {
  sidebarOpen: boolean;
}

const initialState: MenuInterface = {
  sidebarOpen: true,
};

const themeSlice = createSlice({
  name: "theme",
  initialState,
  reducers: {
    setSideBar: (state, action: PayloadAction<any>) => {
      state.sidebarOpen = action.payload;
    },
    toggleSideBar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
  },
});

export const { setSideBar, toggleSideBar } = themeSlice.actions;

export default themeSlice.reducer;
