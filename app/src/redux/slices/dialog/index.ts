import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Define a type for the slice state
interface DialogState {
  open: boolean;
  properties: any;
}

interface PropertyPayload {
  config: object;
  options: object;
}

type PropertyConfigPayload = object;

// Define the initial state using that type
const initialState: DialogState = {
  open: false,
  properties: null,
};

export const dialogSlice = createSlice({
  name: "dialog",
  // `createSlice` will infer the state type from the `initialState` argument
  initialState,
  reducers: {
    openDialog: (state, action: PayloadAction<PropertyPayload>) => {
      state.open = true;
      state.properties = {
        ...action.payload.config,
        ...action.payload.options,
      };
    },
    closeDialog: (state) => {
      state.open = false;
      state.properties = null;
    },
    updateDialogProperties: (
      state,
      action: PayloadAction<PropertyConfigPayload>,
    ) => {
      state.properties = {
        ...state.properties,
        ...(action.payload || {}),
      };
    },
  },
});

export const { openDialog, closeDialog, updateDialogProperties } =
  dialogSlice.actions;

export default dialogSlice.reducer;
