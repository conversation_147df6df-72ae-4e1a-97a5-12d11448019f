// slices/interviewSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import {
  CandidateInterviewInterface,
  InterviewInterface,
} from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { candidateInterview<PERSON>pi } from "@src/apis/wildcardApis";

interface InterviewDetailInterface {
  interview: CandidateInterviewInterface;
  records: Array<InterviewInterface>;
}
interface InterviewState {
  rows: CandidateInterviewInterface[]; // Adjust the type according to your interview data structure
  limit: number;
  count: number;
  currentPage: number;
  detail: InterviewDetailInterface | null;
}

const initialState: InterviewState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  detail: null,
};

const interviewSlice = createSlice({
  name: "interview",
  initialState,
  reducers: {
    setInterviewData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    setInterviewDetail: (
      state,
      action: PayloadAction<null | InterviewDetailInterface>,
    ) => {
      state.detail = action.payload;
    },
    updateInterviewState: (
      state,
      action: PayloadAction<CandidateInterviewInterface>,
    ) => {
      const index = state.rows.findIndex(
        (interview) => interview.id === action.payload.id,
      );
      if (index !== -1) {
        state.rows[index] = { ...state.rows[index], ...action.payload };
      }
    },
    updateCandidateInterviewState: (
      state,
      action: PayloadAction<CandidateInterviewInterface>,
    ) => {
      if (state.detail && state.detail.interview.id == action.payload.id) {
        state.detail = {
          ...state.detail,
          interview: { ...state.detail.interview, ...action.payload },
        } as InterviewDetailInterface;
      }
    },
    updateCandidateInterviewRecordState: (
      state,
      action: PayloadAction<InterviewInterface>,
    ) => {
      let records = (state.detail?.records ?? []) as Array<InterviewInterface>;
      const index = records.findIndex(
        (interview) => interview.id === action.payload.id,
      );
      if (index !== -1) {
        records[index] = { ...records[index], ...action.payload };
      }
      state.detail = {
        ...state.detail,
        records: records,
      } as InterviewDetailInterface;
    },
  },
});

const {
  setInterviewData,
  setInterviewDetail,
  updateInterviewState,
  updateCandidateInterviewState,
} = interviewSlice.actions;

export const { updateCandidateInterviewRecordState } = interviewSlice.actions;

/**
 * Retrieves all interview list from the server and dispatches an action to update the interview data in the Redux store.
 * @param params Parameters for fetching interview list
 */
export const getAllCandidateInterviewList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateInterviewApi.getInterviewsList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setInterviewData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setInterviewData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Updates the status of an existing interview on the server and dispatches an action to update the interview status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the interview to update.
 * @param status The updated status for the interview.
 * @param callback Optional callback function to execute after updating the interview status.
 */
export const getCandidateInterviewDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    await dispatch(setInterviewDetail(null));
    const { success, ...response } =
      await candidateInterviewApi.getInterviewDetails(id);
    if (success) {
      const { interview, records } = response.data;
      await dispatch(setInterviewDetail({ interview, records }));
    } else {
      await dispatch(setInterviewDetail(null));
    }
    callback && callback(success, response);
  };

/**
 * Updates the status of an existing interview on the server and dispatches an action to update the interview status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the interview to update.
 * @param status The updated status for the interview.
 * @param callback Optional callback function to execute after updating the interview status.
 */
export const updateCandidateInterviewStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await candidateInterviewApi.updateInterviewStatus(id, { status });
    if (success) {
      await dispatch(updateInterviewState(response.data));
      await dispatch(updateCandidateInterviewState(response.data));
    }
    callback && callback(success, response);
  };

export default interviewSlice.reducer;
