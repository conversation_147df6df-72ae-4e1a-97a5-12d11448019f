// slices/candidateSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { CandidateInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { profileApi } from "@src/apis/wildcardCandidateApis";

interface CandidateState {
  candidate: null | CandidateInterface;
}

const initialState: CandidateState = {
  candidate: null,
};

const candidateSlice = createSlice({
  name: "wildcardCandidate",
  initialState,
  reducers: {
    setCandidateData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    setCandidateState: (
      state,
      action: PayloadAction<null | CandidateInterface>,
    ) => {
      state.candidate = action.payload;
    },
  },
});

const { setCandidateData, setCandidateState } = candidateSlice.actions;

/**
 * Retrieves candidate detail from the server and dispatches an action to update the candidate data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param callback Optional callback function to execute after fetching the candidate detail.
 */
export const getCandidateSelfDetail =
  (callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await profileApi.getSelfDetail();
    if (success) {
      await dispatch(setCandidateState(response.data));
    } else {
      await dispatch(setCandidateState(null));
    }
    callback && callback(success, response);
  };

export default candidateSlice.reducer;
