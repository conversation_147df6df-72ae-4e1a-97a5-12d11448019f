// slices/interviewSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { InterviewInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { interviewApi } from "@src/apis/wildcardApis";
import { updateCandidateInterviewRecordState } from "../candidateInterview";

interface InterviewDataState {
  rows: InterviewInterface[]; // Adjust the type according to your interview data structure
  limit: number;
  count: number;
  currentPage: number;
  interview: null | InterviewInterface;
}
interface InterviewState extends InterviewDataState {
  histories: InterviewDataState;
}

const initialState: InterviewState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  interview: null,
  histories: {
    interview: null,
    rows: [],
    limit: 10,
    count: 0,
    currentPage: 1,
  },
};

const interviewSlice = createSlice({
  name: "interview",
  initialState,
  reducers: {
    setInterviewData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    addInterview: (state, action: PayloadAction<InterviewInterface>) => {
      state.rows.push(action.payload);
      // state.rows.unshift(action.payload); // Add to the beginning
      state.count += 1; // Increase total count by 1
      if (state.rows.length > state.limit) {
        state.rows.pop(); // Remove the last item if rows exceed the limit
      }
    },
    updateInterviewState: (
      state,
      action: PayloadAction<InterviewInterface>,
    ) => {
      const index = state.rows.findIndex(
        (interview) => interview.id === action.payload.id,
      );
      if (index !== -1) {
        state.rows[index] = { ...state.rows[index], ...action.payload };
      }
    },
    setInterviewState: (
      state,
      action: PayloadAction<null | InterviewInterface>,
    ) => {
      state.interview = action.payload;
    },
    setInterviewHistoryState: (
      state,
      action: PayloadAction<InterviewDataState>,
    ) => {
      state.histories = action.payload;
    },
  },
});

const {
  setInterviewData,
  setInterviewState,
  addInterview,
  updateInterviewState,
  setInterviewHistoryState,
} = interviewSlice.actions;

/**
 * Retrieves all interview list from the server and dispatches an action to update the interview data in the Redux store.
 * @param params Parameters for fetching interview list
 */
export const getAllInterviewList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await interviewApi.getInterviewsList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setInterviewData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setInterviewData({
          rows: [],
          limit: params.limit || 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves all interview list from the server and dispatches an action to update the interview data in the Redux store.
 * @param params Parameters for fetching interview list
 */
export const getInterviewHistories =
  (id: number, params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } = await interviewApi.getInterviewHistoryList(
      id,
      params,
    );
    if (success) {
      const { page, rows, count, limit } = response.data;
      const { interview } = response.meta;
      await dispatch(
        setInterviewHistoryState({
          interview: interview,
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setInterviewHistoryState({
          interview: null,
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves interview detail from the server and dispatches an action to update the interview data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the interview to fetch.
 * @param callback Optional callback function to execute after fetching the interview detail.
 */
export const getInterviewDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await interviewApi.getInterviewDetail(id);
    if (success) {
      await dispatch(setInterviewState(response.data));
    } else {
      await dispatch(setInterviewState(null));
    }
    callback && callback(success, response);
  };

/**
 * Creates a new interview on the server and dispatches an action to add the interview to the Redux store.
 * Optionally, executes a callback function with the response.
 * @param data The data for the new interview.
 * @param callback Optional callback function to execute after creating the interview.
 */
export const createInterview =
  (data: any, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await interviewApi.createInterview(data);
    if (success) {
      await dispatch(addInterview(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Updates an existing interview on the server and dispatches an action to update the interview in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the interview to update.
 * @param data The updated data for the interview.
 * @param callback Optional callback function to execute after updating the interview.
 */
export const updateInterview =
  (
    id: number,
    data: any,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await interviewApi.updateInterview(
      id,
      data,
    );
    if (success) {
      await dispatch(updateInterviewState(response.data));
    }
    callback && callback(success, response);
  };

/**
 * Updates the status of an existing interview on the server and dispatches an action to update the interview status in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the interview to update.
 * @param status The updated status for the interview.
 * @param callback Optional callback function to execute after updating the interview status.
 */
export const updateInterviewStatus =
  (
    id: number,
    status: number,
    callback?: (success: boolean, response: any) => void,
  ) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } = await interviewApi.updateInterviewStatus(
      id,
      { status },
    );
    if (success) {
      await dispatch(updateInterviewState(response.data));
      await dispatch(updateCandidateInterviewRecordState(response.data));
    }
    callback && callback(success, response);
  };

export default interviewSlice.reducer;
