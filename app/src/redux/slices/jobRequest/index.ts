// slices/jobRequestSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { JobRequestInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { jobRequestApi } from "@src/apis/wildcardApis";

interface JobRequestState {
  rows: JobRequestInterface[]; // Adjust the type according to your job request data structure
  limit: number;
  count: number;
  currentPage: number;
  jobRequest: null | JobRequestInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: JobRequestState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  jobRequest: null,
  options: [],
};

const jobRequestSlice = createSlice({
  name: "jobRequest",
  initialState,
  reducers: {
    setJobRequestData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateJobRequestDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setJobRequestState: (
      state,
      action: PayloadAction<null | JobRequestInterface>,
    ) => {
      state.jobRequest = action.payload;
    },
    setJobRequestOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
  },
});

const { setJobRequestData, setJobRequestState } = jobRequestSlice.actions;

export const { updateJobRequestDetail } = jobRequestSlice.actions;

/**
 * Updates the detail of a job request in the job request data array.
 * @param jobRequestData Array of job request data
 * @param payload Payload containing the updated data and the ID of the jobRequest
 * @returns Updated array of job request data
 */
const UpdateDetailInRow = (
  jobRequestData: JobRequestInterface[],
  payload: any,
) => {
  const detail = payload.data;
  const newJobRequestData = jobRequestData.map(
    (jobRequest: JobRequestInterface) => {
      if (jobRequest.id === payload.id) {
        return { ...jobRequest, ...detail };
      }
      return jobRequest;
    },
  );
  return newJobRequestData;
};

/**
 * Retrieves all job request list from the server and dispatches an action to update the job request data in the Redux store.
 * @param params Parameters for fetching job request list
 */
export const getAllJobRequestList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await jobRequestApi.getJobRequestList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setJobRequestData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setJobRequestData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves job request detail from the server and dispatches an action to update the job request data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the job request to fetch.
 * @param callback Optional callback function to execute after fetching the job request detail.
 */
export const getJobRequestDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await jobRequestApi.getJobRequestDetail(id);
    if (success) {
      await dispatch(setJobRequestState(response.data));
    } else {
      await dispatch(setJobRequestState(null));
    }
    callback && callback(success, response);
  };

export default jobRequestSlice.reducer;
