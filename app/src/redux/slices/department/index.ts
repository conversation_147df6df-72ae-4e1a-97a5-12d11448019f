// slices/departmentSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { DepartmentInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { departmentApi } from "@src/apis/wildcardApis";

interface DepartmentState {
  rows: DepartmentInterface[]; // Adjust the type according to your department data structure
  limit: number;
  count: number;
  currentPage: number;
  department: null | DepartmentInterface;
  options: Array<{ value: string | number; label: string }>;
}

const initialState: DepartmentState = {
  rows: [],
  limit: 10,
  count: 0,
  currentPage: 1,
  department: null,
  options: [],
};

const departmentSlice = createSlice({
  name: "department",
  initialState,
  reducers: {
    setDepartmentData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateDepartmentDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setDepartmentState: (
      state,
      action: PayloadAction<null | DepartmentInterface>,
    ) => {
      state.department = action.payload;
    },
    setDepartmentOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
  },
});

const { setDepartmentData, setDepartmentState, setDepartmentOptionsState } =
  departmentSlice.actions;

export const { updateDepartmentDetail } = departmentSlice.actions;

/**
 * Updates the detail of a department in the department data array.
 * @param departmentData Array of department data
 * @param payload Payload containing the updated data and the ID of the department
 * @returns Updated array of department data
 */
const UpdateDetailInRow = (
  departmentData: DepartmentInterface[],
  payload: any,
) => {
  const detail = payload.data;
  const newDepartmentData = departmentData.map(
    (department: DepartmentInterface) => {
      if (department.id === payload.id) {
        return { ...department, ...detail };
      }
      return department;
    },
  );
  return newDepartmentData;
};

/**
 * Retrieves all department list from the server and dispatches an action to update the department data in the Redux store.
 * @param params Parameters for fetching department list
 */
export const getAllDepartmentList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await departmentApi.getCategoriesList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setDepartmentData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setDepartmentData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves department detail from the server and dispatches an action to update the department data in the Redux store.
 * Optionally, executes a callback function with the response.
 * @param id The ID of the department to fetch.
 * @param callback Optional callback function to execute after fetching the department detail.
 */
export const getDepartmentDetail =
  (id: number, callback?: (success: boolean, response: any) => void) =>
  async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await departmentApi.getDepartmentDetail(id);
    if (success) {
      await dispatch(setDepartmentState(response.data));
    } else {
      await dispatch(setDepartmentState(null));
    }
    callback && callback(success, response);
  };

/**
 * Retrieves all department options from the server and dispatches an action to update the department options data in the Redux store.
 * @param params Parameters for fetching department list
 */
export const getAllDepartmentOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await departmentApi.getCategoriesOptions(params);
    if (success) {
      await dispatch(setDepartmentOptionsState(response.data));
    } else {
      await dispatch(setDepartmentOptionsState([]));
    }
  };

export default departmentSlice.reducer;
