import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { AppDispatch } from "@src/redux/store";
import { candidateApi } from "@src/apis/wildcardApis";

interface RegisteredCandidateOptionalState {
  rows?: any[];
  count?: number;
  filter?: "week" | "month" | "year";
  page?: number;
  limit?: number;
}

interface RegisteredCandidateState {
  rows: any[];
  count: number;
  filter: "week" | "month" | "year";
  page: number;
  limit: number;
}
// Optional: Define a type for better structure
interface AnalyticsManagementState {
  registeredCandidates: RegisteredCandidateState;
}

const initialState: AnalyticsManagementState = {
  registeredCandidates: {
    rows: [],
    count: 0,
    filter: "week",
    page: 1,
    limit: 10,
  },
};

const hiringManagementSlice = createSlice({
  name: "hiringManagements",
  initialState,
  reducers: {
    setRegistedCandidateData: (
      state,
      action: PayloadAction<RegisteredCandidateOptionalState>,
    ) => {
      state.registeredCandidates = {
        ...state.registeredCandidates,
        ...action.payload,
      };
    },
  },
});

export const { setRegistedCandidateData } = hiringManagementSlice.actions;

/**
 * Fetch registered candidates by filter and id
 * @param id - Employee ID
 * @param currentDateTime - Current date (this stays the same, only filter changes)
 */
export const fetchRegisteredCandidates =
  (
    currentDateTime: Date,
    filter: "week" | "month" | "year",
    page: number,
    limit: number,
  ) =>
  async (dispatch: AppDispatch) => {
    // Format current date to API format
    const formatToAPIDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
    };

    const params: Record<string, any> = { page, limit };

    // Send the current date for all filters - let API handle the filtering logic
    const formattedCurrentDate = formatToAPIDate(currentDateTime);
    if (filter === "week") {
      params.current_week_date = formattedCurrentDate;
      console.log(
        "This Week Filter - Sending current date:",
        params.current_week_date,
      );
    } else if (filter === "month") {
      params.current_month_date = formattedCurrentDate;
      console.log(
        "Current Month Filter - Sending current date:",
        params.current_month_date,
      );
    } else if (filter === "year") {
      params.current_year_date = formattedCurrentDate;
      console.log(
        "Current Year Filter - Sending current date:",
        params.current_year_date,
      );
    }

    console.log("API Payload Params:", params);
    console.log("Filter Applied:", filter);
    console.log("Reference DateTime:", currentDateTime);

    try {
      const { success, data } =
        await candidateApi.getRegisteredCandidate(params);
      if (success) {
        dispatch(
          setRegistedCandidateData({
            rows: data.rows || [],
            count: data.count || 0,
            filter,
            page,
            limit,
          }),
        );
      } else {
        dispatch(setRegistedCandidateData({ rows: [], count: 0 }));
      }
    } catch (error) {
      console.error("Error fetching registered candidates:", error);
      dispatch(setRegistedCandidateData({ rows: [], count: 0 }));
    }
  };

export default hiringManagementSlice.reducer;
