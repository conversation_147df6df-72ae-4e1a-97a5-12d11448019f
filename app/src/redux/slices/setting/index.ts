// slices/nodeSlice.js

import { createSlice } from "@reduxjs/toolkit";
import {
  DependentPermissionInterface,
  DisabledPermissionInterface,
  NodeSettingInterface,
  PermissionInterface,
} from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { settingApi } from "@src/apis/commonApis";

interface SettingState {
  nodes: NodeSettingInterface[]; // Adjust the type according to your setting data structure
  permissions: PermissionInterface[]; // Adjust the type according to your setting data structure
  dependentPermissions: DependentPermissionInterface;
  disabledPermissions: DisabledPermissionInterface;
}

const initialState: SettingState = {
  nodes: [],
  permissions: [],
  dependentPermissions: {},
  disabledPermissions: {},
};

const settingSlice = createSlice({
  name: "setting",
  initialState,
  reducers: {
    setSettingData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    setPermissionList: (state, action) => {
      state.permissions = action.payload;
    },
    setDependentPermissions: (state, action) => {
      state.dependentPermissions = action.payload || {};
    },
    setDisabledPermissions: (state, action) => {
      state.disabledPermissions = action.payload || {};
    },
    setNodesList: (state, action) => {
      state.nodes = action.payload;
    },
  },
});

const {
  setPermissionList,
  setNodesList,
  setDependentPermissions,
  setDisabledPermissions,
} = settingSlice.actions;

/**
 * Dispatches an action to set the permissions list in the Redux store.
 * @param {boolean} admin - Indicates whether the user is an admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Function} - A thunk action that dispatches either the permissions list or an empty array.
 */
export const getAllPermissionList =
  (admin: boolean, params?: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } = await settingApi.getPermissionsList(
      admin,
      params,
    );
    if (success) {
      const { permission_rows, dependent_permissions, disabled_permissions } =
        response.data;
      await dispatch(setPermissionList(permission_rows));
      await dispatch(setDependentPermissions(dependent_permissions));
      await dispatch(setDisabledPermissions(disabled_permissions));
    } else {
      await dispatch(setPermissionList([]));
      await dispatch(setDependentPermissions({}));
      await dispatch(setDisabledPermissions({}));
    }
  };

/**
 * Dispatches an action to set the nodes list in the Redux store.
 * @param {boolean} admin - Indicates whether the user is an admin.
 * @param {any} params - The parameters to be sent in the API request.
 * @returns {Function} - A thunk action that dispatches either the nodes list or an empty array.
 */
export const getAllNodesList =
  (admin: boolean, params?: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } = await settingApi.getNodesList(
      admin,
      params,
    );
    if (success) {
      await dispatch(setNodesList(response.data));
    } else {
      await dispatch(setNodesList([]));
    }
  };

export default settingSlice.reducer;
