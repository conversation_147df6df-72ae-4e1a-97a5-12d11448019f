import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface OauthTokenInterface {
  token: string;
  expires_at: number;
}

interface OauthInterface {
  google_auth: OauthTokenInterface | null;
}

const initialState: OauthInterface = {
  google_auth: null,
};

const oauthSlice = createSlice({
  name: "oauth",
  initialState,
  reducers: {
    setGoogleAuthToken: (
      state,
      action: PayloadAction<OauthTokenInterface | null>,
    ) => {
      state.google_auth = action.payload;
    },
    clearOauthState: (state) => {
      state = { ...initialState };
      return state;
    },
  },
});

export const { setGoogleAuthToken, clearOauthState } = oauthSlice.actions;

export default oauthSlice.reducer;
