// slices/dashboardSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { DashboardStatsState } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { dashboardApi } from "@src/apis/wildcardApis";

interface DashboardStats {
  stats: DashboardStatsState;
}

const defaultStats: DashboardStatsState = {
  total_candidates: 0,
  total_employees: 0,
  jobs_active: 0,
  total_candidates_last_week: 0,
  total_jobs_last_week: 0,
  total_shortlisted: 0,
  total_shortlisted_last_week: 0,
  total_interviews: 0,
  total_interviews_last_week: 0,
  total_completed_interviews: 0,
  total_running_interviews: 0,
  has_candidate_permission: false,
  has_interview_permission: false,
  has_job_permission: false,
  remaining_credits: 0,
  total_credits: 0,
};
const initialState: DashboardStats = {
  stats: defaultStats,
};

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setStats: (state, action: PayloadAction<DashboardStatsState>) => {
      state.stats = action.payload;
    },
  },
});

const { setStats } = dashboardSlice.actions;

/**
 * Retrieves all dashboard list from the server and dispatches an action to update the dashboard data in the Redux store.
 */
export const getDashboardStats = () => async (dispatch: AppDispatch) => {
  const { success, ...response } = await dashboardApi.dashboardDetails();
  if (success) {
    await dispatch(setStats(response.data));
  } else {
    await dispatch(setStats(defaultStats));
  }
};

export default dashboardSlice.reducer;
