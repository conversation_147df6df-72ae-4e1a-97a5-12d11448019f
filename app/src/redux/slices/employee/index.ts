// slices/employeeSlice.js

import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { EmployeeInterface } from "@src/redux/interfaces";
import { AppDispatch } from "@src/redux/store";
import { employeeManagementApi } from "@src/apis/wildcardApis";

interface EmployeeState {
  rows: EmployeeInterface[]; // Adjust the type according to your employee data structure
  limit: number;
  count: number;
  currentPage: number;
  options: Array<{ value: string | number; label: string }>;
  rolesOption: Array<{ value: string | number; label: string }>;
}

const initialState: EmployeeState = {
  rows: [],
  limit: 9, //default 9 employee per page
  count: 0,
  currentPage: 1,
  options: [],
  rolesOption: [],
};

const employeeSlice = createSlice({
  name: "employee",
  initialState,
  reducers: {
    setEmployeeData: (state, action) => {
      state = { ...state, ...action.payload };
      return state;
    },
    updateEmployeeDetail: (state, action) => {
      state.rows = UpdateDetailInRow(state.rows, action.payload);
    },
    setEmployeeOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.options = action.payload ?? [];
    },
    setEmployeeRolesOptionsState: (
      state,
      action: PayloadAction<Array<{ value: string | number; label: string }>>,
    ) => {
      state.rolesOption = action.payload ?? [];
    },
  },
});

const {
  setEmployeeData,
  setEmployeeOptionsState,
  setEmployeeRolesOptionsState,
} = employeeSlice.actions;

export const { updateEmployeeDetail } = employeeSlice.actions;

/**
 * Updates the detail of a employee in the employee data array.
 * @param employeeData Array of employee data
 * @param payload Payload containing the updated data and the ID of the employee
 * @returns Updated array of employee data
 */
const UpdateDetailInRow = (employeeData: EmployeeInterface[], payload: any) => {
  const detail = payload.data;
  const newEmployeeData = employeeData.map((employee: EmployeeInterface) => {
    if (employee.id == payload.id) {
      return { ...employee, ...detail };
    }
    return employee;
  });
  return newEmployeeData;
};

/**
 * Thunk action for fetching all wildcard employee list.
 * @param params Parameters for fetching the employee list
 */
export const getAllWildcardEmployeeList =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await employeeManagementApi.getEmployeesList(params);
    if (success) {
      const { page, rows, count, limit } = response.data;
      await dispatch(
        setEmployeeData({
          rows: rows,
          limit: limit,
          count: count,
          currentPage: page,
        }),
      );
    } else {
      await dispatch(
        setEmployeeData({
          rows: [],
          limit: 10,
          count: 0,
          currentPage: 1,
        }),
      );
    }
  };

/**
 * Retrieves all employee options from the server and dispatches an action to update the employee options data in the Redux store.
 * @param params Parameters for fetching employee list
 */
export const getAllEmployeeOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await employeeManagementApi.getEmployeesOptions(params);
    if (success) {
      await dispatch(setEmployeeOptionsState(response.data));
    } else {
      await dispatch(setEmployeeOptionsState([]));
    }
  };

/**
 * Retrieves all employee options from the server and dispatches an action to update the employee roles options data in the Redux store.
 * @param params Parameters for fetching employee list
 */
export const getAllEmployeeRolesOptions =
  (params: any) => async (dispatch: AppDispatch) => {
    const { success, ...response } =
      await employeeManagementApi.getEmployeesRolesOption(params);
    if (success) {
      await dispatch(setEmployeeRolesOptionsState(response.data));
    } else {
      await dispatch(setEmployeeRolesOptionsState([]));
    }
  };

export default employeeSlice.reducer;
