import { configureStore } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import rootReducer from "./reducers";
import { persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { HYDRATE, createWrapper } from "next-redux-wrapper";
import { encryptTransform } from "redux-persist-transform-encrypt";
import { REDUX_SECRET_KEY } from "@src/constants";

// Configuration for encrypting persisted state
const encryptionConfig = {
  secretKey: REDUX_SECRET_KEY,
  onError: (error: any) => {
    console.error("Encryption error:", error);
  },
};

// Create encryption transform
const encryptedTransform = encryptTransform(encryptionConfig);

// Configuration for persisting state
const persistConfig = {
  key: "root",
  storage: storage,
  transforms: [encryptedTransform],
  whitelist: ["auth", "ouath"], // Only persist the "auth" and "ouath" slice of the state
};

// Master reducer function to handle hydration and root reducer
const masterReducer = (state: any, action: any) => {
  if (action.type === HYDRATE) {
    // Merge server state with client state, but only preserve the "auth" slice
    const nextState = { ...action.payload, auth: state.auth };
    return nextState;
  } else {
    // For all other actions, use the root reducer
    return rootReducer(state, action);
  }
};

// Create persisted reducer using persistConfig
const persistedReducer = persistReducer(persistConfig, masterReducer);

// Configure the Redux store
const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }), // Disable serializable check for redux-persist
});

// Custom hook to access dispatch function
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();

// Export the Redux store
export default store;

// Create and export the redux wrapper for Next.js
export const wrapper = createWrapper(() => store);
