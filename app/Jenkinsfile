pipeline {
    agent any

    environment {
        DOCKER_CREDENTIALS_ID = 'tlgt-git' // <PERSON> Docker credentials ID
        IMAGE_NAME = 'ghcr.io/talentelgia-technologies-pvt-ltd/ats-frontend'
        K8S_NAMESPACE = 'ats' // Namespace in Kubernetes
        SONARQUBE_SERVER = 'sonarqube-server'
        SONARSCANNER = 'sonarqube-scanner'
        STRIPE_PUBLISHABLE_KEY = credentials('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY')
    }

    stages {
        stage('Checkout Code') {
            steps {
                // Checkout the code from your Git repository
                git url: 'https://github.com/Talentelgia-Technologies-Pvt-Ltd/ATS.git', credentialsId: 'tlgt-git', branch: 'master'
            }
        }

        stage('SonarQube Analysis') {
            environment {
                scannerHome = tool name: "${SONARSCANNER}"
            }
            steps {
                script {
                    def executable = "${scannerHome}/bin/sonar-scanner"
                    
                    withCredentials([string(credentialsId: 'jenkins-sonar-token', variable: 'SONAR_TOKEN')]) {
                        withSonarQubeEnv("${SONARQUBE_SERVER}") {
                            sh """
                                #!/bin/bash
                                ${executable} \\
                                -Dsonar.projectKey=ats-frontend \\
                                -Dsonar.projectName=ATS_Frontend \\
                                -Dsonar.projectVersion=1.0 \\
                                -Dsonar.sources=app \\
                                -Dsonar.exclusions='**/node_modules/**/*,**/build/**/*' \\
                                -Dsonar.sourceEncoding=UTF-8 \\
                                -Dsonar.host.url=https://sonar.teamtalentelgia.com/ \\
                                -Dsonar.login=${SONAR_TOKEN} \\
                                -Dsonar.language=js \\
                                -Dsonar.exclusions=**/*.jsx,**/*.tsx \\
                                -Dsonar.javascript.lcov.reportPaths=app/coverage/lcov-report/index.html
                            """
                        }
                    }
                }
            }
        }

        stage('Quality Gate') {
            steps {
                script {
                    timeout(time: 1, unit: 'HOURS') {
                        def qualityGate = waitForQualityGate()
                        if (qualityGate.status != 'OK') {
                            error "Pipeline aborted due to quality gate failure: ${qualityGate.status}"
                        }
                    }
                }
            }
        }

stage('Build Image') {
    steps {
        script {
            try {
                // Add the context '.' to indicate the current directory
            dockerImage = docker.build IMAGE_NAME, "--build-arg NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY} app"
            } catch (Exception e) {
                error "Docker build failed: ${e.message}"
            }
        }
    }
}

        stage('Push Image') {
            steps {
                script {
                    // Push the Docker image with the build number tag
                    docker.withRegistry('https://ghcr.io', DOCKER_CREDENTIALS_ID) {
                        dockerImage.push("${env.BUILD_NUMBER}")
                    }
                }
            }
        }

        stage('Trigger ManifestUpdate') {
            steps {
                echo "triggering updatemanifestjob"
                build job: 'ATS-Manifest-Frontend', parameters: [string(name: 'DOCKERTAG', value: env.BUILD_NUMBER)]
            }
        }
    }

    // Optionally, you can add post actions here if needed
}
