version: "3.8"
services:
    ats-app:
        build:
            context: ./
            dockerfile: ./app/Dockerfile.dev
            target: dev
        image: ats-app
        container_name: ats-app
        command: pnpm dev
        ports:
            - "3081:3000"
        volumes:
            - ./app:/app
            - /app/node_modules
            - /app/.next
        env_file:
            - ./app/.env
        restart: always
        depends_on:
            - ats-backend
        networks:
            - ATS-NET

    ats-backend:
        build:
            context: ./backend
            dockerfile: Dockerfile
        command: bash -c "python migrate.py migrate && python run.py server"
        image: ats-backend
        container_name: ats-backend
        restart: always
        volumes:
            - ./backend/:/var/www
        ports:
            - "3082:4000"
        healthcheck:
            test:
                [
                    "CMD-SHELL",
                    "curl -f http://localhost:4000/api/v1/health_check || exit 1",
                ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        networks:
            - ATS-NET

    ats-celery:
        build:
            context: ./backend
            dockerfile: Dockerfile
        command: bash -c "python run.py celery"
        image: ats-backend
        container_name: ats-celery
        restart: always
        volumes:
            - ./backend/:/var/www
        networks:
            - ATS-NET

    ats-code-execution:
        build:
            context: ./code-execution
            dockerfile: Dockerfile
        command: bash -c "python run.py"
        image: ats-code-execution
        container_name: ats-code-execution
        restart: always
        volumes:
            - ./code-execution/:/var/www
        networks:
            - ATS-NET
        ports:
            - "3083:4000"

    ats-auth-service:
        build:
            context: ./auth-service
            dockerfile: Dockerfile
        command: bash -c "python run.py"
        image: ats-auth-service
        container_name: ats-auth-service
        restart: always
        volumes:
            - ./auth-service/:/var/www
        networks:
            - ATS-NET
        ports:
            - "3084:4000"

    ats-db:
        image: mysql:9.0.0
        restart: always
        volumes:
            - ats-db:/var/lib/mysql
        container_name: ats-db
        ports:
            - "3304:3306"
        env_file:
            - ./.mysql.env
        networks:
            - ATS-NET

    ats-nginx:
        image: nginx:latest
        ports:
            - "80:80"
        volumes:
            - ./nginxconf:/etc/nginx/conf.d
        container_name: ats_nginx
        networks:
            - ATS-NET

volumes:
    ats-db:

networks:
    ATS-NET:
